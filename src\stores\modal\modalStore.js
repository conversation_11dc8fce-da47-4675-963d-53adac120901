import { create } from 'zustand';
import { getFeatureTitle } from '@/helpers/tier/featureTitles';

const useModalStore = create((set, get) => ({
  showPlans: false,
  showInlinePlans: false,
  currentFeature: null,
  currentFeatureTitle: null,
  inlineCurrentFeature: null,
  inlineCurrentFeatureTitle: null,
  setShowPlans: (show) => set({ showPlans: show }),
  setShowInlinePlans: (show) => set({ showInlinePlans: show }),
  setCurrentFeature: (feature) => set({ currentFeature: feature }),
  setInlineCurrentFeature: (feature) => set({ inlineCurrentFeature: feature }),
  openPlansModal: (feature) => set({
    showPlans: true,
    currentFeature: feature,
    currentFeatureTitle: getFeatureTitle(feature)
  }),
  openInlinePlansModal: (feature) => set({
    showInlinePlans: true,
    inlineCurrentFeature: feature,
    inlineCurrentFeatureTitle: getFeatureTitle(feature) || 'Upgrade your plan to unlock this feature'
  }),
  closePlansModal: () => {
    // First, hide the modal
    set({ showPlans: false });


    setTimeout(() => {
      const currentState = get();
   
      if (!currentState.showPlans) {
        set({
          currentFeature: null,
          currentFeatureTitle: null
        });
      }
    }, 350); 
  },
  closeInlinePlansModal: () => {
    // First, hide the modal
    set({ showInlinePlans: false });

    
    setTimeout(() => {
      const currentState = get();
     
      if (!currentState.showInlinePlans) {
        set({
          inlineCurrentFeature: null,
          inlineCurrentFeatureTitle: null
        });
      }
    }, 350); 
  },
}));

export default useModalStore; 