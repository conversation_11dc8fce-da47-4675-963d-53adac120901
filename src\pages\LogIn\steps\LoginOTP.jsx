import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import * as authService from '@/services/auth.service';
import useToast from '@/hooks/useToast';
import { useUserStore } from '@/stores/user/userStore';
import { getUserInfo, getUserProfile } from '@/services/user.service';
import { Turnstile } from '@marsidev/react-turnstile';

// OTP Input component for verification code
const OTPInput = ({ code, setCode, error, onSubmit, clearError }) => {
  const [inputValues, setInputValues] = useState(Array(6).fill(''));
  const inputRefs = Array(6).fill(0).map(() => useState(null)[0]);

  const handleChange = (index, value) => {
    if (value.match(/^[0-9]$/) || value === '') {
      const newInputValues = [...inputValues];
      newInputValues[index] = value;
      setInputValues(newInputValues);

      // Combine digits and update parent component
      setCode(newInputValues.join(''));

      // Clear error when user starts typing
      if (error && clearError) {
        clearError();
      }

      // Auto-focus next input
      if (value !== '' && index < 5) {
        inputRefs[index + 1].focus();
      }
    }
  };

  const handleKeyDown = (index, e) => {
    // Move to previous input on backspace
    if (e.key === 'Backspace') {
      // Clear error when user starts deleting
      if (error && clearError) {
        clearError();
      }

      if (inputValues[index] === '' && index > 0) {
        inputRefs[index - 1].focus();
      }
    }

    // Handle Enter key to submit form
    if (e.key === 'Enter' && inputValues.every(val => val !== '')) {
      onSubmit();
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    const digits = pastedData.replace(/\D/g, '').split('').slice(0, 6);

    if (digits.length) {
      const newInputValues = [...inputValues];
      digits.forEach((digit, index) => {
        if (index < 6) {
          newInputValues[index] = digit;
        }
      });

      setInputValues(newInputValues);
      setCode(newInputValues.join(''));

      // Clear error when user pastes content
      if (error && clearError) {
        clearError();
      }

      // Focus the next empty input or the last one
      const nextEmptyIndex = newInputValues.findIndex(val => val === '');
      if (nextEmptyIndex !== -1) {
        inputRefs[nextEmptyIndex].focus();
      } else {
        inputRefs[5].focus();
      }
    }
  };

  return (
    <div>
      <div>
        <div className="flex gap-2 justify-center">
          {inputValues.map((value, index) => (
            <input
              key={index}
              ref={el => inputRefs[index] = el}
              type="text"
              inputMode="numeric"
              maxLength={1}
              value={value}
              onChange={(e) => handleChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={index === 0 ? handlePaste : undefined}
              className={`w-11 h-12 text-center text-xl font-medium rounded-lg border ${
                error ? 'border-red-500' : 'border-gray-200'
              } focus:outline-none focus:ring-2 focus:ring-purple-200 text-min-safe-input`}
            />
          ))}
        </div>
        <div className="h-5 mt-1">
          {error && <p className="text-red-500 text-xs text-center leading-none">{error}</p>}
        </div>
      </div>
    </div>
  );
};

const LoginOTP = ({
  email,
  password,
  otpCode,
  setOtpCode,
  goBack,
  loading,
  setLoading,
  error,
  setError
}) => {
  const { addSuccessToast } = useToast();
  const navigate = useNavigate();
  const setUser = useUserStore((state) => state.setUser);
  const saveAuthDetail = useUserStore((state) => state.saveAuthDetail);
  const [resendCountdown, setResendCountdown] = useState(0);
  const [captchaToken, setCaptchaToken] = useState('');
  const [captchaError, setCaptchaError] = useState('');
  const captchaRef = useRef(null);

  // Clear error when component mounts or when code is empty (user went back)
  useEffect(() => {
    if (!otpCode || otpCode.length === 0) {
      setError('');
    }
  }, [otpCode, setError]);

  // Helper functions for CAPTCHA logic
  const isCaptchaEnabled = () => {
    const siteKey = import.meta.env.VITE_APP_TURNSTILE_SITEKEY;
    return siteKey && siteKey !== 'YOUR_SITE_KEY_HERE' && siteKey !== 'DISABLED';
  };

  const isCaptchaSuccessful = () => {
    const enabled = isCaptchaEnabled();
    const hasToken = !!captchaToken;
    return !enabled || hasToken;
  };

  // Function to reset CAPTCHA widget
  const resetCaptcha = () => {
    if (isCaptchaEnabled() && captchaRef.current) {
      try {
        captchaRef.current.reset();
        setCaptchaToken('');
        setCaptchaError('');
      } catch (error) {
        setCaptchaToken('');
        setCaptchaError('');
      }
    }
  };

  const handleVerify = async () => {
    if (otpCode.length !== 6) {
      setError('Please enter all 6 digits of the verification code');
      return;
    }
    if (!isCaptchaSuccessful()) {
      setCaptchaError('Please complete the CAPTCHA');
      return;
    }
    setLoading(true);
    setError('');
    setCaptchaError('');

    try {
      const payload = {
        username: email,
        password: password,
        otp_code: otpCode
      };
      if (isCaptchaEnabled()) {
        payload.captcha_token = captchaToken || '';
      }

      const response = await authService.login(payload);

      if (response && response.status === 200) {
        const auth = { access_token: response.data.access_token };

        saveAuthDetail(auth);

        const { data: userInfo } = await getUserInfo(auth.access_token);
        const userProfile = await getUserProfile();

        saveAuthDetail({ ...auth, ...userInfo, user_id: userInfo.id });
        setUser({ ...userInfo, ...userProfile });

        navigate('/');
      }
    } catch (err) {
      console.error(err);
      setError(
        err.response?.status === 401
          ? 'Invalid verification code'
          : err.response?.data?.detail || 'Error verifying code'
      );
      if (
        err.response?.data?.detail?.toLowerCase().includes('captcha') ||
        err.response?.data?.detail?.toLowerCase().includes('invalid captcha') ||
        err.response?.data?.detail?.toLowerCase().includes('captcha verification failed')
      ) {
        setCaptchaError('CAPTCHA verification failed. Please try again.');
        resetCaptcha();
      }
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (resendCountdown > 0) return;

    try {
      const response = await authService.resendOTP(email);
      if (response.status === 200) {
        addSuccessToast({ message: 'Verification code resent successfully' });

        // Start countdown
        setResendCountdown(52);
        const countdownInterval = setInterval(() => {
          setResendCountdown(prev => {
            if (prev <= 1) {
              clearInterval(countdownInterval);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      }
    } catch (err) {
      console.error(err);
      setError('Failed to resend verification code');
    }
  };

  return (
    <div className="bg-white rounded-t-3xl shadow-lg p-7 mx-2 md:mx-0">
      {/* Back Button */}
      <div className="mb-6">
        <button
          onClick={goBack}
          className="flex items-center text-gray-600 font-medium hover:text-gray-900"
          disabled={loading}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 10H5M5 10L10 15M5 10L10 5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <span className="ml-1">Back</span>
        </button>
      </div>

      {/* Verification Icon */}
      <div className="flex justify-center mb-6">
        <div className="relative">
          <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="40" cy="40" r="30" fill="#F0F0FF"/>
            <path d="M40 25V40L50 50" stroke="#8070F2" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
            <circle cx="40" cy="40" r="20" stroke="#8070F2" strokeWidth="3"/>
          </svg>
        </div>
      </div>

      {/* Header */}
      <div className="text-center mb-7 pt-4">
        <h1 className="text-2xl font-medium mb-2">Two-factor authentication</h1>
        <p className="text-gray-500 text-sm">
          Enter the 6-digit verification code from your authenticator app
        </p>
      </div>

      {/* Verification Code Input */}
      <div className="mb-6">
        <OTPInput
          code={otpCode}
          setCode={setOtpCode}
          error={error}
          onSubmit={handleVerify}
          clearError={() => setError('')}
        />
      </div>
      {/* CAPTCHA - only show if enabled */}
      {isCaptchaEnabled() && (
        <div className="mb-4">
          <Turnstile
            ref={captchaRef}
            siteKey={import.meta.env.VITE_APP_TURNSTILE_SITEKEY}
            options={{
              action: 'login-otp',
              cData: 'login-otp',
              theme: 'light',
              size: 'flexible',
            }}
            onSuccess={(token) => {
              setCaptchaToken(token);
              setCaptchaError('');
            }}
            onError={() => {
              setCaptchaError('CAPTCHA verification failed. Please try again.');
              setCaptchaToken('');
            }}
            onExpire={() => {
              setCaptchaToken('');
              setCaptchaError('CAPTCHA expired. Please verify again.');
            }}
          />
          <div className="h-5 mt-1">
            {captchaError && (
              <p className="text-red-500 text-xs">{captchaError}</p>
            )}
          </div>
        </div>
      )}
      {/* Action Button */}
      <div className="mb-6">
        <button
          onClick={handleVerify}
          className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center hover:bg-indigo-700 transition"
          disabled={loading || !isCaptchaSuccessful()}
        >
          {loading ? (
            <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : null}
          Verify and Log in
        </button>
      </div>

      {/* Resend Link */}
      <div className="text-center">
        <button
          onClick={handleResendCode}
          className={`text-sm ${resendCountdown > 0 ? 'text-gray-400' : 'text-indigo-600 hover:underline'}`}
          disabled={resendCountdown > 0 || loading}
        >
          {resendCountdown > 0
            ? `Didn't receive the code? Resend code (${resendCountdown}s)`
            : 'Didn\'t receive the code? Resend code'}
        </button>
      </div>
    </div>
  );
};

export default LoginOTP;
