// pages/TestPage.jsx
import React, { useState, useEffect, useRef } from "react";

import OnboardingLayout from "@/layouts/OnboardingLayout";
import "./index.css";
import { useNavigate, useLocation } from "react-router-dom";
import OTPInputs from '@/components/OTPInputs';
import { verifyOnboarding, claimOnboarding } from '@/services/onboarding.service';
import { useUserStore } from "@/stores/user/userStore";
import { getUserInfo } from "@/services/user.service";


const CheckmarkIcon = ({ isValid }) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d={isValid
        ? "M12 22.5C14.7848 22.5 17.4555 21.3938 19.4246 19.4246C21.3938 17.4555 22.5 14.7848 22.5 12C22.5 9.21523 21.3938 6.54451 19.4246 4.57538C17.4555 2.60625 14.7848 1.5 12 1.5C9.21523 1.5 6.54451 2.60625 4.57538 4.57538C2.60625 6.54451 1.5 9.21523 1.5 12C1.5 14.7848 2.60625 17.4555 4.57538 19.4246C6.54451 21.3938 9.21523 22.5 12 22.5ZM16.65 9.675C16.7386 9.55681 16.8031 9.42232 16.8398 9.2792C16.8765 9.13609 16.8846 8.98715 16.8637 8.8409C16.8428 8.69465 16.7933 8.55394 16.718 8.42682C16.6428 8.2997 16.5432 8.18864 16.425 8.1C16.3068 8.01136 16.1723 7.94686 16.0292 7.9102C15.8861 7.87353 15.7372 7.86541 15.5909 7.88631C15.4446 7.9072 15.3039 7.9567 15.1768 8.03197C15.0497 8.10724 14.9386 8.20681 14.85 8.325L11.1285 13.287L9.045 11.205C8.83174 11.0063 8.54967 10.8981 8.25822 10.9032C7.96676 10.9084 7.68869 11.0264 7.48257 11.2326C7.27645 11.4387 7.15838 11.7168 7.15324 12.0082C7.1481 12.2997 7.25628 12.5817 7.455 12.795L10.455 15.795C10.5689 15.9089 10.706 15.997 10.8569 16.0534C11.0079 16.1097 11.1692 16.133 11.3299 16.1216C11.4906 16.1102 11.647 16.0645 11.7885 15.9874C11.93 15.9104 12.0533 15.8038 12.15 15.675L16.65 9.675Z"
        : "M20.25 12C20.25 14.188 19.3808 16.2865 17.8336 17.8336C16.2865 19.3808 14.188 20.25 12 20.25C9.81196 20.25 7.71354 19.3808 6.16637 17.8336C4.61919 16.2865 3.75 14.188 3.75 12C3.75 9.81196 4.61919 7.71354 6.16637 6.16637C7.71354 4.61919 9.81196 3.75 12 3.75C14.188 3.75 16.2865 4.61919 17.8336 6.16637C19.3808 7.71354 20.25 9.81196 20.25 12ZM22.5 12C22.5 14.7848 21.3938 17.4555 19.4246 19.4246C17.4555 21.3938 14.7848 22.5 12 22.5C9.21523 22.5 6.54451 21.3938 4.57538 19.4246C2.60625 17.4555 1.5 14.7848 1.5 12C1.5 9.21523 2.60625 6.54451 4.57538 4.57538C6.54451 2.60625 9.21523 1.5 12 1.5C14.7848 1.5 17.4555 2.60625 19.4246 4.57538C21.3938 6.54451 22.5 9.21523 22.5 12ZM16.65 9.675C16.7386 9.55681 16.8031 9.42232 16.8398 9.2792C16.8765 9.13609 16.8846 8.98715 16.8637 8.8409C16.8428 8.69465 16.7933 8.55394 16.718 8.42682C16.6428 8.2997 16.5432 8.18864 16.425 8.1C16.3068 8.01136 16.1723 7.94686 16.0292 7.9102C15.8861 7.87353 15.7372 7.86541 15.5909 7.88631C15.4446 7.9072 15.3039 7.9567 15.1768 8.03197C15.0497 8.10724 14.9386 8.20681 14.85 8.325L11.1285 13.287L9.045 11.205C8.83174 11.0063 8.54967 10.8981 8.25822 10.9032C7.96676 10.9084 7.68869 11.0264 7.48257 11.2326C7.27645 11.4387 7.15838 11.7168 7.15324 12.0082C7.1481 12.2997 7.25628 12.5817 7.455 12.795L10.455 15.795C10.5689 15.9089 10.706 15.997 10.8569 16.0534C11.0079 16.1097 11.1692 16.133 11.3299 16.1216C11.4906 16.1102 11.647 16.0645 11.7885 15.9874C11.93 15.9104 12.0533 15.8038 12.15 15.675L16.65 9.675Z"
      }
      fill={isValid ? "#06BA63" : "#D1CFE2"}
    />
  </svg>
)



export default function AccountVerification() {
  const location = useLocation();
  const navigate = useNavigate();
  const { user_id, email, kb_id, chatbot_token, role, url, marketingConsent } = location.state || {};
  const saveAuthDetail = useUserStore((state) => state.saveAuthDetail);
  const setUser = useUserStore((state) => state.setUser);

  // Individual validation functions for each requirement
  const checkMinLength = (password) => password && password.length >= 8;
  const checkUppercase = (password) => password && /[A-Z]/.test(password);
  const checkLowercase = (password) => password && /[a-z]/.test(password);
  const checkSpecialChar = (password) => password && /[^a-zA-Z0-9]/.test(password);

  const testimonials = [{
    id: 1,
    description: 'At least 8 characters',
    validator: checkMinLength
  },
  {
    id: 2,
    description: 'Uppercase letter',
    validator: checkUppercase
  },
  {
    id: 3,
    description: 'Lowercase letter',
    validator: checkLowercase
  },
  {
    id: 4,
    description: 'Special character (!@#$%^&*)',
    validator: checkSpecialChar
  }];

  const [verificationCode, setVerificationCode] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [resendLoading, setResendLoading] = useState(false);

  const validatePassword = (password) => {
    if (!password) return 'Password is required';
    if (password.length < 8) return 'Password must be at least 8 characters long';
    if (!/[a-z]/i.test(password)) return 'Password must include at least one letter';
    if (!/[0-9]/.test(password)) return 'Password must include at least one number';
    if (!/[^a-zA-Z0-9]/.test(password)) return 'Password must include at least one symbol';
    return '';
  };

  // Clear error when inputs change
  const handlePasswordChange = (e) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    if (passwordError) {
      setPasswordError('');
    }
  };

  const handleVerificationCodeChange = (code) => {
    setVerificationCode(code);
    if (error) {
      setError('');
    }
  };

  const handleVerify = async () => {
    
    setError("");
    setPasswordError("");

    let hasErrors = false;

    // Validate verification code
    if (!verificationCode || verificationCode.trim() === '') {
      setError('Verification code is required');
      hasErrors = true;
    } else if (verificationCode.length !== 6) {
      setError('Please enter all 6 digits of the verification code');
      hasErrors = true;
    }

    // Validate password
    if (!password || password.trim() === '') {
      setPasswordError('Password is required');
      hasErrors = true;
    } else {
      const passwordValidationError = validatePassword(password);
      if (passwordValidationError) {
        setPasswordError(passwordValidationError);
        hasErrors = true;
      }
    }

   
    if (hasErrors) {
      return;
    }

   
    setLoading(true);
    try {
      const response = await verifyOnboarding(user_id, verificationCode, password, role, url, marketingConsent);
      if(response.status === 200){
        const auth = { access_token: response.data.access_token };
        saveAuthDetail(auth);
        const userInfo = await getUserInfo(response.data.access_token);
        setUser(userInfo);
        if (kb_id) {
          navigate(`/chatbot/${kb_id}?first_time=true`);
        } else {
          navigate('/');
        }
      }
      // Optionally navigate or show success
    } catch (err) {
      console.error('Verification failed:', err);
      console.error('Backend error response:', err.response?.data);
      const errorMessage = err.response?.data?.detail[0]?.msg || err.response?.data?.detail ||
                          err.response?.data?.message ||
                          err.message ||
                          'Verification failed. Please try again.';
      console.error('Final error message:', errorMessage);

      // Route errors to appropriate error state based on content
      const lowerErrorMessage = errorMessage?.toLowerCase();
      if (lowerErrorMessage.includes('password')) {
        setPasswordError(errorMessage);
        setError(''); // Clear verification code error
      } else {
        // For all other errors (including verification code errors), show in verification code error area
        setError(errorMessage);
        setPasswordError(''); // Clear password error
      }
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    setResendLoading(true);
    setError("");
    try {
      const response = await claimOnboarding(email, user_id);
      if (response.status === 200) {
       
        setError(""); 
      }
    } catch (err) {
      setError("Failed to resend code. Please try again.");
    } finally {
      setResendLoading(false);
    }
  };

  const handleChangeEmail = async () => {
    //send them back to the account creation page with email and user_id
    navigate('/create-account/claim', {
      state: {
        email: email,
        user_id: user_id,
        kb_id: kb_id,
        chatbot_token: chatbot_token,
      }
    })
  };

  return (
       <OnboardingLayout
      containerClassName="min-h-screen overflow-x-hidden "
      navClassName="max-w-[75rem] mx-auto lg:px-[20px] px-2"
    >
      <div className="flex flex-col lg:flex-row  items-center lg:min-h-[100vh] mt-[128px] lg:mt-0">
        <main className="relative z-10 flex items-center px-[20px]  pb-[40px] lg:pb-0 md:pb-0 w-full">
        <div className="flex flex-col md:flex-col lg:flex-row justify-center items-center lg:max-w-[75rem] w-full mx-auto gap-[40px] md:gap-[80px] h-full ">
            <div className="flex flex-col gap-[24px] md:gap-[40px] lg:gap-[48px] items-center justify-center flex-1 lg:max-w-[530px]">
                <div className="flex flex-col gap-[16px] md:gap-[23px] w-full ">
                    <h1 className="text-[28px] font-sora leading-[140%] font-bold tracking-[-0.56px]">Check your email!</h1>
                    <p className="text-sm md:text-base leading-[160%] font-inter">
                      We sent a 6-digit code to <b>{email}</b>.
                    </p>
                </div>
                <div className="flex flex-col gap-[24px] w-full">
                    <div className="flex flex-col w-full gap-2">
                        <p className="text-sm md:text-base leading-[160%] font-inter text-grey-75">Verification code:</p>
                        <OTPInputs length={6} newDesign={true} onChange={handleVerificationCodeChange} error={error} />
                    </div>
                    <div className="flex flex-col gap-2">
                      <div className="flex flex-col gap-2">
                          <p className="text-sm md:text-base leading-[160%] font-inter text-grey-75">Create password:</p>
                          <input
                              type="password"
                              className={`
                                  flex
                                  h-14
                                  px-4
                                  py-2.5
                                  items-center
                                  gap-2.5
                                  self-stretch
                                  rounded-lg
                                  border
                                  ${passwordError ? 'border-[#DF0F32] focus:border-[#DF0F32]' : 'border-[#D1CFE2] focus:border-[#5C5A6E] '}}
                                  bg-white
                                  w-full
                                  focus:outline-none
                                  placeholder:text-black
                                  transition-colors
                                  duration-200
                               
                              `}
                              value={password}
                              onChange={handlePasswordChange}
                              onKeyDown={e => {
                                if (e.key === 'Enter') {
                                  handleVerify();
                                }
                              }}
                              disabled={loading}
                          />
                          <div className="h-4">
                            {passwordError && (
                              <p className="text-[#DF0F32] text-sm font-[Inter]">{passwordError}</p>
                            )}
                          </div>
                      </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-y-[16px] ">
                      {testimonials.map((testimonial) => (
                          <div key={testimonial.id} className="flex gap-[8px] items-center">
                              <CheckmarkIcon isValid={testimonial.validator(password)} />
                              <p className="text-base leading-[160%] font-inter">{testimonial.description}</p>
                          </div>
                      ))}
                    </div>
                   </div>
                </div>
                <button
                    className="
                        flex
                        h-14
                        px-6
                        py-2.5
                        flex-col
                        justify-center
                        items-center
                        self-stretch
                        rounded-lg
                        border-2
                        border-[#2B13FB]
                        text-base
                        text-white
                        leading-[150%]
                        font-[Inter]
                        font-medium
                        transition-all
                        duration-200
                        bg-[#5642FF] hover:bg-[#3F29FF] shadow-custom-glow
                    "
                    onClick={handleVerify}
                    disabled={loading}
                    >
                    {loading ? 'Verifying...' : 'Complete setup'}
                </button>
                <div className="flex flex-col gap-4 w-full">
                    <div className="flex items-center justify-between">
                        <p className="text-sm md:text-base leading-[160%] font-inter">Didn’t receive the code?</p>
                        <button
                            className="text-sm md:text-base leading-[160%] font-inter text-purple-300 font-bold disabled:opacity-50"
                            onClick={handleResendCode}
                            disabled={resendLoading}
                        >
                            {resendLoading ? 'Sending...' : 'Resend code'}
                        </button>
                    </div>
                    <div className="flex items-center justify-between ">
                        <p className="text-sm md:text-base leading-[160%] font-inter">Found a typo in email?</p>
                        <button className="text-sm md:text-base leading-[160%] font-inter text-purple-300 font-bold" onClick={handleChangeEmail}>Change email</button>
                    </div>
                </div>
            </div>
        </div>


      </main>
      </div>
    </OnboardingLayout>
  );
}
