import { useEffect, useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';

import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';
import * as teamManagementService from '@/services/teamManagement.service';
import useLayoutStore from '@/stores/layout/layoutStore';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import { useUserStore } from '@/stores/user/userStore';
import {
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  Transition,
} from '@headlessui/react';

import DModalCreateNew from '../DModalCreateNew';
import DNotificationHeader from '../DNotificationHeader';
import AiVoiceIcon from '../Global/Icons/AiVoiceIcon';
import DButtonIcon from '../Global/DButtonIcon';
import DFullLogo from '../Global/DLogo/DFullLogo';
import DNavLink from '../Global/DNavLink';
import ActivityIcon from '../Global/Icons/ActivityIcon';
import AddIcon from '../Global/Icons/AddIcon';
import AiAvatarIcon from '../Global/Icons/AiAvatarIcon';
import AiChatbotIcon from '../Global/Icons/AiChatbotIcon';
import BookIcon from '../Global/Icons/BookIcon';
import ChevronDownIcon from '../Global/Icons/ChevronDownIcon';
import ClockIcon from '../Global/Icons/ClockIcon';
import CloseIcon from '../Global/Icons/CloseIcon';
import CloseNavIcon from '../Global/Icons/CloseNavIcon';
import ExpandIcon from '../Global/Icons/ExpandIcon';
import GraphIcon from '../Global/Icons/GraphIcon';
import LeaveTeamIcon from '../Global/Icons/LeaveTeamIcon';
import LiveAgentIcon from '../Global/Icons/LiveAgentIcon';
import NotificationIcon from '../Global/Icons/NotificationIcon';
import OpenNavIcon from '../Global/Icons/OpenNavIcon';
import OptionsHorizontalIcon from '../Global/Icons/OptionsHorizontalIcon';
import HeaderAccountMenu from '../HeaderAccountMenu';
import './index.css';
import PhoneIcon from '../Global/Icons/PhoneIcon';
import DModalBetaVoice from '../DModalBetaVoice';
import DTransition from '../Global/DTransition';
import DCircularChart from '../Global/DCircularChart';
import DModalBetaAvatar from '../DModalBetaAvatar';
import featureCheck, {
  checkTeamManagementPermission,
  checkFeatureAvailability,
} from '@/helpers/tier/featureCheck';
import useToast from '@/hooks/useToast';
import AvatarIcon from '../Global/Icons/AvatarIcon';
import * as userService from '@/services/user.service';
import clsx from 'clsx';
import DTooltip from '../Global/DTooltip';
import InfoIcon from '../Global/Icons/InfoIcon';
import DButton from '../Global/DButton';
import ZapIcon from '../Global/Icons/ZapIcon';
import TeamWorkspaceIcon from '../Global/Icons/TeamWorkspaceIcon';
import { useCreateChatbotStore } from '@/stores/chatbot/createChatbotStore';
import DModalPlans from '@/components/DModalPlans';
import UnlockIcon from '../Global/Icons/UnlockIcon';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import HeaderLearningHub from '../HeaderLearningHub';
import NewBadge from '../Global/NewBadge';
import { formatRemainingCredits } from '@/utils/formatCredits';

const MainNav = ({ hideCollapsedButton = false, closeMobileNav }) => {
  // Store references
  const sidebarOpen = useLayoutStore((state) => state.sidebarOpen);
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setUser = useUserStore((state) => state.setUser);
  const userData = useUserStore((state) => state.user);

  const {
    owner,
    userMode,
    selectedTeam,
    setSelectedTeam,
    setUserMode,
    setTeamName,
    teamName,
    setTeams,
    setOwner,
    teams,
  } = useTeamManagementStore((state) => state);

  const location = useLocation();
  const navigate = useNavigate();
  const { addWarningToast } = useToast();
  const isAboveSm = useIsAboveBreakpoint('sm');

  const [isCollapsed, setIsCollapsed] = useState(sidebarOpen || true);
  const [dropdownOpened, setDropdownOpened] = useState(false);
  const [teamDropdownOpened, setTeamDropdownOpened] = useState(false);
  const [hasMounted, setHasMounted] = useState(false);

  const [openModalCreateNew, setOpenModalCreateNew] = useState(false);
  const [openAIVoiceModal, setOpenAIVoiceModal] = useState(false);
  const [openAiAvatarModal, setOpenAiAvatarModal] = useState(false);
  const resetChatbotData = useCreateChatbotStore(
    (state) => state.resetChatbotData
  );
  const chatbots = useChatbotStore((state) => state.chatbots);
  const TRIAL_DAYS =
    userData?.new_design_trial && userData?.has_created_chatbot ? 14 : 7;

  const [isPlanModalOpen, setIsPlanModalOpen] = useState(false);

  // Nav links
  const initialNavLinks = [
    {
      label: 'Create New',
      icon: <AddIcon />,
      iconPlacement: 'pre',
      href: '',
      active: location.pathname.startsWith('/chatbot/create'),
      activeFunction: () => {
        return location.pathname.startsWith('/chatbot/create');
      },
      onClick: () => {
        resetChatbotData();
        navigate('/chatbot/create');
        closeMobileNav && closeMobileNav();
      },
    },
    {
      label: 'AI Chatbots',
      icon: <AiChatbotIcon />,
      iconPlacement: 'pre',
      active:
        (location.pathname === '/' ||
          location.pathname.startsWith('/chatbot')) &&
        !location.pathname.startsWith('/chatbot/create'),
      activeFunction: () => {
        return (
          location.pathname === '/' ||
          (location.pathname.startsWith('/chatbot') &&
            !location.pathname.startsWith('/chatbot/create'))
        );
      },
      href: '/',
      onClick: () => {
        navigate('/');
        closeMobileNav && closeMobileNav();
      },
      submenu: [
        {
          label: 'Human Handover',
          icon: <LiveAgentIcon />,
          iconPlacement: 'pre',
          href: '/human-handover',
          active: location.pathname.startsWith('/human-handover'),
          activeFunction: () => {
            return location.pathname.startsWith('/human-handover');
          },
          onClick: (e) => {
            e.preventDefault();
            navigate('/human-handover');
            closeMobileNav && closeMobileNav();
          },
          showIfNoTeam: true,
        },
      ],
      submenuOpen: false,
      submenuFunction: () => {
        return (
          location.pathname.startsWith('/chatbot') ||
          location.pathname.startsWith('/human-handover')
        );
      },
    },
    // {
    //   label: 'AI Digital Personas',
    //   icon: <AiAvatarIcon />,
    //   iconPlacement: 'pre',
    //   href: '',
    //   active: openAiAvatarModal,
    //   activeFunction: () => {
    //     return openAiAvatarModal;
    //   },
    //   onClick: () => {
    //     setOpenAiAvatarModal(true);
    //     closeMobileNav && closeMobileNav();
    //   },
    //   showIfNoTeam: true,
    // },
    {
      label: 'AI Voice Agents',
      icon: isCollapsed ? <AiVoiceIcon /> : <NewBadge className='w-full !h-3' textSize="text-[8px]" />,
      iconPlacement: 'pre',
      href: '/voice',
      active: openAIVoiceModal,
      activeFunction: () => {
        return location.pathname.startsWith('/voice');
      },
      onClick: () => {
        navigate('/voice');
        closeMobileNav && closeMobileNav();
      },
      submenu:[
          {
            label: 'Phone Numbers',
            icon: <PhoneIcon />,
            iconPlacement: 'pre',
            href: '/phone-numbers',
            active: location.pathname.startsWith('/phone-numbers'),
            activeFunction: () => {
              return location.pathname.startsWith('/phone-numbers');
            },
            onClick: (e) => {
              e.preventDefault();
              navigate('/phone-numbers');
              closeMobileNav && closeMobileNav();
            },
          },
        ],
      submenuOpen: false,
      submenuFunction: () => {
        return location.pathname.startsWith('/phone-numbers');
      },
      showIfNoTeam: true,
    },
  ];
  const [navLinks, setNavLinks] = useState(initialNavLinks);

  const fetchUserProfile = async (signal) => {
    try {
      const response = await userService.getUserProfile({ signal });
      if (response.status === 200) {
        setUser(response.data);
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('Fetch aborted');
      } else {
        console.log('error', error);
      }
    }
  };

  // Effects
  useEffect(() => {
    if (window.innerWidth < 768) {
      setSidebarOpen(true);
    }
  }, [window.innerWidth]);

  useEffect(() => {
    getTeams();
  }, [teamName]);

  useEffect(() => {
    setIsCollapsed(!sidebarOpen);
  }, [sidebarOpen]);

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    fetchUserProfile(signal);
    return () => {
      controller.abort();
    };
  }, [selectedTeam]);

  const getTeams = async () => {
    const response = await teamManagementService.getTeams();
    setOwner(
      response.data.results.find((team) => team.owner_id === userData.id)
    );
    setTeams(response.data.results);
  };

  // Toggle the entire sidebar's collapse/expand
  const handleToggleNav = () => {
    setIsCollapsed(!isCollapsed);
    setSidebarOpen(!sidebarOpen);
  };

  // Submenu toggling
  const toggleSubmenu = (label) => {
    setNavLinks((prev) =>
      prev.map((item) =>
        item.label === label
          ? { ...item, submenuOpen: !item.submenuOpen }
          : item
      )
    );
  };

  // Team management
  const handleTeamManagementClick = async () => {
    setTeamDropdownOpened(false);

    // Check if user has access to team management feature
    if (!featureCheck('team_management')) {
      return; // featureCheck will show the upgrade modal
    }

    try {
      if (
        teams.length === 0 ||
        !teams.some((team) => team.owner_id === userData?.id)
      ) {
        const response = await teamManagementService.createTeam(
          `${userData.full_name}'s team`
        );
        if (response.status === 200) {
          setSelectedTeam(response.data);
          getTeams();
          fetchUserProfile();
          navigate(`/team-management/${response.data.id}`);
        }
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const handleTeamClick = async (e, team) => {
    e.stopPropagation();

    // Check if user has access to team management feature
    if (!featureCheck('team_management')) {
      return; // featureCheck will show the upgrade modal
    }

    setUserMode(false);
    setSelectedTeam(team);
    setTeamName(team.name);
    navigate(`/team-management/${team.id}`);
    closeMobileNav && closeMobileNav();
  };

  const handleLeaveTeam = async (id) => {
    try {
      const response = await teamManagementService.leaveTeam(id);
      if (response.status === 200) {
        setSelectedTeam(null);
        setUserMode(true);
        setTeamDropdownOpened(false);
        navigate('/');
      }
    } catch (e) {
      console.log('error', e);
    }
  };

  const updateNavLinks = () => {
    return initialNavLinks
      .filter((navLink) => {
        if (navLink.showIfNoTeam && selectedTeam) {
          return false;
        }
        if (navLink.label === 'Create New' && selectedTeam) {
          return checkTeamManagementPermission('create_chatbot');
        }
        return true;
      })
      .map((navLink) => {
        const updatedLink = {
          ...navLink,
          active: navLink.activeFunction(),
        };

        if (navLink.submenu) {
          updatedLink.submenu = navLink.submenu
            .filter((subNav) => {
              if (subNav.showIfNoTeam && selectedTeam) {
                return false;
              }
              return true;
            })
            .map((subNav) => ({
              ...subNav,
              active: subNav.activeFunction ? subNav.activeFunction() : false,
            }));

          updatedLink.submenuOpen =
            typeof navLink.submenuFunction === 'function'
              ? navLink.submenuFunction() && !selectedTeam
              : false;
        }

        return updatedLink;
      });
  };

  // useEffect for updating navLinks based on state changes
  useEffect(() => {
    setNavLinks(updateNavLinks());
  }, [
    location.pathname,
    openModalCreateNew,
    openAIVoiceModal,
    openAiAvatarModal,
    userData,
    isCollapsed
  ]);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  return (
    <aside className="h-full flex flex-col">
      <div
        className={clsx(
          'main_nav_wrapper bg-white px-size1 py-size3 xl:p-size3',
          'overflow-hidden',
          hasMounted && 'transition-all duration-300',
          isCollapsed ? 'w-20' : 'w-[220px]'
        )}
      >
        <div
          className={`flex flex-col w-full ${
            (dropdownOpened || teamDropdownOpened) && 'dropdown_opened'
          }`}
        >
          {/* Header (logo + collapse button + maybe user menu on mobile) */}
          <div
            className={`flex items-center ${
              isCollapsed ? 'justify-between' : 'justify-between'
            } gap-1 text-base w-full`}
          >
            {/* Logo (hide or shrink when collapsed) */}
            {!isCollapsed && (
              <button
                className="dbutton overflow-hidden transition-all"
                onClick={() => navigate('/')}
              >
                <DFullLogo />
              </button>
            )}

            {/* Collapse button */}
            {!hideCollapsedButton && (
              <div
                className={clsx(
                  'dbutton transition-all flex justify-center items-center',
                  {
                    'w-full': isCollapsed,
                  }
                )}
              >
                <button onClick={handleToggleNav} className="ml-[5px]">
                  {isCollapsed ? <OpenNavIcon /> : <CloseNavIcon />}
                </button>
              </div>
            )}

            {/* Mobile only stuff */}
            {!isAboveSm && (
              <div className="flex gap-size2 items-center justify-end">
                <DNotificationHeader />
                <HeaderLearningHub />
                <HeaderAccountMenu closeMobileNav={closeMobileNav} />
                <DButtonIcon
                  size="lg"
                  onClick={closeMobileNav}
                  className="border border-grey-5 rounded-size0"
                >
                  <CloseIcon className="w-6 h-6" />
                </DButtonIcon>
              </div>
            )}
          </div>

          {/* Divider */}
          <div className="border border-grey-5 w-full my-size4"></div>

          {/* NAVIGATION LINKS */}
          <div className="main_nav-nav_links w-full flex flex-col gap-size2">
            {navLinks.map((navLink, index) => (
              <div
                key={navLink.label}
                className={`relative nav-item-${navLink.label
                  .replace(/\s+/g, '-')
                  .toLowerCase()}`}
              >
                <DNavLink
                  label={isCollapsed ? '' : navLink.label}
                  icon={navLink.icon}
                  iconPlacement={navLink.iconPlacement}
                  active={navLink.active}
                  collapsed={isCollapsed}
                  onClick={() => {
                    if (navLink?.submenu && navLink.submenu.length > 0) {
                      toggleSubmenu(navLink.label);
                      navLink.onClick?.();
                    } else {
                      navLink.onClick?.();
                    }
                  }}
                  className={`flex items-center w-full ${
                    navLink?.submenu && navLink.submenu.length > 0
                      ? 'cursor-pointer'
                      : ''
                  }`}
                >
                  {!isCollapsed && navLink?.submenu?.length > 0 && (
                    <span
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleSubmenu(navLink.label);
                      }}
                    >
                      <ChevronDownIcon
                        className={`w-3 h-3 ${
                          navLink.submenuOpen ? 'rotate-180' : ''
                        }`}
                      />
                    </span>
                  )}
                </DNavLink>

                {/* Submenu transition */}
                <DTransition
                  show={navLink.submenu && navLink.submenuOpen}
                  className="mt-size2"
                  type="collapse"
                >
                  {navLink?.submenu?.map((subNav) => (
                    <DNavLink
                      key={subNav.label}
                      label={isCollapsed ? '' : subNav.label}
                      icon={subNav.icon}
                      iconPlacement={subNav.iconPlacement}
                      active={subNav.active}
                      onClick={subNav.onClick}
                      className={
                        subNav.label === 'Human Handover'
                          ? 'human-handover-nav-item'
                          : ''
                      }
                    />
                  ))}
                </DTransition>

                {/* Example divider after certain item */}
                {index === 4 && (
                  <div className="h-px w-full bg-gray-300 mt-2"></div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/*
          USAGE BARS AND TRIAL DAYS
          We put them above the team management (still near bottom).
        */}
        <div className="flex flex-col w-full gap-size3 mt-size4">
          {/* Account usage section */}
          <div
            className={`flex flex-col w-full gap-size2 relative ${
              teamDropdownOpened && 'dropdown_opened'
            }`}
          >
            {/*
              If collapsed, we might just show DCircularChart
              Otherwise, show full usage bars
            */}
            {isCollapsed ? (
              <div className="flex justify-center items-center">
                <DCircularChart
                  value={userData?.credits_key?.percentage}
                  size="sm"
                />
              </div>
            ) : (
              <div className="flex flex-col gap-size2 sidebar-usage-section">
                {userData &&
                  userData?.tier_on_trial &&
                  userData?.new_design_trial &&
                  !selectedTeam &&
                  chatbots?.length === 0 && (
                    <div
                      className="flex items-center gap-size1 text-white p-size1 rounded-size1"
                      style={{
                        background:
                          'linear-gradient(87deg, rgb(81 99 255) -15.34%, rgb(107 98 251) 21.77%, rgb(157 126 255) 83.94%) 0% 0% / 123.26% 100% border-box border-box',
                      }}
                    >
                      {/* <UnlockIcon className='size-7 text-purple-200'/> */}
                      <p className="text-xs tracking-tight leading-[16px]">
                        Create your first AI Chatbot for an additional 7 trial
                        days
                      </p>
                    </div>
                  )}
                {userData &&
                  userData?.tier_on_trial &&
                  userData?.new_design_trial &&
                  !selectedTeam && (
                    <div className="flex flex-col gap-size1 sidebar-trial-days">
                      <div className="flex w-full items-center justify-between gap-size1">
                        <p className="text-sm tracking-tight text-grey-50">
                          <span className="text-black">
                            {userData?.new_design_trial_days_left}
                          </span>{' '}
                          trial days left
                        </p>
                        {/* {userData?.tier_on_trial && !userData?.has_created_chatbot && <DTooltip
                        content={<div  className='flex flex-col gap-size1 p-size1'>
                          <p>Unlock 7 additional free trial <br /> days by creating an AI Chatbot</p>
                          <DButton variant='contained' fullWidth onClick={() => navigate('/chatbot/create')}>
                            Create AI Chatbot
                          </DButton>
                        </div>}
                      >
                        <InfoIcon className="w-4 h-4 text-grey-50" />
                      </DTooltip>} */}
                      </div>
                      <DButton
                        variant="green"
                        className="!gap-size0"
                        onClick={() => setIsPlanModalOpen(true)}
                        fullWidth
                      >
                        <ZapIcon />
                        <span className="mr-size0">Add plan</span>
                      </DButton>
                      {/* <div className="flex space-x-1">
                      {[...Array(TRIAL_DAYS)].map((_, index) => {
                        const isFilled = (Math.min(
                          (userData?.new_design_trial_days_left ),
                          TRIAL_DAYS
                        )) > index;
                        return (
                          <div
                            key={index}
                            className={`h-1 w-full rounded ${
                              isFilled ? 'bg-negative-100' : 'bg-negative-10'
                            }`}
                          ></div>
                        );
                      })}
                    </div> */}
                    </div>
                  )}
                {/* Minutes left */}
                
                {/* {userData?.credits_key?.ai_voice_minutes_used != null &&
                  userData?.credits_key?.ai_voice_minutes_available != null && (
                    <div className="flex flex-col gap-size1 sidebar-voice-minutes">
                      <div className="flex w-full items-center justify-between gap-size1">
                        <p className="text-sm tracking-tight text-grey-50">
                          <span className="text-black">
                            {Math.round(
                              userData.credits_key.ai_voice_minutes_available -
                                userData.credits_key.ai_voice_minutes_used
                            )}
                          </span>{' '}
                          AI Voice Agent minutes.{' '}
                          <a
                            className="text-black underline font-medium"
                            href="https://www.dante-ai.com/guides/how-to-create-an-ai-voice-agent"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            Learn more
                          </a>
                        </p>
                      </div>
                      <div className="w-full bg-green-10 h-0.5">
                        <div
                          className="bg-green-400 h-0.5"
                          style={{
                            width: `${Math.ceil(
                              userData.credits_key.percentage
                            )}%`,
                          }}
                        ></div>
                      </div>
                    </div>
                  )} */}

                {/* Credits left */}
                {userData?.credits_key?.credits_available != null &&
                  userData?.credits_key?.credits_used != null && (
                    <div className="flex flex-col gap-size1 sidebar-credits">
                      <div className="flex w-full items-center justify-between gap-size1">
                        <p className="text-sm tracking-tight text-grey-50">
                          <span className="text-black">
                            {formatRemainingCredits(
                              userData.credits_key.credits_available,
                              userData.credits_key.credits_used
                            )}
                          </span>{' '}
                          credits
                        </p>
                      </div>
                      <div className="w-full bg-green-10 h-0.5">
                        <div
                          className="bg-green-400 h-0.5"
                          style={{
                            width: `${Math.ceil(
                              userData.credits_key.percentage
                            )}%`,
                          }}
                        ></div>
                      </div>
                    </div>
                  )}
                {userData &&
                  userData?.tier_on_trial &&
                  !userData?.new_design_trial &&
                  !selectedTeam &&
                  userData?.trial_key?.trial_days_left > 0 && (
                    <div className="flex flex-col gap-size1 sidebar-old-trial">
                      <div className="flex w-full items-center justify-between gap-size1">
                        <p className="text-sm tracking-tight text-grey-50">
                          <span className="text-black">
                            {userData?.trial_key?.trial_days_left}
                          </span>{' '}
                          trial days left
                        </p>
                      </div>
                      <div className="flex space-x-1">
                        {[...Array(14)].map((_, index) => {
                          const isFilled =
                            Math.min(userData?.trial_key?.trial_days_left, 14) >
                            index;
                          return (
                            <div
                              key={index}
                              className={`h-1 w-full rounded ${
                                isFilled ? 'bg-negative-100' : 'bg-negative-10'
                              }`}
                            ></div>
                          );
                        })}
                      </div>
                    </div>
                  )}

                {/* AI Chatbots used */}
                {userData &&
                  userData?.used_ai_chatbots >= 0 &&
                  !selectedTeam && (
                    <div className="flex flex-col gap-size1 sidebar-chatbots">
                      <div className="flex w-full items-center justify-between gap-size1">
                        <p className="text-sm tracking-tight text-grey-50">
                          <span className="text-black">
                            {userData?.used_ai_chatbots}
                          </span>
                          /{userData?.total_ai_chatbots} AI Chatbots
                        </p>
                        {userData?.total_ai_chatbots <
                          userData?.used_ai_chatbots && (
                          <DTooltip content="Your chatbots exceed the trial limit and will be deleted.">
                            <InfoIcon className="size-3 text-grey-50" />
                          </DTooltip>
                        )}
                      </div>
                      <div className="w-full bg-purple-10 h-0.5">
                        <div
                          className="bg-purple-200 h-0.5 max-w-[100%]"
                          style={{
                            width: `${
                              (userData?.used_ai_chatbots /
                                userData?.total_ai_chatbots) *
                              100
                            }%`,
                          }}
                        ></div>
                      </div>
                    </div>
                  )}
              </div>
            )}
          </div>

          {/* Divider */}
          <div className="border border-grey-5 w-full"></div>

          {/* TEAM MANAGEMENT - pinned at bottom */}
        
            <div
              className={`main_nav-team_management relative w-full justify-center items-center flex ${
                dropdownOpened && 'dropdown_opened'
              }`}
            >
            <Menu>
              {({ open }) => {
                if (teamDropdownOpened !== open) {
                  setTeamDropdownOpened(open);
                }
                return (
                  <>
                    <MenuButton
                      className={`flex items-center w-full rounded-size3 hover:bg-grey-2 ${
                        !isCollapsed ? '' : 'justify-center'
                      }`}
                    >
                      <div
                        className={`flex items-center gap-size1 text-base font-regular ${
                          isCollapsed ? 'm-auto' : ''
                        }`}
                      >
                        <TeamWorkspaceIcon className="size-6" />
                        {/* <img
                          src={
                            selectedTeam?.team_icon ??
                            'https://dante-ai-files.s3.eu-west-1.amazonaws.com/profile-images/team/team-01-small.png'
                          }
                          alt="Team Management Avatar"
                          className="size-10"
                        /> */}
                        {!isCollapsed && (
                          <span className="text-sm truncate w-[150px] block text-left">
                            {selectedTeam
                              ? `${selectedTeam?.name}`
                              : 'Team Workspace'}
                          </span>
                        )}
                      </div>
                      {!isCollapsed && <div> <ExpandIcon className="text-grey-50 !h-4 !w-4" /> </div>}
                    </MenuButton>

                    <MenuItems
                      anchor={isCollapsed ? 'right end' : 'top'}
                      className="border border-grey-5 bg-white rounded-size2 w-[210px] shadow-md p-size1 flex flex-col gap-size1"
                    >
                      {!userMode && (
                        <MenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedTeam(null);
                            setUserMode(true);
                            setTeamDropdownOpened(false);
                            navigate('/');
                            closeMobileNav && closeMobileNav();
                          }}
                          className="cursor-pointer"
                        >
                          <div className="flex items-center gap-size1 cursor-pointer">
                            <AvatarIcon className="size-5" />
                            <span className="text-sm font-regular">
                              Switch to user mode
                            </span>
                          </div>
                        </MenuItem>
                      )}
                      {teams.map((team) => (
                        <MenuItem
                          key={team.id}
                          onClick={(e) => handleTeamClick(e, team)}
                        >
                          <a
                            className="flex justify-between gap-size0 items-center hover:bg-grey-2 text-black"
                            href="#!"
                          >
                            <div className="flex items-center gap-size1 text-sm font-regular justify-between">
                              {/* <img
                                src={
                                  team?.team_icon ??
                                  'https://dante-ai-files.s3.eu-west-1.amazonaws.com/profile-images/team/team-02-small.png'
                                }
                                alt="Team Management Avatar"
                                className="w-8 h-8"
                              /> */}
                              <span className={'truncate max-w-full'}>
                                {team.name}
                              </span>
                            </div>
                            <span className="flex items-center justify-center h-4 text-black bg-grey-5 py-o.5 px-size0 rounded-size0 text-[8px] font-bold w-[40px]">
                              {owner?.owner_id === team.owner_id
                                ? 'Owner'
                                : 'Guest'}
                            </span>
                          </a>
                        </MenuItem>
                      ))}
                      {(teams.length === 0 ||
                        !teams.some(
                          (team) => team.owner_id === userData?.id
                        )) && (
                        <span
                          className="text-sm font-regular cursor-pointer"
                          onClick={handleTeamManagementClick}
                        >
                          Create team
                        </span>
                      )}
                    </MenuItems>
                  </>
                );
              }}
            </Menu>
          </div>
        
        </div>
      </div>

      {/* Modals */}
      <DModalCreateNew
        open={openModalCreateNew}
        onClose={() => {
          setOpenModalCreateNew(false);
          closeMobileNav && closeMobileNav();
        }}
        handleOpenModalBetaVoice={() => {
          if (userData?.credits_key?.ai_voice_minutes_available != null) {
            navigate('/voice');
          } else {
            setOpenAIVoiceModal(true);
          }
        }}
        handleOpenModalBetaAvatar={() => {
          setOpenAiAvatarModal(true);
        }}
      />
      <DModalBetaVoice
        open={openAIVoiceModal}
        onClose={() => {
          setOpenAIVoiceModal(false);
        }}
      />
      <DModalBetaAvatar
        open={openAiAvatarModal}
        onClose={() => {
          setOpenAiAvatarModal(false);
        }}
      />
      <DModalPlans
        isOpen={isPlanModalOpen}
        onClose={() => setIsPlanModalOpen(false)}
      />
    </aside>
  );
};

export default MainNav;
