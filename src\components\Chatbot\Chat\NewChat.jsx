import React, { useState, useCallback, useRef, useEffect } from 'react';
import NewChatListMessages from '../ChatListMessages/NewChatListMessages';
import MessageInput from '@/components/MessageInput';
import { v4 as uuidv4 } from 'uuid';
import DButton from '@/components/Global/DButton';
import ConfirmIcon from '@/components/Global/Icons/ConfirmIcon';
import UploadedImagePreview from '@/components/Global/UploadedImagePreview';
import { saveChatbotImages } from '@/services/customization.service';
import DSpinner from '@/components/Global/DSpinner';
import DLoading from '@/components/DLoading';

/**
 * NewChat component
 * @param {Object} props - Component props
 * @param {Object} props.config - Chat configuration containing messages, settings, etc.
 * @param {Object} props.chatContainerRef - Reference to the chat container for scrolling
 * @param {Function} props.handleSendQuestion - Function to handle sending messages
 * @param {boolean} props.isInApp - Whether the chat is in the app
 * @param {boolean} props.isPreviewMode - Whether the chat is in preview mode
 * @param {boolean} props.showInAppHeader - Whether to show the in-app header
 * @param {boolean} props.isDanteFaq - Whether this is the Dante FAQ
 * @param {boolean} props.hiddenConversation - Whether the conversation is hidden
 * @param {boolean} props.hiddenPoweredByDante - Whether to hide the "Powered by Dante" branding
 * @param {Function} props.handleCloseButton - Function to handle close button click
 * @param {Function} props.handleOpenDanteConversations - Function to handle opening Dante conversations
 * @param {Function} props.handleOpenDanteFaq - Function to handle opening Dante FAQ
 * @param {Function} props.handleOpenLiveAgent - Function to handle opening live agent
 * @param {boolean} props.showCloseBtn - Whether to show the close button
 * @param {boolean} props.showMenuBtn - Whether to show the menu button
 * @param {boolean} props.isAnswerLoading - Whether an answer is loading
 * @param {boolean} props.showButtons - Whether to show buttons
 * @param {Object} props.dynamicButtons - Dynamic button configurations
 * @param {boolean} props.hideFooter - Whether to hide the footer
 * @param {Function} props.handleFooterButton - Function to handle footer button click
 * @param {boolean} props.isPreviousConversationHasLiveAgent - Whether previous conversation has live agent
 * @param {boolean} props.isLoadingSuggestions - Whether suggestions are currently loading
 * @returns {JSX.Element}
 */
const NewChat = ({ 
  config,
  handleSendQuestion,
  chatContainerRef,
  isInApp = false,
  isPreviewMode = false,
  isDanteFaq = false,
  handleOpenLiveAgent = () => {},
  isAnswerLoading = false,
  showButtons,
  dynamicButtons,
  handleFooterButton = () => {},
  showSuggestionPrompts = false,
  isLoadingSuggestions = false,
  chatImageLoader,
  isPreviousConversationHasLiveAgent = false,
  handleMessagesOnChat = () => {},
  interactingWithLiveAgent,
  isInHumanHandoverApp,
  pollResponse,
  humanHandoverConfig = {},
  humanHandoverMarkResolved = () => {},
  humanHandoverTakeConversation = () => {},
  handleShowVoice = () => {},
  showConsent = false,
  setShowConsent = () => {},
  isLeadGenMode = false,
  isLiveAgentResolved = false,
  messagesLoading = false,
  onTypingStart = () => {},
  onTypingStop = () => {},
}) => {
  // State for the input message
  const [question, setQuestion] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const messagesEndRef = useRef(null);
  const [uploadedImages, setUploadedImages] = useState([]);
  const [isUploadingImages, setIsUploadingImages] = useState(false);
  
  // Auto-scroll to bottom when messages change
  // useEffect(() => {
  //   if (messagesEndRef.current) {
  //     messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
  //   }
  // }, [config?.messages]);
  
  // Handle sending a message
  const handleSend = useCallback(async (message) => {
    if (message.trim() || uploadedImages.length > 0) {
      let imageUrls = [];
      if (uploadedImages.length > 0 && config?.kb_id) {
        // Set uploading state to true to show spinner
        setIsUploadingImages(true);
        
        // Create FormData to upload images
        const formData = new FormData();
        console.log('uploadedImages', uploadedImages);
        uploadedImages.forEach((image, index) => {
          console.log('image', image);
          if (typeof image !== 'string') {
            formData.append('file', image);
          }
        });

        try {
          const response = await saveChatbotImages(config.kb_id, formData);
          console.log('Image upload response:', response.data);
          
          // Check if response contains an array of URLs
          if (response.data && Array.isArray(response.data.urls)) {
            imageUrls = response.data.urls;
          } 
          // Check if response contains a single URL
          else if (response.data && response.data.url) {
            imageUrls.push(response.data.url);
          }
          // If response structure is different, log it for debugging
          else if (response.data) {
            console.log('Unexpected image upload response structure:', response.data);
          }
        } catch (error) {
          console.error('Error uploading images:', error);
        } finally {
          // Reset uploading state regardless of success or failure
          setIsUploadingImages(false);
        }
      }
      
      handleSendQuestion(message, imageUrls);
      setUploadedImages([]);
      setQuestion('');
    }
  }, [handleSendQuestion, uploadedImages, config?.kb_id]);

  const handleRemoveImage = (indexToRemove) => {
    setUploadedImages(prev => prev.filter((_, index) => index !== indexToRemove));
  };

  // Handle suggestion action click
  const handleSuggestionClick = useCallback((action) => {
    handleSendQuestion(action.content);
  }, [handleSendQuestion]);

  // Handle live agent connection
  const handleLiveAgentConnection = useCallback(() => {
    setShowConsent(false);
    
    handleMessagesOnChat('add', {
      id: uuidv4(),
      content: 'Chat with agent',
      role: 'user',
      date_created: new Date().toISOString(),
      type: 'live_agent_message',
    })

    setTimeout(() => {
      handleMessagesOnChat('add', {
        id: uuidv4(),
        content: 'Chat with team member requested',
        type: 'info',
      })
    }, 1000);
    
    setTimeout(() => {
      handleOpenLiveAgent();
    }, 2000);
  }, [handleMessagesOnChat, handleOpenLiveAgent, setShowConsent]);

  // Handle recording start
  const handleRecordStart = useCallback(() => {
    console.log('Recording started');
  }, []);

  // Handle recording stop
  const handleRecordStop = useCallback((transcript) => {
    console.log('Recording stopped, transcript:', transcript);

    if (transcript && transcript.trim()) {
      handleSendQuestion(transcript);
    }
  }, [handleSendQuestion]);

  if(messagesLoading) {
    return <DLoading show={true} />
  }

  return (
    <div className="flex flex-col h-full">
      {config?.disclaimer_enabled && !isInHumanHandoverApp && <div className="text-sm new-text-grey text-center pb-2 pt-3">
        {config?.disclaimer_text}
      </div> }
      <div className={`flex-grow overflow-auto pb-2 no-scrollbar px-4 flex-1 basis-0 min-h-0 ${config?.disclaimer_enabled ? '' : 'pt-2'}`} ref={chatContainerRef}>
        <NewChatListMessages 
          messages={config?.messages || []} 
          onSuggestionClick={handleSuggestionClick}
          transformLinkUri={config?.transformLinkUri}
          chatbot_profile_pic={config?.chatbot_icon}
          handleFooterButton={handleFooterButton}
          isDanteFaq={isDanteFaq}
          isInApp={isInApp}
          chatImageLoader={chatImageLoader}
          isPreviewMode={isPreviewMode}
          showSuggestionPrompts={showSuggestionPrompts}
          isLoadingSuggestions={isLoadingSuggestions}
          config={config}
          dynamicButtons={dynamicButtons}
          handleOpenLiveAgent={handleLiveAgentConnection}
          isPreviousConversationHasLiveAgent={isPreviousConversationHasLiveAgent}
          isAnswerLoading={isAnswerLoading}
          interactingWithLiveAgent={interactingWithLiveAgent}
          isInHumanHandoverApp={isInHumanHandoverApp}
          pollResponse={pollResponse}
          showConsent={showConsent}
          setShowConsent={setShowConsent}
          handleSend={handleSend}
          isLeadGenMode={isLeadGenMode}
          showButtons={showButtons}
          messagesEndRef={messagesEndRef}
        />
        <div ref={messagesEndRef} />
      </div>
      {isInHumanHandoverApp && !humanHandoverConfig?.isTaken && (
        <div className="grow-0 flex gap-size1 w-full mb-size2">
          <DButton
            variant="grey"
            fullWidth
            size="md"
            onClick={() => {
              humanHandoverMarkResolved();
            }}
          >
            Mark as resolved
          </DButton>
          <DButton
            variant="dark"
            fullWidth
            size="md"
            onClick={() => {
              humanHandoverTakeConversation();
            }}
          >
            Join
          </DButton>
        </div>
      )}
      {
        isInHumanHandoverApp && !isLiveAgentResolved && humanHandoverConfig?.isTaken && (
          <div className="flex gap-size1 grow-0 overflow-x-auto flex-nowrap h-[38px] justify-end px-size1 flex-shrink-0 no-scrollbar overflow-y-hidden">
            <DButton variant="resolved" onClick={() => humanHandoverMarkResolved()}>
              <ConfirmIcon />
              Mark as resolved
            </DButton>
          </div>
        )
      }
      {(!isInHumanHandoverApp || (isInHumanHandoverApp && humanHandoverConfig?.isTaken)) && (
        <div className="pt-4 px-4">
        {/* Always show image previews when there are uploaded images */}
        {uploadedImages.length > 0 && (
            <div className="flex gap-size1 overflow-x-auto py-size1">
              {uploadedImages.map((image, index) => (
                <UploadedImagePreview
                  key={index}
                  image={image}
                  onRemove={() => handleRemoveImage(index)}
                />
              ))}
            </div>
          )}
          {/* Show spinner when uploading images */}
          {isUploadingImages && (
            <div className="flex justify-center items-center py-2">
              <DSpinner color="#6366F1" style={{ height: 24, width: 24 }} />
              <span className="ml-2 text-sm text-gray-600">Uploading image...</span>
            </div>
          )}
          <MessageInput
            onSend={handleSend}
            onRecordStart={handleRecordStart}
            onRecordStop={handleRecordStop}
            isRecording={isRecording}
            setIsRecording={setIsRecording}
            placeholder={config?.input_placeholder_text || "Message"}
            disabled={isPreviewMode || isAnswerLoading || isUploadingImages}
            config={config}
            handleShowVoice={handleShowVoice}
            isInHumanHandoverApp={isInHumanHandoverApp}
            pollResponse={pollResponse}
            humanHandoverConfig={humanHandoverConfig}
            setUploadedImages={setUploadedImages}
            uploadedImages={uploadedImages}
            onTypingStart={onTypingStart}
            onTypingStop={onTypingStop}
          />
        </div>
      )}
    </div>
  );
};

export default NewChat; 