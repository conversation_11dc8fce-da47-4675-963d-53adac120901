import React, { useState } from 'react';
import MessageInput from '../../components/MessageInput';
import HumanAgent from '../../components/HumanAgent';
import LayoutMain from '../../layouts/LayoutMain';

const MessageInputExample = () => {
  const [messages, setMessages] = useState([]);
  const [isRecording, setIsRecording] = useState(false);

  const handleSendMessage = (message) => {
    setMessages([...messages, { type: 'user', content: message }]);
    
    // Simulate a response after a short delay
    setTimeout(() => {
      setMessages(prev => [
        ...prev, 
        { 
          type: 'agent', 
          content: `This is a response to: "${message}"`,
          name: '<PERSON><PERSON>',
          role: 'Customer Support Specialist'
        }
      ]);
    }, 1000);
  };

  const handleRecordStart = () => {
    setIsRecording(true);
    console.log('Recording started');
  };

  const handleRecordStop = () => {
    setIsRecording(false);
    console.log('Recording stopped');
    
    // Simulate a voice message being sent
    setMessages([...messages, { type: 'user', content: '[Voice message]' }]);
    
    // Simulate a response after a short delay
    setTimeout(() => {
      setMessages(prev => [
        ...prev, 
        { 
          type: 'agent', 
          content: 'I received your voice message. How can I help you?',
          name: 'Jeta',
          role: 'Customer Support Specialist'
        }
      ]);
    }, 1000);
  };

  return (
    <LayoutMain>
      <div className="new-theme container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Message Input Component Demo</h1>
        
        <div className="max-w-2xl mx-auto">
          <div className="mb-8">
            <h2 className="text-lg font-medium mb-3">Component States</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Default State</h3>
                <MessageInput 
                  onSend={() => {}}
                  onRecordStart={() => {}}
                  onRecordStop={() => {}}
                  placeholder="Default state"
                />
              </div>
              
              <div>
                <h3 className="text-sm font-medium mb-2">With Custom Placeholder</h3>
                <MessageInput 
                  onSend={() => {}}
                  onRecordStart={() => {}}
                  onRecordStop={() => {}}
                  placeholder="Type your message here..."
                />
              </div>
              
              <div className="dark new-bg-dark p-4 rounded-lg">
                <h3 className="text-sm font-medium mb-2 text-white">Dark Mode</h3>
                <MessageInput 
                  onSend={() => {}}
                  onRecordStart={() => {}}
                  onRecordStop={() => {}}
                />
              </div>
              
              <div>
                <h3 className="text-sm font-medium mb-2">Responsive (Full Width)</h3>
                <MessageInput 
                  className="w-full"
                  onSend={() => {}}
                  onRecordStart={() => {}}
                  onRecordStop={() => {}}
                />
              </div>
            </div>
          </div>
          
          <div className="mb-8">
            <h2 className="text-lg font-medium mb-3">Interactive Demo</h2>
            
            <div className="border rounded-lg p-4 bg-gray-50 mb-4 h-80 overflow-y-auto">
              {messages.length === 0 ? (
                <div className="flex items-center justify-center h-full text-gray-400">
                  Send a message to start the conversation
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map((message, index) => (
                    <div key={index} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                      {message.type === 'user' ? (
                        <div className="new-bg-accent new-text-light px-4 py-2 rounded-2xl max-w-xs">
                          {message.content}
                        </div>
                      ) : (
                        <HumanAgent
                          name={message.name}
                          role={message.role}
                          message={message.content}
                        />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            <div className="flex justify-center">
              <MessageInput 
                className="w-full"
                onSend={handleSendMessage}
                onRecordStart={handleRecordStart}
                onRecordStop={handleRecordStop}
                placeholder="Write you message"
              />
            </div>
          </div>
        </div>
      </div>
    </LayoutMain>
  );
};

export default MessageInputExample; 