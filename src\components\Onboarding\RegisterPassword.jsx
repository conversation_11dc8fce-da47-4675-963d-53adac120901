import { useState } from 'react';
import ChangePasswordForm from '../Auth/ChangePasswordForm';
import DButton from '../Global/DButton';
import DCheckbox from '../Global/DCheckbox';
import LayoutOnBoarding from './LayoutOnBoarding';
import { SignUpStepsEnum } from './signUpSteps';
import * as authService from '@/services/auth.service';
import { useUserStore } from '@/stores/user/userStore';
import { getUserInfo } from '@/services/user.service';
import getUserProfile from '@/application/user/getUserProfile';
import DRadioGroup from '../Global/DRadioGroup';
import { useNavigate, useSearchParams } from 'react-router-dom';
import * as userService from '@/services/user.service';
import { trackSignup } from '@/helpers/analytics';

const RegisterPassword = ({
  handleChange,
  setCanSubmit,
  setForm,
  setCurrentStep,
  email,
  trial_plan_type,
  contactConsent,
}) => {
  const navigate = useNavigate();
  const [urlParams] = useSearchParams();
  const saveAuthDetail = useUserStore((state) => state.saveAuthDetail);
  const setUser = useUserStore((state) => state.setUser);

  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [hasError, setHasError] = useState(false);

  const [data, setData] = useState({
    new_password: '',
    canSubmit: false,
  });

  const changeOnboarding = async () => {
    try {
      await userService.updateOnboardingStatus({
        onboarding_completed: true,
      });
    } catch (error) {
      console.error('Error changing onboarding:', error);
    }
  };

  const registerUser = async () => {
    try {
      const rewardful_referral = window.Rewardful?.referral;
      const registerResponse = await authService.register({
        email,
        ...data,
        password: data.new_password,
        first_name: 'John',
        last_name: 'Doe',
        full_name: 'John Doe',
        trial_plan_type,
        contact_consent: contactConsent,
        kb_id: urlParams.get('kb_id'),
        rewardful_referral,
      });

      if (registerResponse.status === 201) {
        // Register now returns JWT token directly - no need for separate login call
        const auth = { access_token: registerResponse.data.access_token };

        saveAuthDetail(auth);

        const { data: userInfo } = await getUserInfo(auth.access_token);

        const userProfile = await getUserProfile();

        saveAuthDetail({ ...auth, ...userInfo, user_id: userInfo.id });
        setUser({ ...userInfo, ...userProfile });

        // Track signup event
        trackSignup({
          user_id: userInfo.id,
          email: email,
          first_name: userInfo.first_name || 'John',
          last_name: userInfo.last_name || 'Doe',
          tier_name: trial_plan_type || 'starter',
          billing_cycle: 'none',
          signup_method: 'Email'
        });

        changeOnboarding();
        navigate('/');
        // setCurrentStep(SignUpStepsEnum.PROFILE);
      } else {
        setErrorMessage('Registration failed. Please check your details.');
      }
    } catch (error) {
      console.error('Error during registration:', error);
      setErrorMessage('An unexpected error occurred. Please try again.');
    }
  };

  const handleRegister = async () => {
    try {
      setIsLoading(true);
      setErrorMessage('');

      if (!data.new_password || !data.canSubmit) {
        setIsLoading(false);
        setHasError(true);
        return;
      }

      await registerUser();

      setIsLoading(false);
    } catch (error) {
      console.error('Error during registration:', error);
      setErrorMessage('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <LayoutOnBoarding
      title="Set your password"
      alert={`You are registering with ${email}`}
    >
      {errorMessage && <p className="text-error mb-4">{errorMessage}</p>}
      <ChangePasswordForm
        showCurrentPassword={false}
        loading={isLoading}
        handleChange={handleChange}
        setCanSubmit={setCanSubmit}
        setPasswords={(passwordData) =>
          setData((prevData) => ({ ...prevData, ...passwordData }))
        }
        error={hasError}
      />
      <div className="flex gap-size3 w-full">
        <DButton
          variant="grey"
          size="lg"
          className="w-max whitespace-nowrap"
          onClick={() => setCurrentStep(SignUpStepsEnum.EMAIL)}
        >
          Go Back
        </DButton>
        <DButton
          type="submit"
          variant="dark"
          className={`trial-selected-button trial-selected-is-${trial_plan_type}`}
          size="lg"
          fullWidth
          onClick={handleRegister}
          loading={isLoading}
        >
          Complete
        </DButton>
      </div>
    </LayoutOnBoarding>
  );
};

export default RegisterPassword;
