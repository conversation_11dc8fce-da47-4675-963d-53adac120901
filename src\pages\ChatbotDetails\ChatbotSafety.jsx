import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Bubble from '@/components/Bubble';
import DAlert from '@/components/Global/DAlert';
import DButton from '@/components/Global/DButton';
import DInput from '@/components/Global/DInput/DInput';
import DSwitch from '@/components/Global/DSwitch';
import DSwitchAccordion from '@/components/Global/DSwitchAccordion';
import { COMMON_CLASSNAMES, DANTE_THEME_CHAT } from '@/constants';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import LayoutWithButtons from '@/layouts/LayoutWithButtons';
import useLayoutStore from '@/stores/layout/layoutStore';
import * as customizationService from '@/services/customization.service';
import useDanteApi from '@/hooks/useDanteApi';
import useToast from '@/hooks/useToast';
import featureCheck from '@/helpers/tier/featureCheck';
import compareObjects from '@/helpers/compareObjects';
import ReactRouterPrompt from 'react-router-prompt';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import DLoading from '@/components/DLoading';
import StyleTag from '@/components/StyleTag';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
const ChatbotSafety = () => {
  const params = useParams();
  const [seePassword, setSeePassword] = useState(false);
  const { addSuccessToast, addWarningToast, addErrorToast } = useToast();
  const teamSelected = useTeamManagementStore((state) => state.selectedTeam?.id);
  const navigate = useNavigate();

  const {
    data: safetyData,
    refetch: refetchSafetyData,
    loading: safetyLoading,
  } = useDanteApi(customizationService.getChatbotSafety, [], {}, params.id);

  const {
    data: chatbotCustomization,
    refetch: refetchChatbotCustomization,
    loading: chatbotCustomizationLoading,
  } = useDanteApi(
    customizationService.getChatbotCustomizationById,
    [],
    {},
    { kb_id: params.id }
  );

  const [data, setData] = useState(safetyData);
  const [isSaveLoading, setIsSaveLoading] = useState(false);
  const [passwordProtection, setPasswordProtection] = useState(
    chatbotCustomization?.public ? false : true
  );
  const [changedData, setChangedData] = useState({});

  // Blocked words state
  const [blockedWords, setBlockedWords] = useState(data?.blocked_words || []);
  const [blockedWordInput, setBlockedWordInput] = useState('');

  useEffect(() => {
    setBlockedWords(data?.blocked_words || []);
  }, [data]);

  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setIsInPreviewBubblePage = useLayoutStore(
    (state) => state.setIsInPreviewBubblePage
  );
  const [error, setError] = useState({
    current_password: null,
    password: null,
    confirm_password: null,
  });
  const [passwordData, setPasswordData] = useState({
    password: '',
    confirm_password: '',
    current_password: '',
  });
  const [unsavedChanges, setUnsavedChanges] = useState(false);

  const validatePassword = () => {
    return passwordData.password === passwordData.confirm_password;
  };

  const handleCancel = async () => {
    await refetchSafetyData();
    await refetchChatbotCustomization();
    navigate(`/chatbot/${params.id}`);
  };

  const handleSaveSafety = async () => {
    try {
      if (!data?.public) {
        let newErrors = {};
        let hasError = false;

        if (chatbotCustomization?.password && !passwordData.current_password) {
          newErrors.current_password = 'Current password is required';
          hasError = true;
        }

        if (passwordData.password || passwordData.confirm_password) {
          if (!passwordData.password) {
            newErrors.password = 'Password is required';
            hasError = true;
          }

          if (!passwordData.confirm_password) {
            newErrors.confirm_password = 'Confirm password is required';
            hasError = true;
          }

          if (
            passwordData.password &&
            passwordData.confirm_password &&
            !validatePassword()
          ) {
            newErrors.password = 'Passwords do not match';
            newErrors.confirm_password = 'Passwords do not match';
            hasError = true;
          }
        }

        if (hasError) {
          setError(newErrors);
          return;
        }
      }

      setIsSaveLoading(true);
      const response = await customizationService.updateChatbotSafety(
        params.id,
        {
          ...changedData,
          password: passwordData.password,
          current_password: passwordData.current_password,
        }
      );
      if (response.status === 200) {
        addSuccessToast({
          message: 'AI Chatbot safety updated successfully',
        });
        // Refetch safety data and chatbot customization so that our local data matches the saved state.
        await refetchSafetyData();
        await refetchChatbotCustomization();

        setUnsavedChanges(false);
        setError({
          current_password: null,
          password: null,
          confirm_password: null,
        });
        setPasswordData({
          confirm_password: '',
          password: '',
          current_password: '',
        });
      }
    } catch (e) {
      if (e.response.data.detail) {
        addErrorToast({
          message: e.response.data.detail,
        });
      }
    } finally {
      setIsSaveLoading(false);
    }
  };

  const handleActivateChatbot = async (checked) => {
    try {
      await customizationService.toggleChatbotActivation(params.id, checked);
      setData({ ...data, activate_chatbot: checked });
      setChangedData({
        ...changedData,
        activate_chatbot: checked,
      });
      addSuccessToast({
        message: `AI Chatbot ${checked ? 'activated' : 'paused'} successfully`,
      });
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    setSidebarOpen(false);
    setIsInPreviewBubblePage(true);
  }, [setSidebarOpen, setIsInPreviewBubblePage]);

  useEffect(() => {
    setData(safetyData);
  }, [safetyData]);

  useEffect(() => {
    setPasswordProtection(chatbotCustomization?.public ? false : true);
  }, [chatbotCustomization]);

  useEffect(() => {
    if (!data || !safetyData) {
      return;
    }
    const hasUnsavedChanges = !compareObjects(safetyData, data);
    setUnsavedChanges(hasUnsavedChanges);
  }, [data, safetyData]);

  if (safetyLoading || chatbotCustomizationLoading || !data) {
    return <DLoading show={true} />;
  }

  return (
    <>
      <LayoutRightSidebar
        RightSidebar={() => (
          <div className={COMMON_CLASSNAMES.previewBubble}>
            <StyleTag
              tag=".bubble"
              tempCustomizationData={chatbotCustomization}
            />
            <Bubble
              type="chatbot"
              config={{
                ...chatbotCustomization,
                ...data,
                home_tab_enabled: false,
                remove_watermark: true,
              }}
              isPreviewMode={true}
            />
          </div>
        )}
      >
        {() => (
          <LayoutWithButtons
            footer={
              <div className="flex items-center justify-between">
                <DButton
                  variant="grey"
                  className="!h-12 w-full md:w-auto md:!min-w-52"
                  onClick={handleCancel}
                >
                  Cancel
                </DButton>
                <DButton
                  variant="dark"
                  className="!h-12 w-full md:w-auto md:!min-w-52"
                  onClick={handleSaveSafety}
                  loading={isSaveLoading}
                  disabled={compareObjects(safetyData, data)}
                >
                  Save
                </DButton>
              </div>
            }
          >
            <div className="flex flex-col gap-size5">
              <div className="flex flex-col gap-size1 bg-grey-2 rounded-size0 p-size3">
                <DSwitch
                  label={
                    data?.activate_chatbot
                      ? 'AI Chatbot active'
                      : 'AI Chatbot inactive'
                  }
                  checked={data?.activate_chatbot}
                  onChange={(checked) => handleActivateChatbot(checked)}
                />
                {!data?.activate_chatbot && (
                  <DAlert state="alert" className="!w-full flex items-center">
                    <p className="text-sm tracking-tight">
                      This AI Chatbot is currently inactive and inaccessible to
                      users.
                    </p>
                  </DAlert>
                )}
              </div>
              <div className="h-px w-full bg-grey-5"></div>
              <div className="flex flex-col gap-size1">
                <p className="text-base font-medium tracking-tight">
                  Maximum credit allowance
                </p>
                <p className="text-xs tracking-tight text-grey-50">
                  Set a maximum credit limit for the AI Chatbot{' '}
                  <span className="text-orange-300">(0 for unlimited)</span>.
                </p>
                <DInput
                  placeholder="0"
                  value={data?.max_number_of_credits || ''}
                  type="number"
                  min={0}
                  onChange={(e) => {
                    if (!teamSelected && !featureCheck('rate_limiting')) {
                      return;
                    }

                      const value = Math.max(0, Number(e.target.value) || 0);
                      setData({
                        ...data,
                        max_number_of_credits: e.target.value
                          ? Number(value).toString()
                          : '',
                      });
                      setChangedData({
                        ...changedData,
                        max_number_of_credits: e.target.value
                          ? Number(value).toString()
                          : 0,
                    });
                  }}
                />
              </div>
              <div className="flex flex-col gap-size1">
                <p className="text-base font-medium tracking-tight">
                  Conversation message limit
                </p>
                <p className="text-xs tracking-tight text-grey-50">
                  Set maximum messages per conversation limit{' '}
                  <span className="text-orange-300">(0 for unlimited)</span>.
                </p>
                <DInput
                  placeholder="0"
                  value={data?.max_messages_in_conversations || ''}
                  type="number"
                  min={0}
                  onChange={(e) => {
                    if (!teamSelected && !featureCheck('rate_limiting')) {
                      return;
                    }
                    const value = Math.max(0, Number(e.target.value) || 0);
                    setData({
                      ...data,
                      max_messages_in_conversations: e.target.value
                        ? Number(value).toString()
                        : '',
                    });
                    setChangedData({
                      ...changedData,
                      max_messages_in_conversations: e.target.value
                        ? Number(value).toString()
                        : 0,
                    });
                  }}
                />
              </div>
              <div className="flex flex-col gap-size1">
                <p className="text-base font-medium tracking-tight">
                  Maximum message allowance
                </p>
                <p className="text-xs tracking-tight text-grey-50">
                  Limit messages in a time frame (e.g., 5 in 1 min){' '}
                  <span className="text-orange-300">(0 for unlimited)</span>.
                </p>
                <div className="flex flex-col md:flex-row gap-size2">
                  <DInput
                    placeholder="0"
                    value={data?.number_of_messages_per_minutes || ''}
                    type="number"
                    min={0}
                    onChange={(e) => {
                      if (!teamSelected && !featureCheck('rate_limiting')) {
                        return;
                      }
                      const value = Math.max(0, Number(e.target.value) || 0);
                      setData({
                        ...data,
                        number_of_messages_per_minutes: e.target.value
                          ? Number(value).toString()
                          : '',
                      });
                      setChangedData({
                        ...changedData,
                        number_of_messages_per_minutes: e.target.value
                          ? Number(value).toString()
                          : 0,
                      });
                    }}
                    suffixText="messages"
                  />
                  <p className="text-sm tracking-tight mt-0 md:mt-size2">in</p>
                  <DInput
                    placeholder="0"
                    value={data?.number_of_minutes || ''}
                    type="number"
                    min={0}
                    onChange={(e) => {
                      if (!teamSelected && !featureCheck('rate_limiting')) {
                        return;
                      }
                      const value = Math.max(0, Number(e.target.value) || 0);
                      setData({
                        ...data,
                        number_of_minutes: e.target.value
                          ? Number(value).toString()
                          : '',
                      });
                      setChangedData({
                        ...changedData,
                        number_of_minutes: e.target.value
                          ? Number(value).toString()
                          : 0,
                      });
                    }}
                    suffixText="minutes"
                  />
                </div>
              </div>
              <div className="flex flex-col gap-size1">
                <p className="text-base font-medium tracking-tight">Blocked words</p>
                <p className="text-xs tracking-tight text-grey-50">
                  When users try to send messages containing these words as complete words, they will see a warning notification and the message will be blocked.
                </p>
                <div className="flex flex-wrap gap-2 mb-2 min-h-[34px]">
                  {blockedWords.map((word, idx) => (
                    <span
                      key={word}
                      className="bg-grey-5 text-grey-90 px-3 py-1 rounded flex items-center gap-1"
                    >
                      {word}
                      <button
                        type="button"
                        className="ml-1 text-grey-50 hover:text-red-500"
                        onClick={() => {
                          if (!teamSelected && !featureCheck('rate_limiting')) {
                            return;
                          }
                          const updated = blockedWords.filter((_, i) => i !== idx);
                          setBlockedWords(updated);
                          setData({ ...data, blocked_words: updated });
                          setChangedData({ ...changedData, blocked_words: updated });
                        }}
                      >
                        ×
                      </button>
                    </span>
                  ))}
                  <input
                    type="text"
                    className="outline-none border-none bg-transparent min-w-[60px]"
                    placeholder="Add word"
                    value={blockedWordInput}
                    onChange={e => {
                      if (!teamSelected && !featureCheck('rate_limiting')) {
                        return;
                      }
                      setBlockedWordInput(e.target.value);
                    }}
                    onKeyDown={e => {
                      if (!teamSelected && !featureCheck('rate_limiting')) {
                        return;
                      }
                      if (
                        (e.key === 'Enter' || e.key === ',') &&
                        blockedWordInput.trim() &&
                        !blockedWords.includes(blockedWordInput.trim())
                      ) {
                        const updated = [...blockedWords, blockedWordInput.trim()];
                        setBlockedWords(updated);
                        setData({ ...data, blocked_words: updated });
                        setChangedData({ ...changedData, blocked_words: updated });
                        setBlockedWordInput('');
                        e.preventDefault();
                      }
                      if (e.key === 'Backspace' && !blockedWordInput && blockedWords.length) {
                        // Remove last tag on backspace if input is empty
                        const updated = blockedWords.slice(0, -1);
                        setBlockedWords(updated);
                        setData({ ...data, blocked_words: updated });
                        setChangedData({ ...changedData, blocked_words: updated });
                      }
                    }}
                  />
                </div>
              </div>
              <div className="h-px w-full bg-grey-5"></div>
              <DSwitchAccordion
                title="Password protection"
                switchOpen={passwordProtection}
                onToggle={(value) => {
                  if (!teamSelected && !featureCheck('chatbot_password')) {
                    return;
                  }
                  setChangedData({ ...changedData, public: !value });
                  setPasswordProtection(value);
                  setData({ ...data, public: !value });
                }}
              >
                <div className="flex flex-col gap-size3">
                  {!chatbotCustomization?.public && data?.password !== null && (
                    <div className="flex flex-col gap-size1">
                      <p>Current password</p>
                      <div className="flex gap-size1">
                        <DInput
                          type={seePassword ? 'text' : 'password'}
                          placeholder="current password"
                          value={passwordData.current_password || ''}
                          error={error.current_password}
                          autoComplete="off"
                          onChange={(e) => {
                            setPasswordData({
                              ...passwordData,
                              current_password: e.target.value,
                            });
                            setChangedData({
                              ...changedData,
                              current_password: e.target.value,
                            });
                          }}
                        />
                      </div>
                    </div>
                  )}
                  <div className="flex flex-col gap-size1">
                    <p>Set password</p>
                    <DInput
                      type={seePassword ? 'text' : 'password'}
                       placeholder="set password"
                      value={passwordData.password || ''}
                      autoComplete="off"
                      onChange={(e) => {
                        setData({
                          ...data,
                          password: passwordProtection ? e.target.value : '',
                        });
                        setPasswordData({
                          ...passwordData,
                          password: passwordProtection ? e.target.value : '',
                        });
                        setChangedData({
                          ...changedData,
                          password: passwordProtection ? e.target.value : '',
                        });
                      }}
                      error={error.password}
                    />
                  </div>
                  <div className="flex flex-col gap-size1">
                    <p>Confirm password</p>
                    <DInput
                      type={seePassword ? 'text' : 'password'}
                      placeholder="confirm password"
                      autoComplete="off"
                      value={passwordData.confirm_password || ''}
                      onChange={(e) => {
                        setPasswordData({
                          ...passwordData,
                          confirm_password: e.target.value,
                        });
                        setChangedData({
                          ...changedData,
                          confirm_password: e.target.value,
                        });
                        setData({
                          ...data,
                          confirm_password: e.target.value,
                        });
                      }}
                      error={error.confirm_password}
                    />
                  </div>
                </div>
              </DSwitchAccordion>
            </div>
            <ReactRouterPrompt when={unsavedChanges}>
              {({ isActive, onConfirm, onCancel }) => (
                <DConfirmationModal
                  open={isActive}
                  onClose={onCancel}
                  onConfirm={onConfirm}
                  title="Are you sure you want to leave this page?"
                  description="You have unsaved changes. If you leave, you will lose your changes."
                  confirmText="Leave"
                  cancelText="Cancel"
                  variantConfirm="danger"
                />
              )}
            </ReactRouterPrompt>
          </LayoutWithButtons>
        )}
      </LayoutRightSidebar>
    </>
  );
};

export default ChatbotSafety;
