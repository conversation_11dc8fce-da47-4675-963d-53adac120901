import React from 'react';
import DButton from '../DButton';
import DModal from '../DModal';

const DConfirmationModal = ({
  open,
  onClose,
  onConfirm,
  title,
  description,
  confirmText,
  cancelText,
  variantConfirm,
  loading = false,
}) => {
  return (
    <DModal
      isOpen={open}
      onClose={() => onClose()}
      title={title}
      footer={
        <footer className="flex w-full items-center gap-size1 border-grey-5">
          <DButton
            onClick={onClose}
            text={cancelText}
            className="w-full"
            size="sm"
            variant="grey"
            fullWidth
          >
            {cancelText}
          </DButton>
          <DButton
            onClick={onConfirm}
            text={confirmText}
            className="w-full"
            variant={variantConfirm || 'dark'}
            size="sm"
            fullWidth
            loading={loading}
          >
            {confirmText}
          </DButton>
        </footer>
      }
    >
      <p className="text-lg tracking-tight font-light">{description}</p>
    </DModal>
  );
};

export default DConfirmationModal;
