import AiVoiceIcon from '@/components/Global/Icons/AiVoiceIcon';
import DBadge from '../../Global/DBadge';
import DButton from '../../Global/DButton';
import AiAvatarIcon from '../../Global/Icons/AiAvatarIcon';
import ConnectIcon from '../../Global/Icons/ConnectIcon';
import FlagIcon from '../../Global/Icons/FlagIcon';
import GeneralStats from '../GeneralStats';

import ChatbotBlockChart from './ChatbotBlockChart';

import './index.css';
import DButtonIcon from '@/components/Global/DButtonIcon';
import ShareIcon from '@/components/Global/Icons/ShareIcon';
import { STATUS } from '@/constants';
import { useEffect, useState } from 'react';
import { DateTime } from 'luxon';
import EditIcon from '@/components/Global/Icons/EditIcon';

const ChatbotBlock = ({
  icon,
  name,
  status,
  infos,
  stats,
  openChatbot,
  handleShareChatbot,
  insights,
}) => {
  const [chartData, setChartData] = useState([]);

  const renderBadge = (hiddenLabel = false) => (
    <DBadge
      label={status}
      type={status === STATUS.ACTIVE ? STATUS.ACTIVE : STATUS.PAUSED}
      hiddenLabel={hiddenLabel}
    />
  );

  useEffect(() => {
    const chartDataTranform = Object.keys(insights?.stats || {}).map((key) => ({
      date: DateTime.fromISO(key),
      value: insights.stats[key],
    }));
    setChartData(chartDataTranform);
  }, [insights]);

  return (
    <div className="chatbot-block flex flex-col w-full md:max-w-[300px] rounded-size1 bg-white/50 shadow-sm p-size5 gap-size4 animate-fadeIn">
      <header className="flex items-center justify-between animate-fadeInUpDelayed1">
        <div className="flex gap-size0 items-center">
          <img src={icon} alt={`${name} avatar`} className="size-6" />
          <h1 className="text-lg truncate max-w-52">{name}</h1>
        </div>

        <div className="flex">{renderBadge(true)}</div>
      </header>

      <div className="w-full h-px bg-grey-5"></div>

      <div className="chatbot-block_infos flex flex-col gap-size2 animate-fadeInUpDelayed2">
        <GeneralStats
          Icon={FlagIcon}
          value={insights.flagged_messages}
          labelSingular="Flagged message"
          labelPlural="Flagged messages"
          status={status}
        />
        <GeneralStats
          Icon={ConnectIcon}
          value={infos.total_integrations}
          labelSingular="Active integration"
          labelPlural="Active integrations"
          status={status}
        />
        {/* <GeneralStats
          Icon={AiAvatarIcon}
          value={infos.total_avatars}
          labelSingular="Persona connected"
          labelPlural="Personas connected"
          status={status}
          beta={true}
        /> */}
        <GeneralStats
          Icon={AiVoiceIcon}
          value={infos.voice_agents}
          labelSingular="AI Voice Agent connected"
          labelPlural="AI Voice Agents connected"
          status={status}
          beta={true}
        />
      </div>

      {/* <div className="chatbot-block_chart w-full hidden md:flex">
        <ChatbotBlockChart options={{}} data={chartData} status={status} />
      </div> */}

      <footer className="flex flex-col items-start gap-size1 justify-between animate-fadeInUpDelayed3">
        <DButton
          variant="outlined"
          size="sm"
          className="grow"
          onClick={openChatbot}
          fullWidth
        >
          <span className='text-sm'>Edit AI Chatbot</span>
          <EditIcon width={16} height={16} />

        </DButton>
        <DButton variant="outlined" size="sm" onClick={handleShareChatbot} fullWidth>
          <span className='text-sm'>Share AI Chatbot</span>
          <ShareIcon width={16} height={16} />
        </DButton>
      </footer>
    </div>
  );
};

export default ChatbotBlock;
