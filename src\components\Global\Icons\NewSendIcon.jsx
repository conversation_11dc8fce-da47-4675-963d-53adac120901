import React from 'react';
import PropTypes from 'prop-types';

const NewSendIcon = ({ className = '', color = '#6351FF', ...props }) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width="16" 
      height="22" 
      viewBox="0 0 16 22" 
      fill="none"
      className={className}
      {...props}
    >
      <path 
        fillRule="evenodd" 
        clipRule="evenodd" 
        d="M8.00001 21.125C7.70165 21.125 7.4155 21.0065 7.20452 20.7955C6.99354 20.5845 6.87501 20.2984 6.87501 20V4.71499L2.79501 8.79499C2.58175 8.99371 2.29968 9.10189 2.00823 9.09675C1.71678 9.09161 1.4387 8.97354 1.23258 8.76742C1.02646 8.5613 0.908394 8.28322 0.903251 7.99177C0.898109 7.70032 1.00629 7.41825 1.20501 7.20499L7.20501 1.20499C7.41595 0.994311 7.70189 0.875977 8.00001 0.875977C8.29814 0.875977 8.58408 0.994311 8.79501 1.20499L14.795 7.20499C14.9937 7.41825 15.1019 7.70032 15.0968 7.99177C15.0916 8.28322 14.9736 8.5613 14.7674 8.76742C14.5613 8.97354 14.2832 9.09161 13.9918 9.09675C13.7003 9.10189 13.4183 8.99371 13.205 8.79499L9.12501 4.71499V20C9.12501 20.2984 9.00649 20.5845 8.79551 20.7955C8.58453 21.0065 8.29838 21.125 8.00001 21.125Z"
        fill={color}
      />
    </svg>
  );
};

NewSendIcon.propTypes = {
  className: PropTypes.string,
  color: PropTypes.string
};

export default NewSendIcon;
