const NOTIFICATION_AUDIO_URL = 'https://dante-public-files.lon1.cdn.digitaloceanspaces.com/notification_sound.mp3';

export const unlockAudioContext = () => {
  const audio = new Audio(NOTIFICATION_AUDIO_URL);
  audio.muted = true;
  audio.play()
    .then(() => console.log('Audio context unlocked.'))
    .catch((error) => console.error('Failed to unlock audio context:', error));
};

export const registerServiceWorker = async () => {
  if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
    console.error('Service Worker or Push API not supported');
    return null;
  }

  try {
    // First, unregister any existing service workers
    const registrations = await navigator.serviceWorker.getRegistrations();
    for (let registration of registrations) {
      await registration.unregister();
      console.log('Service Worker unregistered');
    }

    // Then register a new one
    console.log('Registering new Service Worker...');
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/'
    });

    console.log('Service Worker registered successfully:', registration);

    // Set up the message listener
    setupServiceWorkerMessageListener();

    return registration;
  } catch (error) {
    console.error('Error registering Service Worker:', error);
    // Log more details about the error
    if (error instanceof TypeError) {
      console.error('TypeError details:', error.message);
    }
    return null;
  }
};

export const requestNotificationPermission = async () => {
  if (!('Notification' in window)) {
    console.error('This browser does not support notifications');
    return false;
  }

  try {
    const permission = await Notification.requestPermission();
    if (permission === 'granted') {
      await unlockAudioContext(); // Unlock audio context on permission grant
    }
    return permission === 'granted';
  } catch (error) {
    console.error('Error requesting permission:', error);
    return false;
  }
};

export const showNotificationWithSound = async (title, options = {}) => {
  if (!('serviceWorker' in navigator)) {
    console.error('Service Worker is not supported');
    return;
  }

  try {
    const registration = await navigator.serviceWorker.ready;

    // Check if we have notification permission without requesting it
    if (Notification.permission !== 'granted') {
      console.log('Notification permission not granted, skipping notification');
      return;
    }

    // Show notification with enhanced options
    await registration.showNotification(title, {
      ...options,
      icon: options.icon || 'https://chat.dante-ai.com/btn-embed-dark.png',
      vibrate: [100, 50, 100],
      requireInteraction: false,
      tag: 'notification-' + Date.now(),
      data: {
        ...options.data,
        soundUrl: NOTIFICATION_AUDIO_URL
      }
    });

    // Play sound immediately for active tabs
    playNotificationSound();
  } catch (error) {
    console.error('Error showing notification:', error);
  }
};

// Helper function to play notification sound
function playNotificationSound() {
  const audio = new Audio(NOTIFICATION_AUDIO_URL);
  audio.volume = 1.0;

  const playPromise = audio.play();
  if (playPromise) {
    playPromise.catch((error) => {
      console.error('Error playing notification sound:', error);
    });
  }
}

// Add this to handle messages from the service worker
let pendingNotificationSounds = new Map(); // Store pending sounds that couldn't be played

export const setupServiceWorkerMessageListener = () => {
  if (!('serviceWorker' in navigator)) return;

  navigator.serviceWorker.addEventListener('message', async (event) => {
    if (event.data.type === 'PLAY_NOTIFICATION_SOUND') {
      try {
        const audio = new Audio(event.data.audioUrl);
        await audio.play();
      } catch (error) {
        if (error.name === 'NotAllowedError') {
          // Store the notification sound to play later
          pendingNotificationSounds.set(event.data.timestamp, event.data.audioUrl);

          // Add one-time click listener to document if not already added
          if (!document.hasAttribute('data-sound-listener')) {
            document.setAttribute('data-sound-listener', 'true');
            document.addEventListener('click', async () => {
              // Try to play all pending sounds
              for (const [timestamp, audioUrl] of pendingNotificationSounds) {
                try {
                  const audio = new Audio(audioUrl);
                  await audio.play();
                  pendingNotificationSounds.delete(timestamp);
                } catch (err) {
                  console.error('Error playing pending notification sound:', err);
                }
              }
            }, { once: true }); // Remove listener after first click
          }
        } else {
          console.error('Error playing notification sound:', error);
        }
      }
    }
  });
};
