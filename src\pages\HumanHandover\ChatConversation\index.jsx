import ChatManager from '@/components/Chatbot/ChatManager';
import MenuCategory from '@/components/Chatbot/MenuCategory';
import CategoryIcon from '@/components/Global/CategoryIcon';
import DButtonIcon from '@/components/Global/DButtonIcon';
import CheckmarkIcon from '@/components/Global/Icons/CheckmarkIcon';
import ChevronDownIcon from '@/components/Global/Icons/ChevronDownIcon';
import ChevronRightIcon from '@/components/Global/Icons/ChevronRightIcon';
import CloseIcon from '@/components/Global/Icons/CloseIcon';
import CopyIcon from '@/components/Global/Icons/CopyIcon';
import FlagIcon from '@/components/Global/Icons/FlagIcon';
import useDanteApi from '@/hooks/useDanteApi';
import { getChatbotCustomizationById } from '@/services/customization.service';
import { updateCategoryConversation } from '@/services/humanHandover';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';
import { useUserStore } from '@/stores/user/userStore';
import { useEffect, useState } from 'react';

const ChatConversation = ({ conversation, mode }) => {
  const { auth } = useUserStore();
  const [copiedName, setCopiedName] = useState(false);
  const [copiedEmail, setCopiedEmail] = useState(false);
  const { setCloseConversation, addOpenedConversation } = useHumanHandoverStore(
    (state) => state
  );
  const { data } = useDanteApi(
    getChatbotCustomizationById,
    [conversation.kb_id],
    {},
    {
      kb_id: conversation.kb_id,
    }
  );

  const handleCopy = (text, type) => {
    navigator.clipboard.writeText(text);
    if (type === 'name') {
      setCopiedName(true);
    } else if (type === 'email') {
      setCopiedEmail(true);
    }

    setTimeout(() => {
      if (type === 'name') {
        setCopiedName(false);
      } else if (type === 'email') {
        setCopiedEmail(false);
      }
    }, 2000);
  };

  const [chatData, setChatData] = useState(null);

  const toggleOpen = () => {
    addOpenedConversation({ ...conversation, open: false });
  };

  const handleCategoryClick = async (hh_category_id) => {
    try {
      const response = await updateCategoryConversation(
        conversation.id,
        hh_category_id
      );
      if (response.status === 200) {
        addOpenedConversation({ ...conversation, category_id: hh_category_id });
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (data) {
      setChatData(data);
    }
  }, [data]);

  return (
    <div
      className={`bg-white rounded-lg shadow-lg transition-all flex flex-col z-[30] ${
        mode === 'box' && conversation.open ? 'w-[464px] h-[624px]' : 'hidden'
      }`}
    >
      <header className="flex flex-col rounded-t-size1">
        <div className="bg-white text-black py-size3 px-size2 flex justify-between items-center rounded-t-size1">
          <div className="flex gap-size1">
            <p className="text-lg font-regular tracking-tight">
              Conversation #
              {conversation.request_id.toString().padStart(4, '0')}
            </p>
            <div className="flex items-center justify-center border border-orange-300 rounded-full size-6">
              <FlagIcon className="text-orange-300 size-3" />
            </div>
          </div>
          <div className="flex items-center gap-size1">
            <MenuCategory
              currentCategoryId={conversation?.category_id}
              handleCategoryClick={handleCategoryClick}
            />

            <DButtonIcon onClick={toggleOpen}>
              <ChevronDownIcon className="size-3" />
            </DButtonIcon>
            <DButtonIcon
              onClick={() => {
                setCloseConversation(conversation);
              }}
            >
              <CloseIcon className="size-4" />
            </DButtonIcon>
          </div>
        </div>
        <div className="bg-grey-5 py-size1 px-size2 grid grid-cols-2 gap-size2  ">
          <div className="min-w-0">
            <p className="text-grey-50 text-xs tracking-tight font-regular">
              Name:
            </p>
            <div className="flex items-center gap-sizeo">
              <p className="text-sm tracking-tight font-regular truncate">
                {conversation?.user_name}
              </p>
              <button onClick={() => handleCopy(conversation?.user_name, 'name')} className="flex-shrink-0">
                {copiedName ? <CheckmarkIcon className="text-grey-50 size-4" /> : <CopyIcon className="text-grey-50 size-4" />}
              </button>
            </div>
          </div>
          <div className="min-w-0">
            <p className="text-grey-50 text-xs tracking-tight font-regular">
              Email:
            </p>
            <div className="flex items-center gap-sizeo ">
              <p className="text-sm tracking-tight font-regular truncate" title={conversation?.user_email}>
                {conversation?.user_email}
              </p>
              <button onClick={() => handleCopy(conversation?.user_email, 'email')} className="flex-shrink-0">
                {copiedEmail ? <CheckmarkIcon className="text-grey-50 size-4" /> : <CopyIcon className="text-grey-50 size-4" />}
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="grow overflow-y-auto p-size2 ">
        <ChatManager
          config={{ ...chatData, token: auth.access_token }}
          initialShouldFetchCustomization={false}
          isPreviewMode={false}
          isInApp={true}
          showInAppHeader={false}
          isInHumanHandoverApp={true}
          liveAgentConversation={conversation}
          isLiveAgentConversationResolved={conversation.state === 'resolved'}
          interactingWithLiveAgent={true}
        />
      </div>
    </div>
  );
};

export default ChatConversation;
