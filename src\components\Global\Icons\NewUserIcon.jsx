import React from 'react';
import PropTypes from 'prop-types';

const UserIcon = ({ className = '', ...props }) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width="24" 
      height="24" 
      viewBox="0 0 24 24" 
      fill="none"
      className={className}
      {...props}
    >
      <path 
        d="M12 12.75C17.775 12.75 22.5 16.5 22.5 19.5C22.5 20.2956 22.1839 21.0587 21.6213 21.6213C21.0587 22.1839 20.2956 22.5 19.5 22.5H4.5C3.70435 22.5 2.94129 22.1839 2.37868 21.6213C1.81607 21.0587 1.5 20.2956 1.5 19.5C1.5 16.5 6.225 12.75 12 12.75ZM12 15C9.585 15 7.404 15.789 5.862 16.851C4.197 18.0015 3.75 19.0995 3.75 19.5C3.75 19.6989 3.82902 19.8897 3.96967 20.0303C4.11032 20.171 4.30109 20.25 4.5 20.25H19.5C19.6989 20.25 19.8897 20.171 20.0303 20.0303C20.171 19.8897 20.25 19.6989 20.25 19.5C20.25 19.0995 19.8045 18 18.138 16.851C16.596 15.789 14.4135 15 12 15ZM12 1.5C13.3924 1.5 14.7277 2.05312 15.7123 3.03769C16.6969 4.02226 17.25 5.35761 17.25 6.75C17.25 8.14239 16.6969 9.47774 15.7123 10.4623C14.7277 11.4469 13.3924 12 12 12C10.6076 12 9.27226 11.4469 8.28769 10.4623C7.30312 9.47774 6.75 8.14239 6.75 6.75C6.75 5.35761 7.30312 4.02226 8.28769 3.03769C9.27226 2.05312 10.6076 1.5 12 1.5ZM12 3.75C11.2044 3.75 10.4413 4.06607 9.87868 4.62868C9.31607 5.19129 9 5.95435 9 6.75C9 7.54565 9.31607 8.30871 9.87868 8.87132C10.4413 9.43393 11.2044 9.75 12 9.75C12.7956 9.75 13.5587 9.43393 14.1213 8.87132C14.6839 8.30871 15 7.54565 15 6.75C15 5.95435 14.6839 5.19129 14.1213 4.62868C13.5587 4.06607 12.7956 3.75 12 3.75Z" 
        fill="currentColor"
      />
    </svg>
  );
};

UserIcon.propTypes = {
  className: PropTypes.string
};

export default UserIcon; 