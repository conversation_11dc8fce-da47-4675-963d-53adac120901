import { STATUS } from '@/constants';

const GeneralStats = ({ Icon, value, labelSingular, labelPlural, status, beta }) => {
  return (
    <div
      className={`chatbot-block_info flex gap-size0 text-sm items-center ${
        (value === 0 && status === STATUS.PAUSED) && 'text-grey-20'
      }`}
    >
      <div className='w-4'>
        <Icon width={16} height={16} /> 
      </div>
      {value ? `${value}\u00A0` : '0\u00A0'}
      {value !== 1 ? labelPlural : labelSingular}
    </div>
  );
};

export default GeneralStats;
