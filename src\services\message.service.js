import generateApiEndpoint from '@/helpers/generateApiEndpoint';
import http from './http';
import { DEFAULT_HEADERS } from './constants.service';

/**
 * Retrieves messages from a conversation by its ID.
 *
 * @param {string} conversation_id - The ID of the conversation.
 * @param {number} [ascending=1] - The order of the messages (1 for ascending, 0 for descending).
 * @returns {Promise} The HTTP response with the messages from the conversation.
 */
export const getMessagesByConversationId = (conversation_id, token, timezone, ascending = 1) => {
  return http.get(generateApiEndpoint('messages'), {
    params: {
      conversation_id,
      ascending,
      timezone
    },
    headers: DEFAULT_HEADERS,
  });
};

export const getMessagesByConversationIdWithApiKey = (conversation_id, apiKey, token, timezone, ascending = 1) => {
  return http.get(generateApiEndpoint('messages'), {
    params: {
      conversation_id,
      ascending,
      timezone
    },
    headers: {
      ...DEFAULT_HEADERS,
      'x-api-key': api<PERSON><PERSON>
    },
  });
};

/**
 * Retrieves messages from a conversation by its ID.
 *
 * @param {string} conversation_id - The ID of the conversation.
 * @param {string} token - The token of the conversation.
 * @param {number} [ascending=1] - The order of the messages (1 for ascending, 0 for descending).
 * @returns {Promise} The HTTP response with the messages from the conversation.
 */
export const getSharedMessagesByConversationId = (conversation_id, token, ascending = 1) => {
  return http.get(generateApiEndpoint('messages/shared'), {
    params: {
      conversation_id,
      token,
      ascending
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

/**
 * Updates the reaction of a message.
 *
 * @param {string} message_id - The ID of the message.
 * @param {string} action - The action to update the reaction.
 * @returns {Promise} The HTTP response with the updated reaction.
 */
export const updateReaction = (message_id, new_reaction) => {
  return http.post(
    generateApiEndpoint('messages/message/update_reaction'),
    {},
    {
      params: {
        message_id,
        new_reaction
      },
      headers: DEFAULT_HEADERS
    });
};

export const getConversationMessagesBySharedId = (conversation_shared_id) => {
  return http.get(generateApiEndpoint(`messages/conversation_shared/${conversation_shared_id}`), {

    headers: DEFAULT_HEADERS
  });
};
