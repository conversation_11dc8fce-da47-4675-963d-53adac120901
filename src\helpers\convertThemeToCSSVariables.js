import { DANTE_THEME_CHAT } from '@/constants';

const hex2rgb = (hex) => {
  const [r, g, b] = [1, 3, 5].map((offset) => parseInt(hex?.slice(offset, offset + 2), 16));
  return `${r}, ${g}, ${b}`;
};

export const convertThemeToCSSVariablesObj = (theme) => {
  const themeVars = {};

  // Converter as cores
  if (theme.color) {
    Object.entries(theme.color).forEach(([key, value]) => {
      themeVars[`--color-${key}`] = hex2rgb(value);
    });
  }

  const { family, size } = theme.font || {};
  // Use font_name from theme, or family from theme.font, or default to Inter
  const fontFamily = theme.font_name || family || DANTE_THEME_CHAT.font_name;
  // Always set font family, even if it's the default Inter
  themeVars['--dt-font-family'] = `'${fontFamily}'`;
  // Use font_size from theme, or size from theme.font, or default to 16
  const fontSize = theme.font_size || size || DANTE_THEME_CHAT.font_size;
  if (fontSize) {
    // Convert pixels to rem for accessibility (assuming 16px base)
    const sizeInRem = fontSize / 16;
    themeVars['--dt-font-size'] = `${sizeInRem}rem`;

    // Calculate responsive line height based on font size
    // 140% for 24px+, 150% for 20px, 160% for 18px and down
    let lineHeightPercentage;
    if (fontSize >= 24) {
      lineHeightPercentage = 140;
    } else if (fontSize >= 20) {
      lineHeightPercentage = 150;
    } else {
      lineHeightPercentage = 160;
    }

    themeVars['--dt-line-height'] = `${lineHeightPercentage}%`;
  }

  // Add support for new color properties
  if (theme.button_color) themeVars['--color-button'] = hex2rgb(theme.button_color);
  if (theme.hover_color) themeVars['--color-hover'] = hex2rgb(theme.hover_color);
  if (theme.accent_hover) themeVars['--color-accent-hover'] = hex2rgb(theme.accent_hover);
  if (theme.stroke_color) themeVars['--color-stroke'] = hex2rgb(theme.stroke_color);

  const colorVariables = {
    '--dt-color-element-2': 'rgba(var(--color-element), 0.02)',
    '--dt-color-element-5': 'rgba(var(--color-element), 0.05)',
    '--dt-color-element-10': 'rgba(var(--color-element), 0.1)',
    '--dt-color-element-20': 'rgba(var(--color-element), 0.2)',
    '--dt-color-element-50': 'rgba(var(--color-element), 0.5)',
    '--dt-color-element-75': 'rgba(var(--color-element), 0.75)',
    '--dt-color-element-100': 'rgba(var(--color-element), 1)',

    '--dt-color-brand-5': 'rgba(var(--color-brand), 0.05)',
    '--dt-color-brand-10': 'rgba(var(--color-brand), 0.1)',
    '--dt-color-brand-40': 'rgba(var(--color-brand), 0.4)',
    '--dt-color-brand-60': 'rgba(var(--color-brand), 0.6)',
    '--dt-color-brand-50': 'rgba(var(--color-brand), 0.5)',
    '--dt-color-brand-100': 'rgba(var(--color-brand), 1)',

    '--dt-color-surface-0': 'rgba(var(--color-surface), 0)',
    '--dt-color-surface-15': 'rgba(var(--color-surface), 0.15)',
    '--dt-color-surface-100': 'rgba(var(--color-surface), 1)',

    '--dt-color-alert-5': 'rgba(var(--color-alert), 0.05)',
    '--dt-color-alert-100': 'rgba(var(--color-alert), 1)',

    '--dt-color-positive-5': 'rgba(var(--color-positive), 0.05)',
    '--dt-color-positive-100': 'rgba(var(--color-positive), 1)',

    '--dt-color-negative-5': 'rgba(var(--color-negative), 0.05)',
    '--dt-color-negative-100': 'rgba(var(--color-negative), 1)',

    // Add new color variables
    '--dt-color-button-100': 'rgba(var(--color-button), 1)',
    '--dt-color-button-50': 'rgba(var(--color-button), 0.5)',
    '--dt-color-button-20': 'rgba(var(--color-button), 0.2)',
    '--dt-color-button-10': 'rgba(var(--color-button), 0.1)',
    '--dt-color-button-5': 'rgba(var(--color-button), 0.05)',

    '--dt-color-hover-100': 'rgba(var(--color-hover), 1)',
    '--dt-color-hover-50': 'rgba(var(--color-hover), 0.5)',
    '--dt-color-hover-20': 'rgba(var(--color-hover), 0.2)',
    '--dt-color-hover-10': 'rgba(var(--color-hover), 0.1)',
    '--dt-color-hover-5': 'rgba(var(--color-hover), 0.05)',

    '--dt-color-accent-hover-100': 'rgba(var(--color-accent-hover), 1)',
    '--dt-color-accent-hover-50': 'rgba(var(--color-accent-hover), 0.5)',
    '--dt-color-accent-hover-20': 'rgba(var(--color-accent-hover), 0.2)',
    '--dt-color-accent-hover-10': 'rgba(var(--color-accent-hover), 0.1)',
    '--dt-color-accent-hover-5': 'rgba(var(--color-accent-hover), 0.05)',

    '--dt-color-stroke-100': 'rgba(var(--color-stroke), 1)',
    '--dt-color-stroke-50': 'rgba(var(--color-stroke), 0.5)',
    '--dt-color-stroke-20': 'rgba(var(--color-stroke), 0.2)',
    '--dt-color-stroke-10': 'rgba(var(--color-stroke), 0.1)',
    '--dt-color-stroke-5': 'rgba(var(--color-stroke), 0.05)'
  };

  return { ...themeVars, ...colorVariables };
};

const convertThemeToCSSVariablesStyle = (theme) => {
  const themeVars = convertThemeToCSSVariablesObj(theme);
  return Object.entries(themeVars)
    .map(([key, value]) => `${key}: ${value};`)
    .join('\n');
};

export default convertThemeToCSSVariablesStyle;
