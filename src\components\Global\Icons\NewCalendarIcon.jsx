import React from 'react';
import PropTypes from 'prop-types';

const CalendarIcon = ({ className = '', ...props }) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width="24" 
      height="24" 
      viewBox="0 0 24 24" 
      fill="none"
      className={className}
      {...props}
    >
      <path 
        fillRule="evenodd" 
        clipRule="evenodd" 
        d="M7.875 8.24549C7.57663 8.24549 7.29048 8.12697 7.0795 7.91599C6.86853 7.70501 6.75 7.41886 6.75 7.12049V5.99999C6.15326 5.99999 5.58097 6.23705 5.15901 6.659C4.73705 7.08096 4.5 7.65326 4.5 8.24999V9.74999H19.5V8.24999C19.5 7.65326 19.2629 7.08096 18.841 6.659C18.419 6.23705 17.8467 5.99999 17.25 5.99999V7.12499C17.25 7.42336 17.1315 7.70951 16.9205 7.92049C16.7095 8.13147 16.4234 8.24999 16.125 8.24999C15.8266 8.24999 15.5405 8.13147 15.3295 7.92049C15.1185 7.70951 15 7.42336 15 7.12499V5.99999H9V7.12049C9 7.41886 8.88147 7.70501 8.6705 7.91599C8.45952 8.12697 8.17337 8.24549 7.875 8.24549ZM15 3.74999H9V2.62199C9 2.32363 8.88147 2.03748 8.6705 1.8265C8.45952 1.61552 8.17337 1.49699 7.875 1.49699C7.57663 1.49699 7.29048 1.61552 7.0795 1.8265C6.86853 2.03748 6.75 2.32363 6.75 2.62199V3.74999C5.55653 3.74999 4.41193 4.2241 3.56802 5.06801C2.72411 5.91193 2.25 7.05652 2.25 8.24999V17.25C2.25 18.4435 2.72411 19.5881 3.56802 20.432C4.41193 21.2759 5.55653 21.75 6.75 21.75H17.25C18.4435 21.75 19.5881 21.2759 20.432 20.432C21.2759 19.5881 21.75 18.4435 21.75 17.25V8.24999C21.75 7.05652 21.2759 5.91193 20.432 5.06801C19.5881 4.2241 18.4435 3.74999 17.25 3.74999V2.62499C17.25 2.32663 17.1315 2.04048 16.9205 1.8295C16.7095 1.61852 16.4234 1.49999 16.125 1.49999C15.8266 1.49999 15.5405 1.61852 15.3295 1.8295C15.1185 2.04048 15 2.32663 15 2.62499V3.74999ZM4.5 12V17.25C4.5 17.8467 4.73705 18.419 5.15901 18.841C5.58097 19.2629 6.15326 19.5 6.75 19.5H17.25C17.8467 19.5 18.419 19.2629 18.841 18.841C19.2629 18.419 19.5 17.8467 19.5 17.25V12H4.5Z" 
        fill="currentColor"
      />
    </svg>
  );
};

CalendarIcon.propTypes = {
  className: PropTypes.string
};

export default CalendarIcon; 