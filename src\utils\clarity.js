import Clarity from '@microsoft/clarity';

let isInitialized = false;

/**
 * Initialize Microsoft Clarity analytics
 * This should be called once when the app starts
 */
export const initClarity = () => {
  // Only initialize if we have a project ID and haven't initialized yet
  const projectId = import.meta.env.VITE_APP_CLARITY_PROJECT_ID;

  if (!projectId || projectId === 'YOUR_CLARITY_PROJECT_ID_HERE' || isInitialized) {
    return;
  }

  try {
    Clarity.init(projectId);
    isInitialized = true;
    console.log('Microsoft Clarity initialized successfully');
  } catch (error) {
    console.error('Failed to initialize Microsoft Clarity:', error);
  }
};

/**
 * Identify a user in Clarity
 * @param {string} customId - The unique identifier for the user
 * @param {string} customSessionId - Optional custom session identifier
 * @param {string} customPageId - Optional custom page identifier
 * @param {string} friendlyName - Optional friendly name for the user
 */
export const identifyUser = (customId, customSessionId = null, customPageId = null, friendlyName = null) => {
  if (!isInitialized) {
    console.warn('Clarity not initialized - cannot identify user');
    return;
  }

  // Validate required customId parameter
  if (!customId || typeof customId !== 'string' || customId.trim() === '') {
    console.warn('Clarity identifyUser: customId is required and must be a non-empty string');
    return;
  }

  try {
    Clarity.identify(customId, customSessionId, customPageId, friendlyName);
  } catch (error) {
    console.error('Failed to identify user in Clarity:', error);
  }
};

/**
 * Set a custom tag in Clarity
 * @param {string} key - The key for the tag
 * @param {string|string[]} value - The value(s) for the tag
 */
export const setTag = (key, value) => {
  if (!isInitialized) {
    console.warn('Clarity not initialized - cannot set tag');
    return;
  }

  if (!key || typeof key !== 'string' || key.trim() === '') {
    console.warn('Clarity setTag: key is required and must be a non-empty string');
    return;
  }

  try {
    Clarity.setTag(key, value);
  } catch (error) {
    console.error('Failed to set Clarity tag:', error);
  }
};

/**
 * Track a custom event in Clarity
 * @param {string} eventName - The name of the event
 */
export const trackEvent = (eventName) => {
  if (!isInitialized) {
    console.warn('Clarity not initialized - cannot track event');
    return;
  }

  if (!eventName || typeof eventName !== 'string' || eventName.trim() === '') {
    console.warn('Clarity trackEvent: eventName is required and must be a non-empty string');
    return;
  }

  try {
    Clarity.event(eventName);
  } catch (error) {
    console.error('Failed to track Clarity event:', error);
  }
};

/**
 * Set cookie consent for Clarity
 * @param {boolean} consent - Whether consent is given (default: true)
 */
export const setConsent = (consent = true) => {
  if (!isInitialized) {
    console.warn('Clarity not initialized - cannot set consent');
    return;
  }

  try {
    Clarity.consent(consent);
  } catch (error) {
    console.error('Failed to set Clarity consent:', error);
  }
};

/**
 * Upgrade session priority in Clarity
 * @param {string} reason - The reason for the upgrade
 */
export const upgradeSession = (reason) => {
  if (!isInitialized) {
    console.warn('Clarity not initialized - cannot upgrade session');
    return;
  }

  if (!reason || typeof reason !== 'string' || reason.trim() === '') {
    console.warn('Clarity upgradeSession: reason is required and must be a non-empty string');
    return;
  }

  try {
    Clarity.upgrade(reason);
  } catch (error) {
    console.error('Failed to upgrade Clarity session:', error);
  }
};
