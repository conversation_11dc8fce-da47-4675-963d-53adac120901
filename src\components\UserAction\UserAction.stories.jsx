import React from 'react';
import UserAction from './index';

export default {
  title: 'Components/UserAction',
  component: UserAction,
  parameters: {
    docs: {
      description: {
        component: `
          A button component with the new design system.
          
          **Typography Specifications:**
          - Font Family: Inter Variable
          - Font Size: 16px
          - Font Weight: 600 (Semibold)
          - Line Height: 160% (25.6px)
          - Default Text Color: #6351FF (Accent)
          
          **Button States:**
          - Default: #EFEFF3 background with #6351FF text
          - Hover: #D2D1E0 background
          - Sent: #6351FF background with white text
          - Disabled: 50% opacity
          
          **Shape:**
          - Border Radius: 16px 16px 4px 16px
          - Padding: 8px 16px
        `
      }
    }
  },
  argTypes: {
    onClick: { action: 'clicked' },
    label: { control: 'text' },
    isSent: { control: 'boolean' },
    disabled: { control: 'boolean' },
    icon: { control: false }
  }
};

// Default state
export const Default = {
  args: {
    label: 'User Action'
  }
};

// Sent state
export const Sent = {
  args: {
    label: 'Sent Action',
    isSent: true
  }
};

// With icon
export const WithIcon = {
  args: {
    label: 'With Icon',
    icon: (
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        width="16" 
        height="16" 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      >
        <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
        <polyline points="10 17 15 12 10 7"></polyline>
        <line x1="15" y1="12" x2="3" y2="12"></line>
      </svg>
    )
  }
};

// Disabled state
export const Disabled = {
  args: {
    label: 'Disabled Action',
    disabled: true
  }
};

// Sent with icon
export const SentWithIcon = {
  args: {
    label: 'Sent With Icon',
    isSent: true,
    icon: (
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        width="16" 
        height="16" 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      >
        <polyline points="20 6 9 17 4 12"></polyline>
      </svg>
    )
  }
};

// Typography showcase
export const TypographyShowcase = {
  args: {
    label: 'Text with Inter Variable Font'
  },
  decorators: [
    (Story) => (
      <div className="space-y-6">
        <Story />
        <div className="p-4 bg-gray-50 rounded-md max-w-md">
          <h3 className="text-sm font-medium mb-2">Typography Specifications:</h3>
          <ul className="text-xs space-y-1 text-gray-600">
            <li><span className="font-medium">Font Family:</span> Inter Variable</li>
            <li><span className="font-medium">Font Size:</span> 16px</li>
            <li><span className="font-medium">Font Weight:</span> 600 (Semibold)</li>
            <li><span className="font-medium">Line Height:</span> 160% (25.6px)</li>
            <li><span className="font-medium">Default Color:</span> #6351FF (Accent)</li>
          </ul>
        </div>
      </div>
    )
  ]
}; 