/* Animate background and description on hover for role options */

.role-option {
  transition: background 0.6s cubic-bezier(0.4,0,0.2,1), border 0.6s cubic-bezier(0.4,0,0.2,1);
  background: transparent;
  border-radius: 0.5rem;
  position: relative;
  border: 1px solid transparent;
}

.role-option:hover {
  background: #fff;
}

.role-option.selected {
  background: #fff;
  border: 1px solid #5642FF;
}

.role-description {
  max-height: 0;
  opacity: 0;
  transform: translateY(10px);
  transition:
    max-height 0.8s cubic-bezier(0.4,0,0.2,1),
    opacity 0.7s cubic-bezier(0.4,0,0.2,1),
    transform 0.7s cubic-bezier(0.4,0,0.2,1);
  overflow: hidden;
  margin-left: 2.8rem;
}

.role-option:hover .role-description,
.role-option.selected .role-description {
  max-height: 100px; /* Large enough for the text */
  opacity: 1;
  transform: translateY(0);
}