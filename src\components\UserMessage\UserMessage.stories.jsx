import React from 'react';
import UserMessage from './index';

export default {
  title: 'Components/UserMessage',
  component: UserMessage,
  parameters: {
    docs: {
      description: {
        component: `
          A component for displaying user messages.
          
          **Typography Specifications:**
          - Font Family: Inter Variable
          - Font Size: 16px
          - Font Weight: 400 (Regular)
          - Line Height: 160% (25.6px)
          - Default Text Color: #0F0942 (Dark)
          - Sent Text Color: #F1F0FF (Light)
          
          **States:**
          - Default: #EFEFF3 background with #0F0942 text
          - Hover: #D2D1E0 background
          - Sent: #6351FF background with #F1F0FF text
          
          **Shape:**
          - Border Radius: 16px 16px 4px 16px
          - Padding: 8px 16px
          - Gap: 10px
        `
      }
    }
  },
  argTypes: {
    message: { control: 'text' },
    isSent: { control: 'boolean' },
    icon: { control: false }
  }
};

// Default state
export const Default = {
  args: {
    message: 'This is a user message'
  }
};

// Sent state
export const Sent = {
  args: {
    message: 'This is a sent message',
    isSent: true
  }
};

// With icon
export const WithIcon = {
  args: {
    message: 'Message with icon',
    icon: (
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        width="16" 
        height="16" 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      >
        <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
        <polyline points="10 17 15 12 10 7"></polyline>
        <line x1="15" y1="12" x2="3" y2="12"></line>
      </svg>
    )
  }
};

// Sent with icon
export const SentWithIcon = {
  args: {
    message: 'Sent message with icon',
    isSent: true,
    icon: (
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        width="16" 
        height="16" 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      >
        <polyline points="20 6 9 17 4 12"></polyline>
      </svg>
    )
  }
};

// Long message
export const LongMessage = {
  args: {
    message: 'This is a much longer message that will wrap to multiple lines to demonstrate how the component handles longer content within the constraints of the design.'
  }
};

// Typography showcase
export const TypographyShowcase = {
  args: {
    message: 'Text with Inter Variable Font (Regular 400)'
  },
  decorators: [
    (Story) => (
      <div className="space-y-6">
        <Story />
        <div className="p-4 bg-gray-50 rounded-md max-w-md">
          <h3 className="text-sm font-medium mb-2">Typography Specifications:</h3>
          <ul className="text-xs space-y-1 text-gray-600">
            <li><span className="font-medium">Font Family:</span> Inter Variable</li>
            <li><span className="font-medium">Font Size:</span> 16px</li>
            <li><span className="font-medium">Font Weight:</span> 400 (Regular)</li>
            <li><span className="font-medium">Line Height:</span> 160% (25.6px)</li>
            <li><span className="font-medium">Default Color:</span> #0F0942 (Dark)</li>
            <li><span className="font-medium">Sent Color:</span> #F1F0FF (Light)</li>
          </ul>
        </div>
      </div>
    )
  ]
}; 