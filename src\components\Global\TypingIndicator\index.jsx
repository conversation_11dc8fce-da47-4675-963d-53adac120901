import React from 'react';
import PropTypes from 'prop-types';

/**
 * TypingIndicator - A component that shows animated typing dots with a message
 * @param {Object} props - Component props
 * @param {string} props.message - The typing message to display (e.g., "Agent is typing..." or "User is typing...")
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.show - Whether to show the typing indicator
 * @param {boolean} props.dotsOnly - Whether to show only dots without the message
 */
const TypingIndicator = ({
  message = "Typing...",
  className = "",
  show = true,
  dotsOnly = false
}) => {
  if (!show) return null;

  // Dots only mode - just show centered animated dots
  if (dotsOnly) {
    return (
      <div className={`flex justify-center ${className}`}>
        <div className="flex items-center gap-1">
          <div
            className="w-[5px] h-[5px] bg-gray-500 rounded-full animate-bounce"
            style={{ animationDelay: '0ms' }}
          ></div>
          <div
            className="w-[5px] h-[5px] bg-gray-500 rounded-full animate-bounce"
            style={{ animationDelay: '200ms' }}
          ></div>
          <div
            className="w-[5px] h-[5px] bg-gray-500 rounded-full animate-bounce"
            style={{ animationDelay: '400ms' }}
          ></div>
        </div>
      </div>
    );
  }

  // Default mode - show message with dots
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="text-sm text-gray-600">{message}</span>
      <div className="flex items-center gap-1">
        <div
          className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"
          style={{ animationDelay: '0ms' }}
        ></div>
        <div
          className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"
          style={{ animationDelay: '200ms' }}
        ></div>
        <div
          className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"
          style={{ animationDelay: '400ms' }}
        ></div>
      </div>
    </div>
  );
};

TypingIndicator.propTypes = {
  message: PropTypes.string,
  className: PropTypes.string,
  show: PropTypes.bool,
  dotsOnly: PropTypes.bool
};

export default TypingIndicator;
