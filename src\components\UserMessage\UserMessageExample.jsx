import React, { useState } from 'react';
import UserMessage from './index';

/**
 * UserMessageExample - Demonstrates the UserMessage component in a chat-like interface
 */
const UserMessageExample = () => {
  const [messages, setMessages] = useState([
    { id: 1, text: 'Hello! How can I help you today?', isUser: false },
    { id: 2, text: 'I have a question about your product', isUser: true, isSent: true },
    { id: 3, text: 'Of course! What would you like to know?', isUser: false }
  ]);
  
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  
  const handleSendMessage = () => {
    if (!newMessage.trim()) return;
    
    // Add user message as not sent yet
    const userMessageId = Date.now();
    setMessages([...messages, { id: userMessageId, text: newMessage, isUser: true, isSent: false }]);
    setNewMessage('');
    
    // Simulate sending after a delay
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === userMessageId ? { ...msg, isSent: true } : msg
        )
      );
      
      // Simulate typing indicator
      setIsTyping(true);
      
      // Simulate response after another delay
      setTimeout(() => {
        setIsTyping(false);
        setMessages(prev => [
          ...prev, 
          { 
            id: Date.now(), 
            text: getResponse(newMessage), 
            isUser: false 
          }
        ]);
      }, 1500);
    }, 1000);
  };
  
  // Simple response generator
  const getResponse = (message) => {
    const responses = [
      "That's a great question! Let me explain...",
      "I understand what you're asking. Here's what you need to know...",
      "Thanks for your question. The answer is...",
      "I'd be happy to help with that. Here's some information..."
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  return (
    <div className="new-theme max-w-md mx-auto p-4 border new-border-stroke rounded-lg">
      <h2 className="text-xl font-medium new-text-dark mb-4">Chat Example</h2>
      
      {/* Chat messages */}
      <div className="space-y-4 mb-6 h-80 overflow-y-auto p-2">
        {messages.map((message) => (
          <div 
            key={message.id} 
            className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
          >
            {message.isUser ? (
              <UserMessage 
                message={message.text} 
                isSent={message.isSent} 
                className="max-w-[80%]"
              />
            ) : (
              <div className="max-w-[80%] new-message-assistant">
                <p>{message.text}</p>
              </div>
            )}
          </div>
        ))}
        
        {isTyping && (
          <div className="flex justify-start">
            <div className="max-w-[80%] new-message-assistant">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Message input */}
      <div className="flex gap-2">
        <input
          type="text"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          placeholder="Type your message..."
          className="new-input flex-grow"
          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
        />
        <button 
          onClick={handleSendMessage}
          className="p-2 new-bg-accent rounded-full new-text-light"
          disabled={!newMessage.trim()}
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
          </svg>
        </button>
      </div>
      
      <div className="mt-4 p-3 bg-gray-50 rounded-md">
        <h3 className="text-sm font-medium mb-2">Component Information:</h3>
        <p className="text-xs text-gray-600">
          This example demonstrates the UserMessage component in a chat interface. 
          When you send a message, it first appears in the default state, then transitions 
          to the "sent" state after a delay, showing both visual states of the component.
        </p>
      </div>
    </div>
  );
};

export default UserMessageExample; 