import { HUMAN_HANDOVER_CHAT_BOX_LIMIT } from '@/constants';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const initialState = {
  selectedOrganizationId: null,
  userOrganization: null,
  selectedOrganization: null,
  categories: [],
  openedConversation: [],
  showChatBox: false,
  quickResponses: [],
  liveAgentConversationEvents: {}
}

const humanHandoverStoreSlice = (set) => ({
  ...initialState,
  setSelectedOrganizationId: (organizationId) => set({ selectedOrganizationId: organizationId }),
  setUserOrganization: (userOrganization) => set({ userOrganization }),
  setSelectedOrganization: (organization) => set({ selectedOrganization: organization }),
  updateCategories: (categories) => set({ categories }),
  updateQuickResponses: (quickResponses) => set({ quickResponses }),
  addOpenedConversation: (conversation) => {
    const openedConversationLocal = useHumanHandoverStore.getState().openedConversation || [];
    const hasConversation = openedConversationLocal.findIndex((c) => c.id === conversation.id);


    if (hasConversation > -1) {
      openedConversationLocal[hasConversation] = conversation;

      set({ openedConversation: [...openedConversationLocal] });
    } else {
      if (openedConversationLocal.length >= HUMAN_HANDOVER_CHAT_BOX_LIMIT) {
        openedConversationLocal.shift();
      }

      set({ openedConversation: [...openedConversationLocal, conversation] });
    }
  },
  setShowChatBox: () => set({ showChatBox: true }),
  setCloseConversation: (conversation) => {
    const openedConversation = useHumanHandoverStore.getState().openedConversation;
    const newOpenedConversation = openedConversation.filter((c) => c.id !== conversation.id);

    if (newOpenedConversation.length === 0) {
      set({ showChatBox: false, openedConversation: [] });
    } else {
      set({ openedConversation: newOpenedConversation });
    }
  },
  setLiveAgentConversationEvent: (conversationId, event) => {
    const liveAgentConversationEvents = useHumanHandoverStore.getState().liveAgentConversationEvents;
    if (!liveAgentConversationEvents[conversationId]) {
      liveAgentConversationEvents[conversationId] = [];
    }

    liveAgentConversationEvents[conversationId].push(event);
    set({ liveAgentConversationEvents });
  },
  getLiveAgentConversationEvents: (conversationId) => useHumanHandoverStore.getState().liveAgentConversationEvents[conversationId] || null,
  deleteLiveAgentConversationEvent: (conversationId, event) => {
    const liveAgentConversationEvents = useHumanHandoverStore.getState().liveAgentConversationEvents;
    liveAgentConversationEvents[conversationId] = liveAgentConversationEvents[conversationId].filter((e) => e !== event);
    set({ liveAgentConversationEvents });
  },
  deleteLiveAgentConversationEvents: (conversationId) => {
    const liveAgentConversationEvents = useHumanHandoverStore.getState().liveAgentConversationEvents;
    delete liveAgentConversationEvents[conversationId];
    set({ liveAgentConversationEvents });
  },

  reset: () => set({ ...initialState }),
});

const persistedHumanHandoverStore = persist(humanHandoverStoreSlice, {
  name: 'humanHandover'
});

export const useHumanHandoverStore = create(persistedHumanHandoverStore);
