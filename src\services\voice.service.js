import generateApiEndpoint from '@/helpers/generateApiEndpoint';
import http from './http';
import { DEFAULT_HEADERS } from './constants.service';

export const applyForBetaAccess = (reason) => {
    return http.post(generateApiEndpoint('ai_voice'), { reason }, {
        headers: DEFAULT_HEADERS
    });
}

export const getLoadingSounds = () => {
    return http.get(generateApiEndpoint('ai-voices/loading-sounds'));
}

export const getVoices = () => {
    return http.get(generateApiEndpoint('ai-voices'));
}

export const createVoice = (data) => {
    return http.post(generateApiEndpoint('ai-voices'), data, { headers: DEFAULT_HEADERS });
}

export const getVoice = (id) => {
    return http.get(generateApiEndpoint('ai-voices/' + id));
}

export const getTextToVoiceOptions = (provider) => {
    // If provider is specified, filter by it, otherwise get all providers
    const params = provider ? { voice_provider: provider } : {};

    return http.get(generateApiEndpoint('ai-voices/text-to-voice/settings'), {
        params,
        headers: DEFAULT_HEADERS,
    });
}

export const getTextToVoiceOptionsWithApiKey = (provider, apiKey) => {
    // If provider is specified, filter by it, otherwise get all providers
    const params = provider ? { voice_provider: provider } : {};

    return http.get(generateApiEndpoint('ai-voices/text-to-voice/settings'), {
        params,
        headers: {
            ...DEFAULT_HEADERS,
            'x-api-key': apiKey
        },
    });
}

export const getAllVoiceSettings = () => {
    return http.get(generateApiEndpoint('ai-voices/workflow-options/all'));
}

export const getAllVoiceSettingsFiltered = (options) => {
    return http.get(
      generateApiEndpoint('ai-voices/workflow-options/valid'),
      {
        params: options,
        headers: DEFAULT_HEADERS,
      }
    );
  };

export const updateVoice = (id, data) => {
    return http.patch(generateApiEndpoint('ai-voices/' + id), data, { headers: DEFAULT_HEADERS });
}

export const deleteVoice = (id) => {
    return http.delete(generateApiEndpoint('ai-voices/' + id), { headers: DEFAULT_HEADERS });
}

export const getVoiceConversations = (id) => {
    return http.get(generateApiEndpoint('ai-voices/conversations'), { params: { ai_voice_id: id } });
}

export const getVoiceConversationDetails = (id) => {
    return http.get(generateApiEndpoint('ai-voices/conversations/' + id));
}

export const getVoiceInsights = (date_from, date_to) => {
    return http.get(generateApiEndpoint('ai-voices/insights'), {
        params: {
            date_from,
            date_to
        },
        headers: DEFAULT_HEADERS
    });
}
