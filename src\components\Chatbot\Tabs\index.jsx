import { useState, useEffect } from 'react';

import DDraggableContainer from '../../Global/DDraggableContainer';
import AddIcon from '../../Global/Icons/AddIcon';
import SlotForm from './SlotForm';
import DTooltip from '@/components/Global/DTooltip';
import InfoIcon from '@/components/Global/Icons/InfoIcon';
import useDante<PERSON>pi from '@/hooks/useDanteApi';
import * as tabsService from '@/services/tabs.service';
import { useParams } from 'react-router-dom';
import SlotItemComponent from './SlotItemComponent';
import SortableEditableInput from '@/components/Global/SortableEditableInput';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import useToast from '@/hooks/useToast';
import DLoading from '@/components/DLoading';
import DSwitch from '@/components/Global/DSwitch';
import EyeIcon from '@/components/Global/Icons/EyeIcon';
import EyeClosedIcon from '@/components/Global/Icons/EyeClosedIcon';
import { useUserStore } from '@/stores/user/userStore';
import featureCheck from '@/helpers/tier/featureCheck';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';

const Tabs = ({
  slots,
  setSlots,
  enableHomeTab,
  setEnableHomeTab,
  enableAvatarTab,
  setEnableAvatarTab,
  chatbotId,
  mainTabs,
  setMainTabs,
  enablePreviousConversation,
  setEnablePreviousConversation,
  isPreviewingHomeTab,
  setIsPreviewingHomeTab,
}) => {
  const { addSuccessToast } = useToast();
  const params = useParams();
  const [selectedBtn, setSelectedBtn] = useState('all_links');
  const [errorMainTabs, setError] = useState({});
  const [isAddSlotFormOpen, setIsAddSlotFormOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const user = useUserStore((state) => state.user);
  const teamSelected = useTeamManagementStore((state) => state.selectedTeam?.id);

  const { data: slotsData, refetch } = useDanteApi(
    tabsService.getTabsPage,
    [],
    {},
    params.id ?? chatbotId
  );

  // Free user home tab logic is now handled in the parent component (ChatbotTabs.jsx)
  // useEffect(() => {
  //   if (user.tier_type === 'free' && !teamSelected) {
  //     setEnableHomeTab(false);
      // Don't modify mainTabs structure here as it causes false positive change detection
      // The enableHomeTab state is sufficient for controlling the home tab functionality
      //    setMainTabs((prevTabs) => ({
      //   ...prevTabs,
      //   home: {
      //     ...prevTabs.home,
      //     enabled: false
      //   }
      // }));
  //   }
  // }, [user.tier]); 
  
  // to prevent false positive change detection during initialization

  const handleAddSlot = () => {
    setIsAddSlotFormOpen(true);
  };

  const handleSwitchChangeMainTabs = (id, value) => {
    if (id === 'home') {
      if(!teamSelected && !featureCheck('home_tab')){
        return;
      }
      if(teamSelected && !featureCheck('tabs')){
        return;
      }

      if (user.tier_type === 'free' && !teamSelected) {
        return;
      }
      setEnableHomeTab(value);
    }
  };

  const handleFieldChangeMainTabs = (id, value) => {
    setMainTabs((prevTabs) => ({
      ...prevTabs,
      [id]: {
        ...prevTabs[id],
        label: value
      }
    }));
  };

  const handleReorderMainTabs = (items) => {
    const homeTab = items.find(item => item.id === 'home');
    const chatTab = items.find(item => item.id === 'chat');

    setMainTabs({
      home: {
        ...mainTabs.home,
        order: homeTab.order + 1
      },
      chat: {
        ...mainTabs.chat,
        order: chatTab.order + 1
      }
    });
  };

  const handleEditSlot = (item) => {
    switch (item.type) {
      case 'quick_link':
        setIsAddSlotFormOpen(true);
        setSelectedBtn('quick_link');
        setSelectedItem(item);
        break;
      case 'meta_link':
        setIsAddSlotFormOpen(true);
        setSelectedBtn('quick_link');
        setSelectedItem(item);
        break;
      case 'link_group':
        setIsAddSlotFormOpen(true);
        setSelectedBtn('link_group');
        setSelectedItem(item);
        break;
      case 'video':
        setIsAddSlotFormOpen(true);
        setSelectedBtn('video');
        setSelectedItem(item);
        break;
      case 'slider':
        setIsAddSlotFormOpen(true);
        setSelectedBtn('slider');
        setSelectedItem(item);
        break;
    }
  };

  const handleDeleteSlot = async (id) => {
    try {
      if (!selectedItem.id) {
        setSlots(
          slots.filter((item) => item.frontend_id !== selectedItem.frontend_id)
        );
        setSelectedItem(null);
        setIsConfirmationModalOpen(false);
        return;
      }
      let response;
      if (selectedItem.type === 'quick_link') {
        response = await tabsService.deleteQuickLink(
          selectedItem.id,
          params.id
        );
      } else if (selectedItem.type === 'meta_link') {
        response = await tabsService.deleteMetaLink(selectedItem.id, params.id);
      } else if (selectedItem.type === 'link_group') {
        response = await tabsService.deleteLinkGroup(
          selectedItem.id,
          params.id
        );
      } else if (selectedItem.type === 'video') {
        response = await tabsService.deleteVideo(selectedItem.id, params.id);
      } else if (selectedItem.type === 'slider') {
        response = await tabsService.deleteSlider(selectedItem.id, params.id);
      }
      if (response.status === 200) {
        addSuccessToast({
          message: 'Slot deleted successfully',
        });
        setIsConfirmationModalOpen(false);
        setSelectedItem(null);
        refetch();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleEyeButtonMouseDown = () => {
    if (user.tier_type === 'free') {
      setIsPreviewingHomeTab(true);
      setEnableHomeTab(true);
    }
  };

  const handleEyeButtonMouseUp = () => {
    if (user.tier_type === 'free') {
      setIsPreviewingHomeTab(false);
      setEnableHomeTab(false);
    }
  };

  useEffect(() => {
    if (slotsData?.slots) {
      const combinedSlots = [
        ...slotsData.slots.quick_links.map((item) => ({ ...item })),
        ...slotsData.slots.meta_links.map((item) => ({ ...item })),
        ...slotsData.slots.link_groups.map((item) => ({ ...item })),
        ...slotsData.slots.videos.map((item) => ({ ...item })),
        ...slotsData.slots.sliders.map((item) => ({ ...item })),
      ];
      setSlots(combinedSlots);
    }
  }, [slotsData?.slots]);

  if (!slotsData) {
    return <DLoading show={true} />;
  }

  // Convert mainTabs object to array for DDraggableContainer
  const mainTabsArray = [
    {
      id: 'home',
      input_label: mainTabs.home.label,
      order: mainTabs.home.order,
      is_editable: true,
      is_deletable: false,
      has_switch: true,
      switchValue: enableHomeTab
    },
    {
      id: 'chat',
      input_label: mainTabs.chat.label,
      order: mainTabs.chat.order,
      is_editable: true,
      is_deletable: false,
      switchValue: true,
      disclaimer: '(Default)'
    }
  ].sort((a, b) => a.order - b.order);

  return (
    <div className="flex flex-col gap-size3 p-size2 ">
      <div className="flex items-center gap-size1 rounded-size0 p-size2 bg-grey-2">
        <p className="text-lg font-medium tracking-tight">Manage Tabs</p>
        <DTooltip content="You can manage the tabs for your AI Chatbot here. You can add, edit, and delete tabs.">
          <InfoIcon className="text-grey-20 size-3" />
        </DTooltip>
      </div>
      <div className="w-full h-px bg-grey-5"></div>
      <div className="flex flex-col gap-size3 ">
        <DDraggableContainer
          items={mainTabsArray}
          setItems={handleReorderMainTabs}
          ItemComponent={SortableEditableInput}
          handleSwitchChange={handleSwitchChangeMainTabs}
          handleFieldChange={handleFieldChangeMainTabs}
          error={errorMainTabs}
          renderExtraContent={(item) => {
            if (item.id === 'home' && user.tier_type === 'free' && !teamSelected) {
              return (
                <button
                  className={`p-1 rounded-full transition-colors ${
                    isPreviewingHomeTab ? 'bg-grey-10' : 'hover:bg-grey-5'
                  }`}
                  onMouseDown={handleEyeButtonMouseDown}
                  onMouseUp={handleEyeButtonMouseUp}
                  onMouseLeave={handleEyeButtonMouseUp}
                  title="Press and hold to preview Home tab"
                >
                  {isPreviewingHomeTab ? <EyeIcon /> : <EyeClosedIcon />}
                </button>
              );
            }
            return null;
          }}
        />
      </div>
      <div className="w-full h-[1px] bg-grey-5"></div>
      <div className="flex items-center gap-size1 rounded-size0 p-size2  justify-between">
        <p className=" tracking-tight">Previous Conversation</p>
        <DSwitch
          checked={enablePreviousConversation}
          onChange={() =>
            setEnablePreviousConversation(!enablePreviousConversation)
          }
        />
      </div>
      <div className="flex items-center gap-size1 rounded-size0 p-size2 bg-grey-2">
        <p className="text-lg font-medium tracking-tight">Manage Slots</p>
      </div>
      <div className="w-full h-[1px] bg-grey-5"></div>
      <div className="flex flex-col gap-size3">
        <DDraggableContainer
          items={slots.sort((a, b) => a.order - b.order) || []}
          setItems={setSlots}
          ItemComponent={SlotItemComponent}
          handleEdit={handleEditSlot}
          handleDelete={(item) => {
            setSelectedItem(item);
            setIsConfirmationModalOpen(true);
          }}
        />
        <button
          className="dbutton bg-grey-2 rounded-size2 py-size1 px-size2 text-sm font-medium tracking-tight flex items-center gap-size1"
          onClick={handleAddSlot}
        >
          <AddIcon />
          <span>Add new slot</span>
        </button>
      </div>
      <SlotForm
        isOpen={isAddSlotFormOpen}
        onClose={() => {
          setIsAddSlotFormOpen(false);
          setSelectedBtn('all_links');
          setSelectedItem(null);
        }}
        selectedBtn={selectedBtn}
        setSelectedBtn={setSelectedBtn}
        item={selectedItem}
        refetch={refetch}
        setSlots={setSlots}
        slots={slots}
        chatbotId={chatbotId}
      />
      <DConfirmationModal
        open={isConfirmationModalOpen}
        onClose={() => setIsConfirmationModalOpen(false)}
        onConfirm={() => handleDeleteSlot(selectedItem.id)}
        title="Delete slot"
        description="Are you sure you want to delete this slot?"
        confirmText="Delete"
        cancelText="Cancel"
        variantConfirm="danger"
      />
    </div>
  );
};

export default Tabs;
