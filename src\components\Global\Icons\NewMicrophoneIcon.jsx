import React from 'react';
import PropTypes from 'prop-types';

const NewMicrophoneIcon = ({ className = '', ...props }) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width="18" 
      height="24" 
      viewBox="0 0 18 24" 
      fill="none"
      className={`new-text-secondary ${className}`}
      {...props}
    >
      <path 
        fillRule="evenodd" 
        clipRule="evenodd" 
        d="M11.25 10.5V5.25C11.25 4.65326 11.0129 4.08097 10.591 3.65901C10.169 3.23705 9.59674 3 9 3C8.40326 3 7.83097 3.23705 7.40901 3.65901C6.98705 4.08097 6.75 4.65326 6.75 5.25V10.5C6.75 11.0967 6.98705 11.669 7.40901 12.091C7.83097 12.5129 8.40326 12.75 9 12.75C9.59674 12.75 10.169 12.5129 10.591 12.091C11.0129 11.669 11.25 11.0967 11.25 10.5ZM9 0.75C7.80653 0.75 6.66193 1.22411 5.81802 2.06802C4.97411 2.91193 4.5 4.05653 4.5 5.25V10.5C4.5 11.6935 4.97411 12.8381 5.81802 13.682C6.66193 14.5259 7.80653 15 9 15C10.1935 15 11.3381 14.5259 12.182 13.682C13.0259 12.8381 13.5 11.6935 13.5 10.5V5.25C13.5 4.05653 13.0259 2.91193 12.182 2.06802C11.3381 1.22411 10.1935 0.75 9 0.75ZM10.125 19.431C12.3002 19.157 14.3005 18.0983 15.7505 16.4539C17.2005 14.8095 18.0004 12.6924 18 10.5V10.125C18 9.82663 17.8815 9.54048 17.6705 9.3295C17.4595 9.11853 17.1734 9 16.875 9C16.5766 9 16.2905 9.11853 16.0795 9.3295C15.8685 9.54048 15.75 9.82663 15.75 10.125V10.5C15.75 12.2902 15.0388 14.0071 13.773 15.273C12.5071 16.5388 10.7902 17.25 9 17.25C7.20979 17.25 5.4929 16.5388 4.22703 15.273C2.96116 14.0071 2.25 12.2902 2.25 10.5V10.125C2.25 9.82663 2.13147 9.54048 1.9205 9.3295C1.70952 9.11853 1.42337 9 1.125 9C0.826631 9 0.540483 9.11853 0.329505 9.3295C0.118526 9.54048 6.28765e-09 9.82663 0 10.125V10.5C0 15.09 3.435 18.8775 7.875 19.431V22.125C7.875 22.4234 7.99353 22.7095 8.2045 22.9205C8.41548 23.1315 8.70163 23.25 9 23.25C9.29837 23.25 9.58452 23.1315 9.79549 22.9205C10.0065 22.7095 10.125 22.4234 10.125 22.125V19.431Z" 
        fill="currentColor"
      />
    </svg>
  );
};

NewMicrophoneIcon.propTypes = {
  className: PropTypes.string
};

export default NewMicrophoneIcon; 