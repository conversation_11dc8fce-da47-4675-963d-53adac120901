import { useEffect, useState, useRef } from 'react';
import { DateTime } from 'luxon';
import { useParams } from 'react-router-dom';
import { TASK_POLLING_INTERVAL } from '@/constants';

const trainingMessages = [
  'Analyzing the structure of your content...',
  'Teaching your chatbot to understand intent.',
  'Indexing files for faster access later.',
  'Cross-referencing data like a pro librarian.',
  'Optimizing conversation flows behind the scenes.',
  'Running a quick sanity check on your inputs.',
  'Cleaning up noisy data — neat and tidy now.',
  'Loading language patterns into memory.',
  'Simulating small talk for better engagement.',
  'Extracting key insights from your files.',
  'Warming up neural pathways for smarter replies.',
  'Aligning data points for smoother conversations.',
  'Adjusting parameters for natural interaction.',
  'Running silent checks — all systems listening.',
];

import Bubble from '@/components/Bubble';
import DChatbotSidebar from '@/components/Chatbot/Details/ChatbotSidebar';
import DAlert from '@/components/Global/DAlert';
import DButton from '@/components/Global/DButton';
import DCheckbox from '@/components/Global/DCheckbox';
import DInput from '@/components/Global/DInput/DInput';
import AddIcon from '@/components/Global/Icons/AddIcon';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import FilesIcon from '@/components/Global/Icons/FilesIcon';
import FilterIcon from '@/components/Global/Icons/FilterIcon';
import LinkIcon from '@/components/Global/Icons/LinkIcon';
import ResetIcon from '@/components/Global/Icons/ResetIcon';
import SearchIcon from '@/components/Global/Icons/SearchIcon';
import UpRightIcon from '@/components/Global/Icons/UpRightIcon';
import useTaskHandler from '@/hooks/useTaskHandler';
import useDanteApi from '@/hooks/useDanteApi';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import LayoutWithButtons from '@/layouts/LayoutWithButtons';
import * as chatbotService from '@/services/chatbot.service';
import * as customizationService from '@/services/customization.service';
import useLayoutStore from '@/stores/layout/layoutStore';
import { useUserStore } from '@/stores/user/userStore';
import { COMMON_CLASSNAMES, TASK_TYPES } from '@/constants';
import AddContent from './AddContent';
import DModal from '@/components/Global/DModal';
import { Menu, MenuButton, MenuItem, MenuItems, Transition } from '@headlessui/react';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import DButtonIcon from '@/components/Global/DButtonIcon';
import ChevronLeftIcon from '@/components/Global/Icons/ChevronLeftIcon';
import ChevronRightIcon from '@/components/Global/Icons/ChevronRightIcon';
import useToast from '@/hooks/useToast';
import BlurredOverlay from '@/components/BlurredOverlay';
import { checkTeamManagementPermission } from '@/helpers/tier/featureCheck';
import DLoading from '@/components/DLoading';
import DLoaderTraining from '@/components/Global/DLoaderTraining';
import useTaskStore from '@/stores/task/taskStore';
import { useCreateChatbotStore } from '@/stores/chatbot/createChatbotStore';
import StyleTag from '@/components/StyleTag';

const ChatbotKnowledge = () => {
  let params = useParams();
  const { addSuccessToast, addErrorToast } = useToast();
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setIsInPreviewBubblePage = useLayoutStore(
    (state) => state.setIsInPreviewBubblePage
  );
  const user = useUserStore((state) => state.user);
  const [openAddContent, setOpenAddContent] = useState(false);
  const [openRetrain, setOpenRetrain] = useState(false);
  const [addedContent, setAddedContent] = useState(null);
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false);
  const [openDeleteFile, setOpenDeleteFile] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [openDeleteFiles, setOpenDeleteFiles] = useState(false);
  const {
    data,
    refetch,
    loading: dataLoading,
  } = useDanteApi(chatbotService.getChatbotById, [], {}, params.id);
  const { data: customizationData, loading: customizationLoading } =
    useDanteApi(
      customizationService.getChatbotCustomizationById,
      [],
      {},
      {
        kb_id: params.id,
      }
    );
  const [filteredFiles, setFilteredFiles] = useState(
    data?.results?.files.slice(0, 10)
  );
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 10;
  const totalPages = Math.ceil(
    (data?.results?.files?.length || 0) / itemsPerPage
  );

  const [isProcessing, setIsProcessing] = useState(false);
  const [isTraining, setIsTraining] = useState(false);
  const [trainingLoading, setTrainingLoading] = useState(false);
  const [taskId, setTaskId] = useState(null);
  const [trainingProgress, setTrainingProgress] = useState(0);
  const [trainingMessage, setTrainingMessage] = useState('');
  const [progressText, setProgressText] = useState('');
  const { tasks } = useTaskStore();
  const resetChatbotData = useCreateChatbotStore(
    (state) => state.resetChatbotData
  );

  const { handleTask } = useTaskHandler();

  // Helper function to check if user should see auto update button
  const shouldShowAutoUpdateButton = () => {
    // List of allowed emails that can see the auto update button
    const allowedEmails = [
      '<EMAIL>',
      '<EMAIL>',
    ];
    return allowedEmails.includes(user?.email);
  };

  const handleDeleteFiles = async () => {
    try {
      setIsProcessing(true);

      if (selectedFiles.length === data?.results?.files?.length) {
        const response = await chatbotService.deleteChatbotAllFiles(params.id);
        if (response.status === 200) {
          setOpenDeleteFiles(false);
          refetch();
          addSuccessToast({
            message: 'All files deleted successfully',
          });
        }
      } else if (selectedFiles.length >= 1) {
        // Use Promise.all to properly handle multiple async operations
        const deletePromises = selectedFiles.map(file =>
          chatbotService.deleteChatbotFile(file)
        );

        const responses = await Promise.all(deletePromises);

        // Check if all deletions were successful
        const allSuccessful = responses.every(response => response.status === 200);

        if (allSuccessful) {
          setOpenDeleteFiles(false);
          refetch();
          addSuccessToast({
            message: 'Selected files deleted successfully',
          });
        }
      }
    } catch (error) {
      console.log(error);
      addErrorToast({
        message: 'Error deleting files',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDeleteFile = async () => {
    try {
      if (!selectedFile) return;
      setIsProcessing(true);
      const response = await chatbotService.deleteChatbotFile(selectedFile);
      if (response.status === 200) {
        setOpenDeleteFile(false);
        refetch();
        addSuccessToast({
          message: 'File deleted successfully',
        });
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSelectAll = (isSelected) => {
    setSelectedFiles(
      isSelected ? data?.results?.files?.map((file) => file.id) : []
    );
  };

  const handleSelectFile = (id) => {
    let updatedSelection = [...selectedFiles];
    if (selectedFiles.includes(id)) {
      updatedSelection = updatedSelection.filter((fileId) => fileId !== id);
    } else {
      updatedSelection.push(id);
    }
    setSelectedFiles(updatedSelection);
  };

  const handleRetrain = async (type) => {
    try {
      setIsTraining(true);
      setTrainingProgress(0.01);
      setTrainingMessage(trainingMessages[0]);
      setProgressText('Starting training process...');

      const trainingTaskId = await handleTask(
        TASK_TYPES.TRAINING,
        chatbotService.trainChatbot,
        params.id,
        addedContent?.knowledge_base?.type ?? type
      );
      if (trainingTaskId) {
        setTaskId(trainingTaskId);
        setOpenRetrain(false);
        addSuccessToast({
          message: 'Chatbot is being retrained. Please wait for it to finish.',
        });
      }
    } catch (error) {
      setIsTraining(false);
      setTrainingProgress(0);
      setProgressText('');

    
      const errorMessage = error?.response?.data?.detail || error?.response?.data?.message || error?.message || 'Failed to start training. Please try again.';

      addErrorToast({ message: errorMessage });
      console.error('Training initiation failed:', error);
    }
  };

  const handleToggleAutoRefresh = async () => {
    try {
      const response = await customizationService.toggleAutoRefresh(
        params.id,
        !autoRefreshEnabled
      );
      if (response.status === 200) {
        setAutoRefreshEnabled(response.data.autorefresh_enabled);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleSearch = async (search, sort, offset = 0, limit = 10) => {
    try {
      const response = await chatbotService.searchChatbotFiles(
        params.id,
        search,
        sort,
        offset,
        limit
      );
      if (response.status === 200) {
        setFilteredFiles(response.data.files);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleExportChatbotCSV = async () => {
    try {
      const response = await chatbotService.exportChatbotFiles(params.id);
      if (response.status === 200 && response.data.download_url) {
        const link = document.createElement('a');
        link.href = response.data.download_url;
        link.setAttribute('download', 'chatbot-files.csv'); // Set filename
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        addSuccessToast({ message: 'CSV export started' });
      }
    } catch (error) {
      console.error('Error exporting CSV:', error);
      addErrorToast({ message: 'Failed to export CSV file' });
    }
  };

  const handlePagination = async (offset, limit) => {
    const response = await handleSearch('', '', offset, limit);
    if (response.status === 200) {
      setFilteredFiles(response.data.files);
    }
  };

  const handleCancelTraining = async () => {
    try {
      setTrainingLoading(true);
      const response = await chatbotService.cancelTraining(params.id);
      if (response.status === 200) {
        setIsTraining(false);
        setTrainingProgress(0);
        setProgressText('');
        setTrainingLoading(false);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setTrainingLoading(false);
    }
  };

  const getPaginationRange = () => {
    const totalPageNumbers = 7;
    const currentPageNumber = currentPage + 1;
    let pages = [];

    if (totalPages <= totalPageNumbers) {
      // less pages than threshold; show all
      pages = [...Array(totalPages)].map((_, i) => i + 1);
    } else {
      const showLeftEllipsis = currentPageNumber > 4;
      const showRightEllipsis = currentPageNumber < totalPages - 3;

      if (!showLeftEllipsis) {
        pages = [1, 2, 3, 4, 5, '...', totalPages];
      } else if (!showRightEllipsis) {
        pages = [
          1,
          '...',
          totalPages - 4,
          totalPages - 3,
          totalPages - 2,
          totalPages - 1,
          totalPages,
        ];
      } else {
        pages = [
          1,
          '...',
          currentPageNumber - 1,
          currentPageNumber,
          currentPageNumber + 1,
          '...',
          totalPages,
        ];
      }
    }
    return pages;
  };

  useEffect(() => {
    setSidebarOpen(false);
    if (window.innerWidth < 768) {
      setLayoutTitle('Chatbot');
    } else {
      setLayoutTitle('');
    }
  }, [window.innerWidth]);

  useEffect(() => {
    setAutoRefreshEnabled(customizationData?.autorefresh_enabled);
  }, [customizationData]);

  useEffect(() => {
    setIsInPreviewBubblePage(true);
  }, []);

  useEffect(() => {
    setFilteredFiles(
      data?.results?.files.slice(
        currentPage * itemsPerPage,
        (currentPage + 1) * itemsPerPage
      )
    );
  }, [data]);

  useEffect(() => {
    if (data?.results?.knowledge_base?.is_currently_training) {
      setIsTraining(true);
      handleRetrain(data?.results?.knowledge_base?.type);
      // setTrainingTaskId(data.results.training_task_id);
    }
  }, [data]);

  // Effect for rotating training messages with smooth transitions
  useEffect(() => {
    let interval;
    let fadeTimeout;

    if (isTraining) {
      // Set initial message
      setTrainingMessage(trainingMessages[0]);

      // Setup interval for message rotation with fade effect
      interval = setInterval(() => {
        // Get the current message element each time
        const messageElement = document.querySelector('.training-message');

        // Add fade-out class if element exists
        if (messageElement) {
          messageElement.classList.add('opacity-0');
        }

        // Change message after fade-out
        fadeTimeout = setTimeout(() => {
          setTrainingMessage(trainingMessages[Math.floor(Math.random() * trainingMessages.length)]);

          // Get the potentially updated element
          const updatedMessageElement = document.querySelector('.training-message');

          // Add fade-in class if element exists
          if (updatedMessageElement) {
            updatedMessageElement.classList.remove('opacity-0');
          }
        }, 300); // Half of the transition duration

      }, TASK_POLLING_INTERVAL * 3);
    }

    return () => {
      clearInterval(interval);
      clearTimeout(fadeTimeout);
    };
  }, [isTraining]);

  // Effect for simulating progress with smooth number-by-number increments
  useEffect(() => {
    let progressInterval;

    if (isTraining) {
      // Start with a clean 1% progress
      setTrainingProgress(0.01);
      // We're not displaying progress text anymore, but keeping the state for compatibility
      setProgressText('');

      // Simple, reliable approach: increment by 1% at a steady pace
      let currentPercent = 1;
      progressInterval = setInterval(() => {
        // Stop at 60% and let the task completion take over
        if (currentPercent < 60) {
          currentPercent += 1;

          // Set the progress directly - simpler and more reliable
          setTrainingProgress(currentPercent / 100);
        } else {
          clearInterval(progressInterval);
        }
      }, 350); // Even faster update for quicker progress
    } else {
      setTrainingProgress(0);
      setProgressText('');
    }

    return () => {
      clearInterval(progressInterval);
    };
  }, [isTraining]);

  useEffect(() => {
    if (tasks[TASK_TYPES.TRAINING]) {
      tasks[TASK_TYPES.TRAINING]?.forEach((task) => {
        if (task.id === taskId && task.status === 'SUCCESS') {
          // Continue the smooth progression from 60% to 100%
          let currentPercent = Math.round(trainingProgress * 100);
          let completionInterval;

          // Start the smooth completion animation - simple and reliable
          completionInterval = setInterval(() => {
            if (currentPercent < 100) {
              currentPercent += 1;

              // Set the progress directly - simpler and more reliable
              setTrainingProgress(currentPercent / 100);

              // When we reach 100%, schedule closing
              if (currentPercent === 100) {
                setTimeout(() => {
                  setIsTraining(false);
                  setTaskId(null);
                  refetch();
                }, 1500);

                // Clear the interval when we reach 100%
                clearInterval(completionInterval);
              }
            }
          }, 350); // Same timing as the progress animation for consistency

          // Safety cleanup after a reasonable time
          setTimeout(() => clearInterval(completionInterval), 15000);

        } else if (task.id === taskId && task.status === 'FAILURE') {
          // Show error state but maintain the current progress
          setTimeout(() => {
            setIsTraining(false);
            setTaskId(null);

            // Show backend error message from task result or fallback to generic message
            const errorMessage = (typeof task.result === 'string' && task.result) || 'Training failed. Please try again.';
            addErrorToast({ message: errorMessage });
          }, 1500);
          console.error(`Task ${task.id} failed:`, task.result);
        } else if (task.id === taskId && task.status === 'ERROR') {
          setTimeout(() => {
            setIsTraining(false);
            setTaskId(null);

            // Show backend error message from task result or fallback to generic message
            const errorMessage = (typeof task.result === 'string' && task.result) || 'Training encountered an error. Please try again.';
            addErrorToast({ message: errorMessage });
          }, 1500);
          console.error(`Task ${task.id} encountered an error:`, task.result);
        } else if (task.id === taskId && task.status === 'PENDING') {
          // Don't update progress here - let the smooth increment effect handle it
        }
      });
    }
  }, [tasks, taskId, trainingProgress]);

  if (!data || dataLoading || customizationLoading) {
    return <DLoading show={true} />;
  }

  return (
    <>
      <LayoutRightSidebar
        RightSidebar={() => (
          <div className={COMMON_CLASSNAMES.previewBubble}>
            <StyleTag tag=".bubble" tempCustomizationData={customizationData} />
            <Bubble
              type="chatbot"
              config={{
                ...customizationData,
                public: true,
                home_tab_enabled: false,
                remove_watermark: true,
              }}
              isPreviewMode={true}
              suggestionPrompts={customizationData?.prompt_suggestions}
            />
            {/* <DAlert state="positive">
                Interact with the chat bubble preview to ensure all the
                information is displayed correctly
              </DAlert> */}
          </div>
        )}
      >
        {() => (
          <LayoutWithButtons className="relative">
            <div className="flex flex-col gap-size5">
              <div className="flex gap-size1">
                <DInput
                  placeholder="Search.."
                  iconPlacement="pre"
                  icon={<SearchIcon />}
                  onChange={(e) => handleSearch(e.target.value, 'name')}
                />
                <Menu>
                  <MenuButton>
                    <FilterIcon />
                  </MenuButton>
                  <MenuItems
                    anchor="bottom end"
                    className="bg-white shadow-sm p-size2 rounded-size1 border border-grey-5 w-72 flex flex-col gap-size2"
                  >
                    <MenuItem
                      onClick={() => handleSearch('', 'name')}
                      className="cursor-pointer"
                    >
                      <p className="text-sm font-light">
                        Name (Alphabetically)
                      </p>
                    </MenuItem>
                    <MenuItem
                      onClick={() => handleSearch('', 'size_ascending')}
                      className="cursor-pointer"
                    >
                      <p className="text-sm font-light">
                        Size (From small to large)
                      </p>
                    </MenuItem>
                    <MenuItem
                      onClick={() => handleSearch('', 'size_descending')}
                      className="cursor-pointer"
                    >
                      <p className="text-sm font-light">
                        Size (From large to small)
                      </p>
                    </MenuItem>
                    <MenuItem
                      onClick={() => handleSearch('', 'character_ascending')}
                      className="cursor-pointer"
                    >
                      <p className="text-sm font-light">
                        Character count (small to large)
                      </p>
                    </MenuItem>
                    <MenuItem
                      onClick={() => handleSearch('', 'character_descending')}
                      className="cursor-pointer"
                    >
                      <p className="text-sm font-light">
                        Character count (large to small)
                      </p>
                    </MenuItem>
                  </MenuItems>
                </Menu>
              </div>

              <div className="relative overflow-x-auto shadow-md sm:rounded-lg">
                <table className="w-full table-fixed text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                  <thead className="text-sm font-regular text-black py-size2 border-b border-grey-5">
                    <tr>
                      <th className="w-[44px] md:w-[62px] p-2 md:p-4">
                        <div className="flex items-center">
                          <DCheckbox
                            checked={
                              selectedFiles.length ===
                              data?.results?.files?.length
                            }
                            onChange={(e) => handleSelectAll(e)}
                          />
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-1 py-3 font-regular  md:w-3/5 max-w-full "
                      >
                        File name
                      </th>
                      <th
                        scope="col"
                        className="hidden md:table-cell px-1 py-3 font-regular  text-end"
                      >
                        Upload Date
                      </th>
                      <th
                        scope="col"
                        className="px-1 py-3 w-[44px] md:w-[62px] text-center"
                      >
                        <button
                          onClick={() => {
                            if (selectedFiles.length > 0) {
                              setOpenDeleteFiles(true);
                            } else {
                              addErrorToast({ message: 'Please select at least one file to delete' });
                            }
                          }}
                        >
                          <DeleteIcon />
                        </button>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredFiles?.map((file, index) => (
                      <tr
                        className="bg-white hover:bg-grey-2 dark:hover:bg-gray-600"
                        key={index}
                      >
                        <td className="w-[30px] p-2 md:p-4">
                          <div className="flex items-center w-full">
                            <DCheckbox
                              checked={selectedFiles.includes(file.id)}
                              onChange={() => handleSelectFile(file.id)}
                            />
                          </div>
                        </td>
                        <td
                          scope="row"
                          className="px-1 py-3 font-medium text-gray-900 whitespace-nowrap max-w-3/5 w-[70px] md:w-3/5"
                        >
                          <div className="flex gap-size1 items-center">
                            <div className="bg-grey-2 rounded-full w-8 h-8 flex items-center justify-center">
                              {file.type === 'URL' ? (
                                <LinkIcon className="text-purple-200" />
                              ) : (
                                <FilesIcon className="text-purple-200" />
                              )}
                            </div>
                            <div className="flex flex-col max-w-2/5 md:max-w-3/5 w-[160px] md:w-4/5">
                              <p className="text-sm truncate text-black">
                                {file.name}
                              </p>
                              <p className="text-grey-50 truncate">
                                {file.num_chars} characters
                              </p>
                            </div>
                          </div>
                        </td>
                        <td className="hidden md:table-cell px-1 py-4 w-1/5 text-end">
                          <p className="text-grey-50 text-xs">
                            {DateTime.fromISO(file.date_created).toFormat(
                              'd MMM yyyy'
                            )}
                          </p>
                        </td>
                        <td className="px-1 py-4 w-[44px] md:w-[62px] text-center">
                          <button
                            className={`border rounded-size1 p-size1 ${
                              selectedFiles.includes(file.id)
                                ? 'border-black'
                                : 'border-grey-5'
                            }`}
                            onClick={() => {
                              setOpenDeleteFile(true);
                              setSelectedFile(file.id);
                            }}
                          >
                            <DeleteIcon
                              className={`${
                                selectedFiles.includes(file.id)
                                  ? 'text-black'
                                  : 'text-grey-50'
                              }`}
                            />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {totalPages > 1 && (
                <div className="flex justify-center items-center gap-size1">
                  <DButtonIcon
                    variant="outlined"
                    onClick={() => {
                      handlePagination(
                        (currentPage - 1) * itemsPerPage,
                        itemsPerPage
                      );
                      setCurrentPage(currentPage - 1);
                    }}
                    disabled={currentPage === 0}
                  >
                    <ChevronLeftIcon />
                  </DButtonIcon>
                  {getPaginationRange().map((page, index) => {
                    if (page === '...') {
                      return (
                        <span
                          key={index}
                          className="rounded-size0 p-size0 size-8"
                        >
                          …
                        </span>
                      );
                    }
                    return (
                      <button
                        key={index}
                        onClick={() => {
                          handlePagination(
                            (page - 1) * itemsPerPage,
                            itemsPerPage
                          );
                          setCurrentPage(page - 1);
                        }}
                        className={`rounded-size0 p-size0 size-8 ${
                          currentPage === page - 1 ? 'bg-grey-2' : 'bg-white'
                        }`}
                      >
                        {page}
                      </button>
                    );
                  })}
                  <DButtonIcon
                    variant="outlined"
                    onClick={() => {
                      handlePagination(
                        (currentPage + 1) * itemsPerPage,
                        itemsPerPage
                      );
                      setCurrentPage(currentPage + 1);
                    }}
                    disabled={currentPage === totalPages - 1}
                  >
                    <ChevronRightIcon />
                  </DButtonIcon>
                </div>
              )}
              {!data?.results?.knowledge_base?.vectordb_indexed &&
                data?.results?.files?.length > 0 && (
                  <DButton
                    variant="dark"
                    size="sm"
                    className="!w-32"
                    onClick={() => handleRetrain()}
                  >
                    Retrain
                  </DButton>
                )}
              {shouldShowAutoUpdateButton() && (
                <div className="flex flex-col gap-size3 mb-size2">
                  <DCheckbox
                    label="Auto refresh"
                    checked={autoRefreshEnabled}
                    onChange={() => handleToggleAutoRefresh()}
                  />
                  {autoRefreshEnabled && (
                    <DAlert state="alert">
                      <p className="text-sm tracking-tight">
                        Your AI Chatbot is set to retrain every 24 hours to
                        incorporate any new data. If there are no updates in the
                        conversation or memory for 45 days, the auto-refresh
                        feature will be disabled.
                      </p>
                    </DAlert>
                  )}
                </div>
              )}
              <div className="flex gap-size1 mb-size2">
                <DButton
                  variant="dark"
                  className="!w-full !h-10 flex items-center gap-size1"
                  onClick={() => {
                    setOpenAddContent(true);
                    resetChatbotData();
                  }}
                >
                  <AddIcon />
                  Add content
                </DButton>
                <DButton
                  variant="grey"
                  className="!w-full !h-10 flex items-center gap-size1"
                  onClick={() => handleExportChatbotCSV()}
                >
                  <UpRightIcon />
                  Export as CSV
                </DButton>
              </div>
            </div>
            {isTraining && (
              <div className="loading-overlay absolute top-0 left-0 w-full h-full bg-white/50 backdrop-blur-sm flex flex-col items-center overflow-hidden">
                <div className="flex flex-col items-center max-h-full mt-[20%]">
                  <DLoaderTraining size={400} />
                  <div className="mt-size2 flex flex-col items-center">
                    <p className="training-message text-xl tracking-tight text-center transition-opacity duration-500 ease-in-out">{trainingMessage}</p>
                    <div className="mt-size3 max-w-[400px] w-full flex flex-col items-center justify-center gap-size2">
                      <div className="w-full h-1.5 bg-grey-5 rounded-full overflow-hidden">
                        <div
                          className="relative h-full bg-purple-200 rounded-full"
                          style={{ width: `${trainingProgress * 100}%`, transition: 'width 250ms linear' }}
                        >
                          <div className="absolute inset-0 w-full h-full overflow-hidden">
                            <div className="absolute inset-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                          </div>
                        </div>
                      </div>
                      <p className="text-grey-50 text-sm tracking-tight">
                        {Math.round(trainingProgress * 100)}%
                      </p>
                      {/* Removed the progress text that appears above Stop Training button */}
                    </div>
                    <div className="flex flex-col gap-size2 mt-size4">
                      <div className="flex gap-size1 mb-size2">
                        <DButton
                          variant="dark"
                          size="sm"
                          className="!w-full !h-10 flex items-center gap-size1"
                          onClick={() => handleCancelTraining()}
                          loading={trainingLoading}
                        >
                          Stop Training
                        </DButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </LayoutWithButtons>
        )}
      </LayoutRightSidebar>
      <AddContent
        isOpen={openAddContent}
        onClose={() => setOpenAddContent(false)}
        refetch={refetch}
        setOpenRetrain={setOpenRetrain}
        setAddedContent={setAddedContent}
      />
      <DModal
        isOpen={openRetrain}
        onClose={() => setOpenRetrain(false)}
        title="Retrain chatbot"
        hideCloseButton={true}
        closeBackdrop={false}
        footer={
          <DButton variant="danger" className="!w-full" onClick={handleRetrain}>
            Retrain
          </DButton>
        }
      >
        <p className="text-sm tracking-tight">
          Note: You have to retrain your AI Chatbot to apply the changes you
          made to your content.
        </p>
      </DModal>
      <DConfirmationModal
        open={openDeleteFile}
        onClose={() => setOpenDeleteFile(false)}
        onConfirm={() => handleDeleteFile()}
        title="Delete file"
        description="Are you sure you want to delete this file?"
        confirmText="Delete"
        cancelText="Cancel"
        loading={isProcessing}
        variantConfirm="danger"
      />
      <DConfirmationModal
        open={openDeleteFiles}
        onClose={() => setOpenDeleteFiles(false)}
        onConfirm={() => handleDeleteFiles()}
        title={selectedFiles.length === data?.results?.files?.length
          ? 'Delete all AI Chatbot files'
          : 'Delete selected AI Chatbot files'}
        description={selectedFiles.length === data?.results?.files?.length
          ? 'Are you sure you want to delete all AI Chatbot files?'
          : 'Are you sure you want to delete the selected AI Chatbot files?'}
        confirmText="Delete"
        cancelText="Cancel"
        loading={isProcessing}
        variantConfirm="danger"
      />
    </>
  );
};

export default ChatbotKnowledge;
