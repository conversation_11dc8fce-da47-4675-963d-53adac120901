import React from 'react';
import PropTypes from 'prop-types';
import RealTimeVoiceIcon from '@/components/Global/Icons/RealTimeVoiceIcon';
import clsx from 'clsx';

/**
 * RealTimeVoiceButton component - A button for activating real-time voice features
 * @param {Object} props
 * @param {Function} props.onClick - Function to call when button is clicked
 * @param {boolean} props.disabled - Whether the button is disabled
 * @param {string} props.className - Additional CSS classes
 * @returns {JSX.Element}
 */
const RealTimeVoiceButton = ({ 
  onClick, 
  disabled = false,
  className = '',
  ...props 
}) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={clsx(
        'inline-flex h-[42px] w-[42px] py-3 px-3 items-center justify-center',
        'rounded-full transition-colors duration-200',
        'bg-btn hover:bg-hover',
        'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-20',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      {...props}
    >
      <RealTimeVoiceIcon className="w-6 h-6 new-text-accent flex-shrink-0" />
    </button>
  );
};

RealTimeVoiceButton.propTypes = {
  onClick: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  className: PropTypes.string,
};

export default RealTimeVoiceButton; 