import React, { useState, useEffect } from 'react';

const TypingText = ({ 
  text, 
  isTyping = true, 
  typingSpeed = 50, 
  className = '',
  onTypingComplete = () => {} 
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (!isTyping) {
      setDisplayedText(text);
      return;
    }

    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayedText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, typingSpeed);

      return () => clearTimeout(timer);
    } else if (currentIndex === text.length && displayedText === text) {
      onTypingComplete();
    }
  }, [currentIndex, text, isTyping, typingSpeed, onTypingComplete, displayedText]);

  // Reset when text changes
  useEffect(() => {
    if (isTyping) {
      setDisplayedText('');
      setCurrentIndex(0);
    }
  }, [text, isTyping]);

  return (
    <p className={`${className} animate-fadein`}>
      {displayedText}
      {isTyping && currentIndex < text.length && (
        <span className="animate-pulse">|</span>
      )}
    </p>
  );
};

export default TypingText;
