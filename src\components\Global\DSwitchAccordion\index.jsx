import React from 'react';
import DSwitch from '../DSwitch';
import DTooltip from '../DTooltip';
import InfoIcon from '../Icons/InfoIcon';
import DTransition from '../DTransition';

const DSwitchAccordion = React.memo(
  ({ title, children, tooltip, tooltipContent, switchOpen, onToggle, titleClassName, extraContent, disabled = false }) => {
    return (
      <div className="rounded-lg">
        <button
          aria-expanded={switchOpen}
          disabled={disabled}
          className={`dbutton flex w-full items-center justify-between bg-grey-2 p-size2 rounded-size1 ${
            switchOpen
              ? 'bg-gradient-to-r from-purple-300/10 to-transparent'
              : ''
          } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={() => !disabled && onToggle(!switchOpen)}
        >
          <div className="flex items-center gap-size1">
            <span className={`text-lg tracking-tight font-medium ${titleClassName} ${disabled ? 'opacity-50' : ''}`}>{title}</span>
            {tooltip && (
              <DTooltip content={tooltipContent}>
                <InfoIcon className="w-3 h-3" />
              </DTooltip>
            )}
          {extraContent && (
            <div className="flex items-center gap-size1">
              {extraContent}
            </div>
          )}
          </div>
          <DSwitch
            checked={switchOpen}
            disabled={disabled}
            onChange={() => !disabled && onToggle(!switchOpen)}
          />
        </button>
        {/* {switchOpen && <div className="p-size2">{children}</div>} */}
        <DTransition
          show={switchOpen && !disabled}
          onInitialMount={switchOpen && !disabled}
          type="collapse"
        >
          <div className={`py-size2 ${disabled ? 'opacity-50 pointer-events-none' : ''}`}>{children}</div>
        </DTransition>
      </div>
    );
  }
);

DSwitchAccordion.displayName = 'DSwitchAccordion';

export default DSwitchAccordion;
