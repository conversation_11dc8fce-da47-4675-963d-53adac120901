import { Suspense } from 'react';
import { Navigate } from 'react-router-dom';
import { useUserStore } from '@/stores/user/userStore';
import PageViewTracker from '@/components/PageViewTracker';
import RouteChangeHandler from '@/components/RouteChangeHandler';

const PublicRoute = ({ permission, children }) => {
  const { auth, user } = useUserStore();

  return auth.access_token && !user.should_redirect ? (
    <Navigate to="/" replace />
  ) : (
    <>
      <PageViewTracker />
      <RouteChangeHandler />
      <Suspense>{children}</Suspense>
    </>
  );
};

export default PublicRoute;
