/**
 * Frame Protection Utility
 * 
 * Protects against clickjacking attacks by detecting if the application
 * is loaded in an iframe and blocking unauthorized embedding.
 * 
 * Allows legitimate embed routes while protecting sensitive pages.
 */

/**
 * Check if the current page is loaded in an iframe
 */
export const isInIframe = () => {
  try {
    return window.self !== window.top;
  } catch (e) {
    // If we can't access window.top due to cross-origin restrictions,
    // we're definitely in an iframe
    return true;
  }
};

/**
 * Check if the current route is allowed to be embedded
 */
export const isEmbedAllowedRoute = () => {
  const pathname = window.location.pathname;
  
  // Allowed embed routes
  const allowedRoutes = [
    '/embed',           // Main embed route
    '/embed/',          // Main embed route with trailing slash
    '/embed/bubble',    // Bubble chat
    '/embed/tooltips',  // Tooltip prompts  
    '/embed/demo',      // Demo functionality
    '/share',           // Share routes
    '/share/',          // Share routes with trailing slash
  ];
  
  // Check exact matches
  if (allowedRoutes.includes(pathname)) {
    return true;
  }
  
  // Check if path starts with allowed patterns
  const allowedPatterns = [
    '/embed/',
    '/share/',
  ];
  
  return allowedPatterns.some(pattern => pathname.startsWith(pattern));
};

/**
 * Check if the current route should be protected from embedding
 */
export const isProtectedRoute = () => {
  const pathname = window.location.pathname;
  
  // Protected authentication and sensitive routes
  const protectedRoutes = [
    '/log-in',
    '/sign-up', 
    '/new-sign-up',
    '/forgot-password',
    '/reset-password',
    '/accept-organization-invite',
    '/onboarding',
    '/chatbot',
    '/integrations',
    '/settings',
    '/billing',
    '/team',
    '/analytics',
    '/human-handover',
    '/ai-voice',
    '/first-time-setup',
  ];
  
  // Check exact matches
  if (protectedRoutes.includes(pathname)) {
    return true;
  }
  
  // Check if path starts with protected patterns
  const protectedPatterns = [
    '/onboarding/',
    '/chatbot/',
    '/integrations/',
    '/settings/',
    '/billing/',
    '/team/',
    '/analytics/',
    '/human-handover/',
    '/ai-voice/',
  ];
  
  return protectedPatterns.some(pattern => pathname.startsWith(pattern));
};

/**
 * Get the parent domain if in iframe (for logging/debugging)
 */
export const getParentDomain = () => {
  try {
    return window.top.location.hostname;
  } catch (e) {
    // Cross-origin restriction - can't access parent domain
    return 'unknown-domain';
  }
};

/**
 * Block the page if it's embedded inappropriately
 */
export const blockUnauthorizedEmbedding = () => {
  if (!isInIframe()) {
    // Not in iframe, allow normal operation
    return false;
  }
  
  if (isEmbedAllowedRoute()) {
    // This is a legitimate embed route, allow it
    return false;
  }
  
  if (isProtectedRoute()) {
    // This is a protected route being embedded, block it
    console.warn('Unauthorized embedding attempt detected on protected route:', window.location.pathname);
    
    // Replace page content with warning
    document.body.innerHTML = `
      <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: #f8f9fa;
        color: #333;
        text-align: center;
        padding: 20px;
      ">
        <div style="
          background: white;
          padding: 40px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          max-width: 400px;
        ">
          <h2 style="color: #e74c3c; margin-bottom: 20px;">Access Restricted</h2>
          <p style="margin-bottom: 20px; line-height: 1.5;">
            This page cannot be displayed in a frame for security reasons.
          </p>
          <a href="${window.location.href}" target="_blank" style="
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
          ">
            Open in New Window
          </a>
        </div>
      </div>
    `;
    
    return true;
  }
  
  // For other routes, allow but log for monitoring
  console.info('Page loaded in iframe:', window.location.pathname);
  return false;
};

/**
 * Initialize frame protection
 * Should be called early in the application lifecycle
 */
export const initFrameProtection = () => {
  // Only run in browser environment
  if (typeof window === 'undefined') {
    return;
  }
  
  // Block unauthorized embedding
  const wasBlocked = blockUnauthorizedEmbedding();
  
  if (wasBlocked) {
    // Stop further script execution
    return;
  }
  
};

/**
 * Check if current environment allows embedding
 * (for development/testing purposes)
 */
export const isDevelopmentEnvironment = () => {
  return window.location.hostname === 'localhost' || 
         window.location.hostname.includes('127.0.0.1') ||
         window.location.hostname.includes('.local');
};
