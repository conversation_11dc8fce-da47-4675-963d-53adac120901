import * as authService from '@/services/auth.service';
import { useUserStore } from '@/stores/user/userStore';
import * as userService from '@/services/user.service';
import getUserInfo from '../user/getUserInfo';
import { updateUserDataLayer, trackLogin } from '@/helpers/analytics';

// Helper function to pad numbers with leading zeros
const padZero = (num) => {
  return num.toString().padStart(2, '0');
};

// Format date including time (for dates already in UTC from backend)
const formatDateWithTime = (date) => {
  return `${padZero(date.getMonth() + 1)}/${padZero(date.getDate())}/${date.getFullYear()} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`;
};

// Helper function to format date with time in UTC (for new Date() calls)
const formatDateWithTimeUTC = (date) => {
  return `${padZero(date.getUTCMonth() + 1)}/${padZero(date.getUTCDate())}/${date.getUTCFullYear()} ${padZero(date.getUTCHours())}:${padZero(date.getUTCMinutes())}:${padZero(date.getUTCSeconds())}`;
};

const loginGoogleUseCase = async ({
  access_token,
  rewardful_referral,
  contact_consent,
  kb_id,
  isLanding
}) => {
  const saveAuthDetail = useUserStore.getState().saveAuthDetail;
  const setUser = useUserStore.getState().setUser;


  try {
    const response = await authService.loginWithGoogle({
      access_token,
      rewardful_referral,
      contact_consent,
      kb_id,
      isLanding
    });
    let userInfo;

    const res = response.data;

    if (response.status === 200) {
      const auth = {};
      auth.access_token = res.access_token;

      const replaceUrlResponse = await userService.replaceUrl({
        from_new_design: true,
        access_token: res.access_token
      });

      if(replaceUrlResponse.status === 200){
        if(replaceUrlResponse.data.redirect === true){
          setUser({ should_redirect: true });
          window.location.href = replaceUrlResponse.data.redirect_url;
          return;
        }
      }
      saveAuthDetail(auth);

      userInfo = await getUserInfo(res.access_token);
      const onboardingStatus = await userService.getOnboardingStatus({ access_token: res.access_token });

      auth.user_id = userInfo.id;
      auth.first_name = userInfo.first_name;
      auth.last_name = userInfo.last_name;
      auth.email = userInfo.email;
      auth.date_created = userInfo.date_created;
      auth.login_count = userInfo.login_count;

      // if(onboardingStatus.data.onboarding_completed){
      //   window.open('/' ,'_self');
      // }else{
      //   window.open('/onboarding/create' ,'_self');
      // }

      saveAuthDetail(auth);
      setUser(userInfo);

      // Format previous login date if available
      let previousLoginDate = '';
      if (res.previous_login_date) {
        const prevLogin = new Date(res.previous_login_date);
        previousLoginDate = formatDateWithTime(prevLogin);
      }

      // Track login event
      trackLogin({
        email: userInfo.email,
        total_logins: userInfo.login_count,
        last_login_date: previousLoginDate
      });

      // Update dataLayer with user information
      updateUserDataLayer(userInfo, auth);

      return { data: { ...res, ...userInfo } };
    }

    return {};
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export default loginGoogleUseCase;
