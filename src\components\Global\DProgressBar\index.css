/* DProgressBar responsive styles */
.progress-bar-wrapper {
  /* Fixed width under 1600px, full width above */
  width: 500px;
  min-width: 0;
}

/* 150% zoom handling - width 250px */
@media screen and (min-resolution: 144dpi) and (max-resolution: 191dpi) {
  .progress-bar-wrapper {
    width: 250px !important;
  }
}

@media (min-width: 1600px) {
  .progress-bar-wrapper {
    width: 100%;
  }
}

.progress-bar-container {
  /* Enable horizontal scrolling on smaller screens */
  @media (max-width: 1599px) {
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: thin;
    scrollbar-color: #e5e7eb transparent;
  }

  /* Hide scrollbar on larger screens */
  @media (min-width: 1600px) {
    overflow-x: visible;
  }
}

/* Custom scrollbar styling for webkit browsers */
.progress-bar-container::-webkit-scrollbar {
  height: 4px;
}

.progress-bar-container::-webkit-scrollbar-track {
  background: transparent;
}

.progress-bar-container::-webkit-scrollbar-thumb {
  background-color: #e5e7eb;
  border-radius: 2px;
}

.progress-bar-container::-webkit-scrollbar-thumb:hover {
  background-color: #d1d5db;
}

/* Ensure progress bar content maintains proper spacing */
.progress-bar-content {
  min-width: max-content;
}

/* Responsive step widths */
.progress-step {
  min-width: 120px;
}

@media (min-width: 640px) {
  .progress-step {
    min-width: 140px;
  }
}

@media (min-width: 1024px) {
  .progress-step {
    min-width: 160px;
  }
}

@media (min-width: 1600px) {
  .progress-step {
    min-width: 120px;
  }
}
