import { DEFAULT_HEADERS } from './constants.service';
import http from './http';

export const startOnboarding = (url, captcha_token) => {
    return http.post(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/v2/onboarding', {
        url,
        captcha_token
    }, {
        headers: DEFAULT_HEADERS
    })
}

export const claimOnboarding = (email, user_id, marketing_consent) => {
    return http.post(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/v2/onboarding/claim', {
        email,
        user_id,
        contact_consent: marketing_consent
    }, {
        headers: DEFAULT_HEADERS
    })
}

export const verifyOnboarding = (user_id, verification_code, new_password, role, url, marketing_consent) => {
    return http.post(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/v2/onboarding/verify', {
        user_id,
        verification_code,
        new_password,
        company_role: role?.id || role, // Send role.id if role is an object, otherwise send role as-is
        url: url,
        contact_consent: marketing_consent
    }, {
        headers: DEFAULT_HEADERS
    })
}

export const verifyOnboardingWithGoogle = ({user_id, access_token, url, role, marketing_consent}) => {
    return http.post(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/v2/onboarding/google', {
        user_id,
        access_token: access_token,
        company_role: role?.id || role, // Send role.id if role is an object, otherwise send role as-is
        url: url,
        contact_consent: marketing_consent
    }, {
        headers: DEFAULT_HEADERS
    })
}

export const confirmUrl = (url) => {
    return http.post(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/v2/onboarding/confirm-url', {
        url
    }, {
        headers: DEFAULT_HEADERS
    })
}
