import { Transition } from '@headlessui/react';
import DLoaderTraining from '../Global/DLoaderTraining';

const DLoading = ({ show = false, style = {}, height = 'h-screen' }) => {
  return (
    <Transition show={show}>
      <div className={`transition data-[closed]:opacity-0 duration-300 ${height} w-full flex items-center justify-center`} style={style}>
        <DLoaderTraining />
      </div>
    </Transition>
  );
};

export default DLoading;
