import React, { useEffect, useState, useRef } from 'react';
import { DateTime } from 'luxon';

import { CHATBOTS_WITH_NEW_DESIGN, COMMON_CLASSNAMES, DANTE_THEME_CHAT } from '@/constants';
import transformLinkUri from '@/helpers/transformLinkUri';
import {
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  Transition,
} from '@headlessui/react';

import DButtonIcon from '@/components/Global/DButtonIcon';
import DShapeLogo from '@/components/Global/DLogo/DShapeLogo';
import DSuggestionPrompt from '@/components/Global/DSuggestionPrompt';
import CloseIcon from '@/components/Global/Icons/CloseIcon';
import ConversationsIcon from '@/components/Global/Icons/ConversationsIcon';
import OptionsIcon from '@/components/Global/Icons/OptionsIcon';
import ReturnIcon from '@/components/Global/Icons/ReturnIcon';
import SendIcon from '@/components/Global/Icons/SendIcon';
import LLMSelector from '../../LLMSelector';
import PoweredByDante from '../../PoweredByDante';
import ChatListMessages from '../ChatListMessages';
import ChevronDownIcon from '@/components/Global/Icons/ChevronDownIcon';
import AiVoiceIcon from '@/components/Global/Icons/AiVoiceIcon';
import MicrophoneIcon from '@/components/Global/Icons/MicrophoneIcon';
import clsx from 'clsx';
import AudioRecorder from '@/components/AudioRecorder';
import DButton from '@/components/Global/DButton';
import AiChatbotIcon from '@/components/Global/Icons/AiChatbotIcon';
import MenuQuickResponse from '../MenuQuickResponse';
import ConfirmIcon from '@/components/Global/Icons/ConfirmIcon';
import MarkAsResovledIcon from '@/components/Global/Icons/MarkAsResovledIcon';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';
import AttachmentIcon from '@/components/Global/Icons/AttachmentIcon';
import UploadedImagePreview from '@/components/Global/UploadedImagePreview';
import { saveChatbotImages } from '@/services/customization.service';
import DLoading from '@/components/DLoading';
import DModalEmailTransaction from '@/components/DModalEmailTransaction';
import DTooltip from '@/components/Global/DTooltip';

export const Chat = ({
  config,
  chatContainerRef,
  isInApp,
  isPreviewMode,
  showInAppHeader = false,
  isDanteFaq = false,
  handleSendQuestion,
  hiddenConversation = false,
  hiddenPoweredByDante = false,
  handleCloseButton = () => {},
  handleOpenDanteConversations = () => {},
  handleOpenDanteFaq = () => {},
  handleOpenLiveAgent = () => {},
  showCloseBtn = false,
  showMenuBtn = false,
  isAnswerLoading = false,
  showButtons = true,
  dynamicButtons,
  hideFooter = false,
  handleFooterButton,
  showConsent = false,
  setShowConsent = () => {},
  hasMic = false,
  showSuggestionPrompts = false,
  isLoadingSuggestions = false,
  sources,
  openSources,
  setOpenSources,
  showSources,
  sourcesLoading,
  chatImageLoader,
  isInHumanHandoverApp = false,
  humanHandoverConfig = {},
  humanHandoverMarkResolved = () => {},
  humanHandoverTakeConversation = () => {},
  setQuestionIsQuickResponse = () => {},
  interactingWithLiveAgent = false,
  isLiveAgentResolved = false,
  pollResponse = false,
  messagesLoading = false,
  handleShowVoice = () => { },
  isPreviousConversationHasLiveAgent = false,
  showEmailTranscript = false,
  setShowEmailTranscript = () => {},
  currectConversationId = null,
  setNotification = () => {},
  isTyping = false,
  typingUser = null,
  onTypingStart = () => {},
  onTypingStop = () => {},
}) => {
  const { openedConversation } = useHumanHandoverStore((state) => state);
  const [showConversation, setShowConversation] = useState(!hiddenConversation);
  const [question, setQuestion] = useState('');
  const [isMicrophoneOpen, setIsMicrophoneOpen] = useState(hasMic);
  const [isRecording, setIsRecording] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(!config?.public || false);
  const [uploadedImages, setUploadedImages] = useState([]);
  const [isTextareaFocused, setIsTextareaFocused] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);
  const [isTextareaScrolling, setIsTextareaScrolling] = useState(false);
  const fileInputRef = useRef(null);
  const textareaRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  // Auto-focus only on initial load, not on mobile to prevent cursor jumping
  useEffect(() => {
    const isMobile = window.innerWidth <= 768 || 'ontouchstart' in window;

    if (!isAnswerLoading && textareaRef.current && !hasInteracted && !isMobile) {
      if (document.activeElement !== textareaRef.current) {
        textareaRef.current.focus();
      }
    }
  }, [isAnswerLoading, hasInteracted]);

  // Auto-resize textarea - simplified for mobile stability
  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const isMobile = window.innerWidth <= 768 || 'ontouchstart' in window;

    // Store cursor position to prevent jumping
    const cursorStart = textarea.selectionStart;
    const cursorEnd = textarea.selectionEnd;

    // Temporarily disable transition for measurement
    textarea.style.transition = 'none';
    textarea.style.height = 'auto';

    const computedStyle = window.getComputedStyle(textarea);
    let lineHeight = parseFloat(computedStyle.lineHeight);
    if (isNaN(lineHeight)) {
      lineHeight = parseFloat(computedStyle.fontSize) * 1.2;
    }

    const lines = Math.ceil(textarea.scrollHeight / lineHeight);
    const maxHeight = lineHeight * 4;
    let newHeight = lines >= 2 ? Math.min(textarea.scrollHeight, maxHeight) : lineHeight;

    // Handle scrolling
    if (textarea.scrollHeight > maxHeight) {
      textarea.style.overflowY = 'auto';
      setIsTextareaScrolling(true);
    } else {
      textarea.style.overflowY = 'hidden';
      setIsTextareaScrolling(false);
    }

    // Apply new height with appropriate transition
    requestAnimationFrame(() => {
      const duration = isMobile ? '0.1s' : '0.25s';
      textarea.style.transition = `height ${duration} ease-out`;
      textarea.style.height = `${newHeight}px`;

      // Restore cursor position if textarea is focused
      if (document.activeElement === textarea) {
        textarea.setSelectionRange(cursorStart, cursorEnd);
      }
    });
  }, [question]);

  // Cleanup typing state on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current && onTypingStop && (isInHumanHandoverApp || pollResponse)) {
        onTypingStop();
        typingTimeoutRef.current = null;
      }
    };
  }, [isInHumanHandoverApp, pollResponse, onTypingStop]);

  // Helper function to determine if message sending should be disabled
  const isMessageSendingDisabled = () => {
    // Standard restrictions
    if (isPreviewMode || isAnswerLoading) {
      return true;
    }

    // Human Handover specific restrictions
    if (isInHumanHandoverApp && humanHandoverConfig?.isTaken) {
      // If conversation is taken but user is not assigned to it, disable sending
      return !humanHandoverConfig?.isAssignedToCurrentUser;
    }

    return false;
  };

  const handleSend = async (tempQuestion = '') => {
    // Check if message sending is disabled
    if (isMessageSendingDisabled()) {
      return;
    }

    if ((question.trim().length > 0 || tempQuestion.trim().length > 0 || uploadedImages.length > 0)) {
      setHasInteracted(true);
      
      // Process images if there are any
      let imageUrls = [];
      if (uploadedImages.length > 0 && config?.kb_id) {
        // Create FormData to upload images
        const formData = new FormData();
        uploadedImages.forEach((image, index) => {
          if (typeof image !== 'string') {
            formData.append('file', image);
          }
        });

        try {
          const response = await saveChatbotImages(config.kb_id, formData);
          if (response.data && response.data.url) {
            imageUrls.push(response.data.url);
          }
        } catch (error) {
          console.error('Error uploading images:', error);
        }
      }

      // Send typing stop event before sending message (both agent and user sides)
      if (onTypingStop && typingTimeoutRef.current && (isInHumanHandoverApp || pollResponse)) {
        onTypingStop();
        typingTimeoutRef.current = null;
      }

      // Send question with image URLs
      handleSendQuestion(tempQuestion || question, imageUrls);
      isDanteFaq && handleOpenDanteFaq();

      setShowConversation(true);
      setQuestion('');
      setUploadedImages([]);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    // Check if message sending is disabled
    if (isMessageSendingDisabled()) {
      return;
    }

    setHasInteracted(true);
    handleSendQuestion(suggestion.content);
  };

  const handleOpenMicrophone = () => {
    setIsMicrophoneOpen(true);
  };

  const handleRecordingComplete = (audioBlob) => {
    setIsMicrophoneOpen(false);
  };

  const handleImageUpload = (event) => {
    if (event.target.files && event.target.files.length > 0) {
      // Add new images to the array
      const newImages = Array.from(event.target.files);
      setUploadedImages(prev => [...prev, ...newImages]);

      // Reset file input so the same file can be selected again
      if (fileInputRef.current) {
        fileInputRef.current.value = null;
      }
    }
  };

  const handleRemoveImage = (indexToRemove) => {
    setUploadedImages(prev => prev.filter((_, index) => index !== indexToRemove));
  };

  const isLiveAgentAvailable = () => {
    if (!config?.talk_to_live_agent) {
      return false;
    }

    if (config?.live_agent_always_active) {
      return true;
    }

    const schedule = config?.live_agent_list_of_schedules || [];
    
    try {
      // Get the configured timezone or default to UTC
      const timezone = config?.human_handover_timezone || Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone || 'Europe/Belgrade';
      
      // Get current date/time in the configured timezone using Luxon
      const now = DateTime.now().setZone(timezone);
      const currentDay = now.weekdayLong; // Returns full weekday name
      const currentTime = now.toFormat('HH:mm'); // 24-hour format

      const todaySchedule = schedule.find((s) => s.day === currentDay);

      if (todaySchedule && dynamicButtons?.show_live_agent) {

        // Convert schedule times to DateTime objects in the configured timezone for comparison
        const scheduleStart = DateTime.fromFormat(todaySchedule.from, 'HH:mm', { zone: timezone });
        const scheduleEnd = DateTime.fromFormat(todaySchedule.to, 'HH:mm', { zone: timezone });
        const currentDateTime = DateTime.fromFormat(currentTime, 'HH:mm', { zone: timezone });

        return currentDateTime >= scheduleStart && currentDateTime <= scheduleEnd;
      }
    } catch (error) {
      console.error('Error checking live agent availability:', error);
    }

    return false;
  };

  useEffect(() => {
    setShowConversation(!hiddenConversation);
  }, [hiddenConversation]);

  useEffect(() => {
    setIsMicrophoneOpen(hasMic);
  }, [hasMic]);

  useEffect(() => {
    setIsMicrophoneOpen(false);
  }, [config?.show_microphone]);

  const handleQuickResponseClick = (quickResponseContent) => {
    setQuestion(quickResponseContent);
    setQuestionIsQuickResponse(true);
  };

  // Track user interaction and prevent focus issues on mobile
  useEffect(() => {
    const handleManualInteraction = (e) => {
      if (e.target !== textareaRef.current) {
        setHasInteracted(true);
      }
    };

    // Prevent hidden elements from stealing focus on mobile
    const handleFocusChange = (e) => {
      const isMobile = window.innerWidth <= 768 || 'ontouchstart' in window;
      if (isMobile && e.target && e.target.type === 'file') {
        e.preventDefault();
        if (textareaRef.current && isTextareaFocused) {
          textareaRef.current.focus();
        }
      }
    };

    const bubbleElement = document.querySelector('.bubble');
    if (bubbleElement) {
      bubbleElement.addEventListener('click', handleManualInteraction);
      bubbleElement.addEventListener('touchstart', handleManualInteraction);
    }

    document.addEventListener('focusin', handleFocusChange);

    return () => {
      if (bubbleElement) {
        bubbleElement.removeEventListener('click', handleManualInteraction);
        bubbleElement.removeEventListener('touchstart', handleManualInteraction);
      }
      document.removeEventListener('focusin', handleFocusChange);
    };
  }, [isTextareaFocused]);
 

  return (
    <div className="flex-1 min-h-0 h-full relative">
      <div
        className={clsx(
          showConversation &&
            'absolute h-full w-full flex flex-col overflow-hidden'
        )}
      >
        <div className={showConversation ? 'flex h-full' : 'flex'}>
          <div
            className={clsx(
              'flex flex-col grow relative',
              'transition',
              'w-full',
              COMMON_CLASSNAMES.transition.duration.long,
              'data-[enter]:data-enter-active:opacity-0',
              'data-[enter]:delay-200',
              {
                'h-full rounded-size1 gap-size2': showConversation,
                'gap-size1': !showConversation,
                'px-size4 py-size5 ':
                  !isPreviewMode && showInAppHeader && showConversation,
              },
              {'md:max-w-[700px] md:mx-auto': isInHumanHandoverApp}
            )}
            style={{
              color: showConversation
                ? 'var(--dt-color-element-100)'
                : undefined,
            }}
          >
            {showInAppHeader && isInApp && (
              <header className="flex gap-size0 items-center justify-between">
                <div
                  className={`flex items-center gap-size0 ${
                    showConversation ? '' : ''
                  }`}
                >
                  {config?.chatbot_icon && !isDanteFaq ? (
                    <img
                      src={config?.chatbot_icon}
                      alt={config?.name || config?.kb_name}
                      className={`${showConversation ? 'size-8' : 'size-5'}`}
                    />
                  ) : (
                    <DShapeLogo
                      className={`${showConversation ? 'size-8' : 'size-5'}`}
                    />
                  )}
                  {config?.show_chatbot_name  && (
                    <span
                      className={`${
                        showConversation ? 'text-xl' : 'text-base'
                      }  tracking-tight`}
                    >
                      {isDanteFaq ? 'Dante FAQ' : config?.name || config?.kb_name}
                    </span>
                  )}
                </div>
                {showConversation && (
                  <div className="flex items-center gap-size0">
                    {showMenuBtn && !isInApp && (
                      <button className="dbutton border border-grey-5 rounded-size1 p-size1">
                        <OptionsIcon />
                      </button>
                    )}
                    {!isDanteFaq && (
                      <button
                        className="dbutton border border-grey-5 rounded-size1 p-size1"
                        onClick={handleOpenDanteConversations}
                      >
                        <ConversationsIcon className="w-[18px] h-[18px]" />
                      </button>
                    )}
                    {showCloseBtn && (
                      <button
                        className="dbutton bg-grey-2 rounded-size1 p-size1"
                        onClick={handleCloseButton}
                      >
                        <CloseIcon className="w-[18px] h-[18px]" />
                      </button>
                    )}
                  </div>
                )}
              </header>
            )}
            <Transition
              show={config?.notification?.show}
              enter="transition-all duration-300"
              enterFrom="-translate-x-full opacity-0" 
              enterTo="translate-x-0 opacity-100"
              leave="transition-all duration-300"
              leaveFrom="translate-x-0 opacity-100"
              leaveTo="-translate-x-full opacity-0"
            >
              <div className="absolute top-0 left-0 right-0 z-50 bg-white">
                <div
                  className={`transition-all duration-300 data-[closed]:opacity-0 notification-${config?.notification?.type} rounded-size0 w-full`}
                  style={{
                    backgroundColor: 'var(--dt-color-surface-100)',
                  }}
                >
                  <div
                    className={'py-size1 px-size2 flex items-center justify-center text-medium text-sm w-full'}
                    style={{
                      backgroundColor: `var(--dt-color-${config?.notification?.type}-5)`,
                      color: `var(--dt-color-${config?.notification?.type}-100)`,
                    }}
                  >
                    {config?.notification?.message}
                  </div>
                </div>
              </div>
            </Transition>

            {config?.disclaimer_enabled && (!isInApp || isPreviewMode) && (
              <div
                className={'disclaimer flex flex-col gap-size1 items-center text-center'}
              >
                <img
                  src={
                    config?.chatbot_icon instanceof File
                      ? URL.createObjectURL(config?.chatbot_icon)
                      : config?.chatbot_icon || DANTE_THEME_CHAT.chatbot_icon
                  }
                  alt={config?.kb_name}
                  className="size-12"
                />
                {config?.show_chatbot_name && (
                  <p
                    style={{ color: 'var(--dt-color-element-100)' }}
                    className="text-xl"
                  >
                    {config?.kb_name}
                  </p>
                )}
                <span
                  style={{ color: 'var(--dt-color-element-50)' }}
                  className="block text-sm"
                  dangerouslySetInnerHTML={{ __html: config?.disclaimer_text }}
                ></span>
              </div>
            )}
            {showConversation && (
              <Transition show={showConversation} appear={showConversation}>
                <div
                  className={clsx(
                    'conversation transition duration-300 flex flex-col ',
                    {'justify-between': CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id)},
                    'animate-fadeIn',
                    {
                      'grow h-[1px] opacity-100 gap-size5': showConversation,
                      'opacity-0': !showConversation,
                    },
                    {'block overflow-y-auto h-full no-scrollbar': CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id)}
                  )}
                >
                  {messagesLoading ? (
                    <div className="flex-1 flex flex-col items-center justify-center">
                      <DLoading show={true} style={{ height: '100%', width: '100%' }}/>
                    </div>
                  ) : (    
                      <ChatListMessages
                        chatContainerRef={chatContainerRef}
                        messages={config?.messages || []}
                        transformLinkUri={transformLinkUri}
                        chatbot_profile_pic={config?.chatbot_icon}
                        handleFooterButton={handleFooterButton}
                        isDanteFaq={isDanteFaq}
                        isInApp={isInApp}
                        hideFooter={hideFooter}
                        sources={sources}
                        openSources={openSources}
                        setOpenSources={setOpenSources}
                        showSources={showSources}
                        sourcesLoading={sourcesLoading}
                        chatImageLoader={chatImageLoader}
                        isInHumanHandoverApp={isInHumanHandoverApp}
                        interactingWithLiveAgent={interactingWithLiveAgent}
                        openedConversation={openedConversation}
                        pollResponse={pollResponse}
                        showDate={isInHumanHandoverApp}
                        kb_id={config?.kb_id}
                        showButtons={showButtons}
                        dynamicButtons={dynamicButtons}
                        isLiveAgentAvailable={isLiveAgentAvailable}
                        isPreviousConversationHasLiveAgent={isPreviousConversationHasLiveAgent}
                        isAnswerLoading={isAnswerLoading}
                        isPreviewMode={isPreviewMode}
                        handleOpenLiveAgent={handleOpenLiveAgent}
                        showSuggestionPrompts={showSuggestionPrompts}
                        isLoadingSuggestions={isLoadingSuggestions}
                        handleSuggestionClick={handleSuggestionClick}
                        config={config}
                        isTyping={isTyping}
                        typingUser={typingUser}
                      />
                  )}

                  {((dynamicButtons?.show_calendly_url &&
                    dynamicButtons?.show_live_agent) ||
                    isLiveAgentAvailable() ||
                    (config?.suggested_prompts_enabled &&
                      config?.prompt_suggestions?.length > 0 &&
                      showSuggestionPrompts &&
                      !isLoadingSuggestions)) &&
                    showButtons && !isInHumanHandoverApp && !CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) && (
                      <div className="flex gap-size1 grow-0 overflow-x-auto flex-nowrap h-[38px] justify-start flex-shrink-0 no-scrollbar overflow-y-hidden suggested-prompts-container animate-fadeInUp">
                        {dynamicButtons?.show_calendly_url && !isInApp && (
                          <DSuggestionPrompt
                            type="book_meet"
                            disabled={isMessageSendingDisabled()}
                            onClick={() =>
                              window.open(config?.calendly_url, '_blank')
                            }
                            content={
                              config?.calendly_btn_text || 'Book a meeting'
                            }
                          />
                        )}
                        {(dynamicButtons?.show_live_agent &&
                          isLiveAgentAvailable()) && !isPreviousConversationHasLiveAgent && !isInApp && !isAnswerLoading && (
                          <DSuggestionPrompt
                            type="connect_live_agent"
                            content={'Connect to Live Agent'}
                            onClick={handleOpenLiveAgent}
                            disabled={isMessageSendingDisabled()}
                          />
                        )}
                          {config?.suggested_prompts_enabled &&
                            config?.prompt_suggestions?.length > 0 &&
                            showSuggestionPrompts &&
                            !isLoadingSuggestions &&
                            config?.prompt_suggestions.map((suggestion, index) => (
                              <div key={index} className="flex-shrink-0">
                                <DSuggestionPrompt
                                  content={suggestion.content}
                                  onClick={() =>
                                    handleSuggestionClick(suggestion)
                                  }
                                  disabled={isMessageSendingDisabled()}
                                />
                              </div>
                            )
                          )}
                      </div>
                    )}

                  {
                    isInHumanHandoverApp && !isLiveAgentResolved && humanHandoverConfig?.isTaken && humanHandoverConfig?.isAssignedToCurrentUser && (
                      <div className="flex gap-size1 grow-0 overflow-x-auto flex-nowrap h-[38px] justify-end px-size1 flex-shrink-0 no-scrollbar overflow-y-hidden">
                        <DButton variant="resolved" onClick={() => humanHandoverMarkResolved()}>
                          <ConfirmIcon />
                          Mark as resolved
                        </DButton>
                      </div>
                    )
                  }
                </div>
              </Transition>
            )}
            
            <div className={clsx(isInHumanHandoverApp ? 'mx-size1' : '')}>
              {isLiveAgentResolved && isInHumanHandoverApp ? (
                <div className="grow-0 flex justify-center gap-size1 items-center py-size2 text-green-500 whitespace-nowrap">
                  <hr className="w-full border-t border-green-10" />
                  <div className="flex items-center gap-size1">
                    <MarkAsResovledIcon />
                    Agent resolved this
                  </div>
                  <hr className="w-full border-t border-green-10" />
                </div>
              ) : ((!showConsent && !isInHumanHandoverApp) ||
                (isInHumanHandoverApp && humanHandoverConfig?.isTaken)) && (
                <div className="grow-0 flex flex-col gap-size1">
                  {/* Show message when user is not assigned to conversation */}
                  {isInHumanHandoverApp && humanHandoverConfig?.isTaken && !humanHandoverConfig?.isAssignedToCurrentUser && (
                    <div className="flex items-center justify-center py-size2 px-size3 bg-orange-50 border border-orange-200 rounded-size1 text-orange-700 text-sm">
                      <span>This conversation is assigned to another agent. You cannot send messages.</span>
                    </div>
                  )}

                  {(isInHumanHandoverApp || pollResponse) && uploadedImages.length > 0 && (
                    <div className="flex gap-size1 overflow-x-auto py-size1">
                      {uploadedImages.map((image, index) => (
                        <UploadedImagePreview
                          key={index}
                          image={image}
                          onRemove={() => handleRemoveImage(index)}
                        />
                      ))}
                    </div>
                  )}
                   <div className={clsx(
                     'bubble-input-container w-full flex justify-between items-center border mx-auto my-[4px]',
                     'border-[--dt-color-element-10]',
                     isTextareaScrolling ? 'px-3 py-2' : 'px-2 py-1',
                     question.includes('\n') || question.length > 50 ? 'rounded-full' : 'rounded-full',
                     isTextareaFocused ? 'border-[var(--dt-color-brand-100)] ring-[0.5px] ring-[var(--dt-color-brand-100)]' : ''
                   )}
                   style={{
                     transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
                     transformOrigin: 'center',
                     willChange: 'border-radius, border-color, box-shadow, transform'
                   }}>
                     {/* <div className={clsx('bubble-input-container relative rounded-size0 w-full flex flex-col gap-size1 bg-[--dt-color-surface-100] animate-fadeInUpDelayed3 h-[42px] mx-auto my-[4px] px-[2px]',
                    // {"bubble-input-container relative rounded-size0 w-full border border-[--dt-color-element-10] flex flex-col gap-size1 pl-size2 bg-[--dt-color-surface-100] animate-fadeInUpDelayed3 grid py-size1 [&>textarea]:text-inherit after:text-inherit [&>textarea]:resize-none [&>textarea]:overflow-hidden [&>textarea]:[grid-area:1/1/2/2] after:[grid-area:1/1/2/2] after:whitespace-pre-wrap after:invisible   after:content-[attr(data-cloned-val)_'_'] after:border after:max-h-16 pr-[64px]": (config?.kb_id !== 'ca7ac107-9716-4ef3-a6b5-55ceab49f15a' && config?.kb_id !== '86927718-7690-4c0c-a99d-8bc8afda0a4c') }
                  // )}> */}
                    {(!isMicrophoneOpen ||
                      !config?.show_microphone ||
                      isRecording) && (
                      <textarea
                        ref={textareaRef}
                        onFocus={() => {
                          setIsTextareaFocused(true);
                          setHasInteracted(true);
                        }}
                        onBlur={() => setIsTextareaFocused(false)}
                        // className='w-full  transition-colors duration-200 no-scrollbar px-2 py-1'
                         className={clsx(
                          'w-full transition-all duration-200 no-scrollbar px-2 bg-transparent resize-none',
                          isMessageSendingDisabled() ? 'border-0 cursor-not-allowed' : 'bg-transparent border-0 focus:outline-none',
                          {'!p-0': isMicrophoneOpen}
                        )}
                        style={{
                          scrollbarWidth: 'none', /* Firefox */
                          msOverflowStyle: 'none', /* IE and Edge */
                        }}
                        rows={1}
                        placeholder={
                          config?.input_placeholder_text ||
                          DANTE_THEME_CHAT.input_placeholder_text
                        }
                        value={question}
                        disabled={isMessageSendingDisabled()}
                        onChange={(e) => {
                          const newValue = e.target.value;
                          setQuestion(newValue);

                          // Handle typing events for human handover
                          if (onTypingStart && onTypingStop && (isInHumanHandoverApp || pollResponse)) {
                            if (newValue.trim() === '') {
                              onTypingStop();
                              if (typingTimeoutRef.current) {
                                clearTimeout(typingTimeoutRef.current);
                                typingTimeoutRef.current = null;
                              }
                            } else {
                              if (!typingTimeoutRef.current) {
                                onTypingStart();
                                typingTimeoutRef.current = true;
                              }
                            }
                          }
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            if (e.shiftKey) {
                              // Allow shift+enter to create a new line
                              return;
                            }
                            if (!isMessageSendingDisabled()) {
                              e.preventDefault();
                              handleSend();
                            }
                          }
                        }}
                      />
                    )}
                    {isMicrophoneOpen && config?.show_microphone && (
                      <AudioRecorder
                        handleRecordingComplete={handleRecordingComplete}
                        setQuestion={setQuestion}
                        isRecording={isRecording}
                        setIsRecording={setIsRecording}
                        isPreviewMode={isMessageSendingDisabled()}
                      />
                    )}

                    {/* <div className="absolute bottom-0 top-0 my-auto right-[6px] flex items-center"> */}
                    <div className='flex items-center '>
                      {isInHumanHandoverApp && (
                        <MenuQuickResponse
                          quickResponses={humanHandoverConfig?.quick_responses}
                          handleQuickResponseClick={handleQuickResponseClick}
                        />
                      )}

                      {(isInHumanHandoverApp || pollResponse) && (
                        <input
                          type="file"
                          accept="image/*"
                          multiple
                          className="hidden"
                          ref={fileInputRef}
                          onChange={handleImageUpload}
                          tabIndex={-1}
                        />
                      )}

                    

                      {(isInHumanHandoverApp || pollResponse) && (
                        <DButtonIcon
                          className="p-size1 size-8 rounded-size0 flex items-end justify-center"
                          onClick={() => fileInputRef.current?.click()}
                        >
                          <AttachmentIcon className="w-8 h-8" />
                        </DButtonIcon>
                      )}
                      {config?.realtime_voice_access && !isInHumanHandoverApp && !pollResponse && (
                        <div className="relative group">
                          <DTooltip content={`<span className="text-nowrap">Click here to start talking to me!</span>`} position="top">
                            <DButtonIcon
                              onClick={() => {
                                // Always stop animation on manual click
                                setHasInteracted(true);
                                handleShowVoice();
                              }}
                              className="relative "
                            >
                              <AiVoiceIcon className="text-blue-500 group-hover:text-blue-600 transition-colors duration-300 relative z-10" />
                              {!hasInteracted && (
                                <>
                                  <span className="absolute inset-1 bg-blue-100  rounded-full animate-ping opacity-75"></span>
                                  <span className="absolute inset-1 bg-blue-50 rounded-full animate-pulse-strong"></span>
                                </>
                              )}
                            </DButtonIcon>
                          </DTooltip>
                        </div>
                      )}

                      {config?.show_microphone && !isRecording && (
                        <Menu>
                          <MenuButton className="p-size1 size-8 rounded-full flex items-end justify-center bg-black">
                            <ChevronDownIcon className="w-[14px] h-[14px] text-white" />
                          </MenuButton>
                          <MenuItems
                            transition
                            className="transition duration-150 ease-out data-[closed]:scale-95 data-[closed]:opacity-0 data-[closed]:translate-y-2  bg-black rounded-size0 flex flex-col gap-size1 justify-center items-center py-size0 w-[32px] translate-y-[-2px]"
                            anchor="top start"
                          >
                            <MenuItem>
                              <button
                                className="dbutton send-message-button flex items-center justify-center hover:bg-[#FFFFFF26] p-size0 rounded-size0"
                                onClick={(e) => {
                                  e.preventDefault();
                                  handleSend(question);
                                }}
                              >
                                <SendIcon className="text-white w-[14px] h-[14px]" />
                              </button>
                            </MenuItem>
                            {config?.show_microphone && !isMicrophoneOpen ? (
                              <MenuItem>
                                <button
                                  className="dbutton open-microphone-button flex items-center justify-center hover:bg-[#FFFFFF26] p-size0 rounded-size0"
                                  onClick={handleOpenMicrophone}
                                >
                                  <MicrophoneIcon className="text-white w-[13px] h-[13px]" />
                                </button>
                              </MenuItem>
                            ) : (
                              <MenuItem>
                                <button
                                  className="dbutton open-chatbot-button flex items-center justify-center hover:bg-[#FFFFFF26] p-size0 rounded-size0"
                                  onClick={() => {
                                    setIsMicrophoneOpen(false);
                                    setIsRecording(false);
                                  }}
                                >
                                  <AiChatbotIcon className="text-white w-[13px] h-[13px]" />
                                </button>
                              </MenuItem>
                            )}
                            {/* {config?.realtime_voice_access && (
                              <MenuItem>
                                <button className="dbutton flex items-center justify-center hover:bg-[#FFFFFF26] p-size0 rounded-size0">
                                  <AiVoiceIcon className="text-white w-[14px] h-[14px]" />
                                </button>
                              </MenuItem>
                            )} */}
                          </MenuItems>
                        </Menu>
                      )}
                      {!config?.show_microphone &&
                        // !config?.realtime_voice_access &&
                        !isRecording && (
                          <DButtonIcon
                            className={clsx(
                              '!rounded-full',
                              'send-message-button',
                              'text-[rgba(9, 8, 31,0.5)]',
                              isMessageSendingDisabled() ? '!bg-[rgba(9, 8, 31,0.05)]' : ''
                            )}
                            onClick={() => handleSend()}
                            style={{
                              color: 'var(--dt-color-element-100)',
                              backgroundColor: 'var(--dt-color-surface-100)',
                            }}
                            disabled={isMessageSendingDisabled()}
                          >
                            <SendIcon className={`w-4 h-4 text-[rgba(9, 8, 31,0.5)]`} />
                          </DButtonIcon>
                        )}
                    </div>
                  </div>
                  {!isPreviewMode &&
                    (isInApp || isDanteFaq) && !isInHumanHandoverApp && (
                      <div
                        className={clsx(
                          'flex items-center justify-between',
                          isDanteFaq ? 'self-end' : ''
                        )}
                      >
                        {!isDanteFaq &&(
                          <div>
                            <LLMSelector />
                          </div>
                        )}

                        {/* <div className="flex items-center gap-size1 text-xs self-end">
                          Submit <ReturnIcon width={12} height={12} />
                        </div> */}
                      </div>
                    )}
                  {!isInApp &&
                   (!hiddenPoweredByDante && !config?.remove_watermark) && (
                      <div className="flex items-center text-xs justify-between">
                        <PoweredByDante
                          surface={config?.surface_color}
                          isPreviewMode={isPreviewMode}
                        />
                      </div>
                    )}
                </div>
              )}
              {showConsent && !isInHumanHandoverApp && (
                <div className="grow-0 flex gap-size1 w-full">
                  <DButton
                    variant="dark"
                    fullWidth
                    onClick={() => {
                      setQuestion('yes');
                      setShowConsent(false);
                      handleSend('yes');
                    }}
                  >
                    Yes
                  </DButton>
                  <DButton
                    variant="outlined"
                    fullWidth
                    onClick={() => {
                      setQuestion('no');
                      setShowConsent(false);
                      handleSend('no');
                    }}
                  >
                    No
                  </DButton>
                </div>
              )}
              {isInHumanHandoverApp && !humanHandoverConfig?.isTaken && (
                <div className="grow-0 flex gap-size1 w-full mb-size2">
                  <DButton
                    variant="grey"
                    fullWidth
                    size="md"
                    onClick={() => {
                      humanHandoverMarkResolved();
                    }}
                  >
                    Mark as resolved
                  </DButton>
                  <DButton
                    variant="dark"
                    fullWidth
                    size="md"
                    onClick={() => {
                      humanHandoverTakeConversation();
                    }}
                  >
                    Join
                  </DButton>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="relative">
        <DModalEmailTransaction
          open={showEmailTranscript}
          onClose={() => setShowEmailTranscript(false)}
          conversationId={currectConversationId}
          token={config?.token}
          setNotification={setNotification}
        />
      </div>
    </div>
  );
};

export default Chat;
