import React from 'react';
import MessageInput from './index';
import NewMicrophoneIcon from '../Global/Icons/NewMicrophoneIcon';
import NewStopRecordingIcon from '../Global/Icons/NewStopRecordingIcon';

export default {
  title: 'Components/MessageInput',
  component: MessageInput,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#FFFFFF' },
        { name: 'dark', value: '#0F0942' },
      ],
    },
  },
};

export function Default() {
  return (
    <MessageInput 
      onSend={(message) => console.log('Message sent:', message)}
      onRecordStart={() => console.log('Recording started')}
      onRecordStop={() => console.log('Recording stopped')}
    />
  );
}

export function WithCustomPlaceholder() {
  return (
    <MessageInput 
      placeholder="Type your message here..."
      onSend={(message) => console.log('Message sent:', message)}
      onRecordStart={() => console.log('Recording started')}
      onRecordStop={() => console.log('Recording stopped')}
    />
  );
}

export function DarkMode() {
  return (
    <div className="dark p-8 rounded-lg bg-[#0F0942]">
      <MessageInput 
        onSend={(message) => console.log('Message sent:', message)}
        onRecordStart={() => console.log('Recording started')}
        onRecordStop={() => console.log('Recording stopped')}
      />
    </div>
  );
}

export function Responsive() {
  return (
    <div className="w-full max-w-md">
      <MessageInput 
        className="w-full"
        onSend={(message) => console.log('Message sent:', message)}
        onRecordStart={() => console.log('Recording started')}
        onRecordStop={() => console.log('Recording stopped')}
      />
    </div>
  );
}

// Interactive demo that shows all states
export function InteractiveDemo() {
  const [demoState, setDemoState] = React.useState('default');
  
  return (
    <div className="new-theme space-y-8">
      <div className="flex flex-wrap gap-4">
        <button 
          onClick={() => setDemoState('default')}
          className={`px-4 py-2 rounded ${demoState === 'default' ? 'new-bg-accent new-text-light' : 'bg-gray-200'}`}
        >
          Default
        </button>
        <button 
          onClick={() => setDemoState('hover')}
          className={`px-4 py-2 rounded ${demoState === 'hover' ? 'new-bg-accent new-text-light' : 'bg-gray-200'}`}
        >
          Hover
        </button>
        <button 
          onClick={() => setDemoState('focus')}
          className={`px-4 py-2 rounded ${demoState === 'focus' ? 'new-bg-accent new-text-light' : 'bg-gray-200'}`}
        >
          Focus
        </button>
        <button 
          onClick={() => setDemoState('recording')}
          className={`px-4 py-2 rounded ${demoState === 'recording' ? 'new-bg-accent new-text-light' : 'bg-gray-200'}`}
        >
          Recording
        </button>
      </div>
      
      <div>
        {demoState === 'default' && (
          <div 
            className="flex items-center gap-2 w-[551px] h-12 rounded-full new-bg-button flex-shrink-0"
            style={{ padding: '7px 8px 7px 16px' }}
          >
            <input
              type="text"
              placeholder="Some message text to send"
              className="flex-1 bg-transparent border-none outline-none new-text-dark font-['Inter_Variable'] text-base font-normal"
              disabled
            />
            <div className="flex items-center justify-center w-8 h-8 rounded-full">
              <NewMicrophoneIcon />
            </div>
          </div>
        )}
        
        {demoState === 'hover' && (
          <div 
            className="flex items-center gap-2 w-[551px] h-12 rounded-full new-bg-hover flex-shrink-0"
            style={{ padding: '7px 8px 7px 16px' }}
          >
            <input
              type="text"
              placeholder="Some message text to send"
              className="flex-1 bg-transparent border-none outline-none new-text-dark font-['Inter_Variable'] text-base font-normal"
              disabled
            />
            <div className="flex items-center justify-center w-8 h-8 rounded-full">
              <NewMicrophoneIcon />
            </div>
          </div>
        )}
        
        {demoState === 'focus' && (
          <div 
            className="flex items-start gap-2 w-[551px] h-12 rounded-full border border-new-accent new-bg-light flex-shrink-0"
            style={{ padding: '7px 8px 7px 16px' }}
          >
            <input
              type="text"
              placeholder="Some message text to send"
              className="flex-1 bg-transparent border-none outline-none new-text-dark font-['Inter_Variable'] text-base font-normal"
              disabled
            />
            <div className="flex items-center justify-center w-8 h-8 rounded-full">
              <NewMicrophoneIcon />
            </div>
          </div>
        )}
        
        {demoState === 'recording' && (
          <div 
            className="flex items-start gap-2 w-[551px] h-12 rounded-full new-bg-hover flex-shrink-0"
            style={{ padding: '7px 8px 7px 16px' }}
          >
            <input
              type="text"
              placeholder="Some message text to send"
              className="flex-1 bg-transparent border-none outline-none new-text-dark font-['Inter_Variable'] text-base font-normal leading-[160%]"
              disabled
            />
            <div className="flex items-center justify-center w-8 h-8 rounded-full">
              <NewStopRecordingIcon />
            </div>
          </div>
        )}
      </div>
      
      <div className="text-sm space-y-2">
        <h3 className="font-medium">Current State: {demoState}</h3>
        <ul className="list-disc pl-5 space-y-1">
          {demoState === 'default' && (
            <>
              <li>Background: var(--new-button)</li>
              <li>Border: none</li>
              <li>Alignment: center</li>
              <li>Padding: 7px 8px 7px 16px</li>
              <li>Icon: Microphone</li>
            </>
          )}
          {demoState === 'hover' && (
            <>
              <li>Background: var(--new-hover)</li>
              <li>Border: none</li>
              <li>Alignment: center</li>
              <li>Padding: 7px 8px 7px 16px</li>
              <li>Icon: Microphone</li>
            </>
          )}
          {demoState === 'focus' && (
            <>
              <li>Background: var(--new-light)</li>
              <li>Border: 1px solid var(--new-accent)</li>
              <li>Alignment: flex-start</li>
              <li>Padding: 7px 8px 7px 16px</li>
              <li>Icon: Microphone</li>
            </>
          )}
          {demoState === 'recording' && (
            <>
              <li>Background: var(--new-hover)</li>
              <li>Border: none</li>
              <li>Alignment: flex-start</li>
              <li>Padding: 7px 8px 7px 16px</li>
              <li>Icon: Stop Recording</li>
            </>
          )}
        </ul>
      </div>
    </div>
  );
} 