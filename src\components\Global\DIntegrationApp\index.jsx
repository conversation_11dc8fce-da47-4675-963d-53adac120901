import { useNavigate, useParams } from 'react-router-dom';
import React, { useState, useEffect, Suspense } from 'react';
import DButton from '../DButton';
import loadIcon from '@/helpers/loadIcons';
import featureCheck from '@/helpers/tier/featureCheck';
import useToast from '@/hooks/useToast';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';

const DIntegrationApp = ({ app }) => {
  const params = useParams();
  const navigate = useNavigate();
  const { addWarningToast } = useToast();
  const [IconComponent, setIconComponent] = useState(null);
  const teamSelected = useTeamManagementStore((state) => state.selectedTeam?.id);

  useEffect(() => {
    const getIcon = async () => {
      const t = await loadIcon(app.icon);
      if (t) {
        setIconComponent(() => t);
      }
    };
    getIcon();
  }, [app.icon]);

  return (
    <div
      className="flex flex-col h-[270px]  gap-size3 p-size3 rounded-size1 border border-grey-5 justify-between"
      key={app.label}
    >
      <div className="flex flex-col gap-size1">
        <div className="flex items-end justify-center w-8 h-8">
          {IconComponent && <IconComponent />}
        </div>
        <div className="flex flex-col gap-size0">
          <p className="text-lg font-medium tracking-tight">{app.label}</p>
          <p className="text-xs text-grey-50">{app.description}</p>
        </div>
      </div>
      <DButton
        variant="grey"
        className="w-full !mt-auto"
        fullWidth
        onClick={() => {
          if (!teamSelected && !featureCheck(`${app.label.toLowerCase()}_integration`)) {
            return;
          }
          if (app.label.toLowerCase() !== 'intercom' && app.label.toLowerCase() !== 'whatsapp' && app.label.toLowerCase() !== 'wordpress') {
            window.open(app.link, '_blank');
          } else {
            navigate(`/chatbot/${params.id}/integrations/${app.link}`);
          }
        }}
      >
        {app.cta}
      </DButton>
    </div>
  );
};

export default DIntegrationApp;
