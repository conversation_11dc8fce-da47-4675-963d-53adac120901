import React, { useState, useEffect } from 'react';
import AddIcon from '@/components/Global/Icons/AddIcon';
import './BlobAnimationButton.css';
import BlobButton from './BlobButton';
import AddChatbotIcon from '../Global/Icons/AddChatbotIcon';

const BlobAnimationButton = ({ onClick, className, buttonText, whiteBlob4 = false }) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {

    const checkMobile = () => {
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const isSmallScreen = window.innerWidth <= 768;
      setIsMobile(isTouchDevice && isSmallScreen);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);



  return (
    <button
      className={`relative flex flex-col items-center justify-center blob-animation-button ${className} ${whiteBlob4 ? 'white-blob-4' : ''} `}
      onClick={onClick}
    >
      {isMobile ? (
        <button
         className={'dbutton flex flex-col items-center rounded-size1 py-size2 px-size3 md:p-size3 justify-center text-grey-20 w-full min-h-[270px]'}
         >
           <AddChatbotIcon className="size-10" />
           <span className="mt-4 text-grey-50 font-medium text-md md:text-lg">
             New AI Voice Agent
           </span>
        </button>
     
      ) : (
        <div className="relative w-full h-28 md:h-40 rounded-size1 flex items-center justify-center overflow-visible transition-transform duration-200 ease-in-out hover:scale-110">
          <div className="absolute inset-0 rounded-size1 overflow-visible">
            <BlobButton className="overflow-visible" />
          </div>
          <div className="relative z-10 flex items-center justify-center rounded-full p-2">
            <AddIcon className="w-7 h-7 text-purple-500" />
          </div>
        </div>
      )}
      {buttonText && (
        <span className="mt-7 text-base md:text-lg font-medium">{buttonText}</span>
      )}
    </button>
  );
};

export default BlobAnimationButton;
