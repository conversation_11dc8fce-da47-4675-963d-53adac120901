import { v4 as uuidv4 } from 'uuid';

/**
 * Transforms a message object into a structured chat bubble message.
 *
 * @param {Object} message - The original message object.
 * @param {string} message.id - Unique identifier for the message.
 * @param {string} message.type - Role of the message sender (e.g., user, assistant).
 * @param {boolean} message.is_quick_response - Flag indicating if the message is a quick response.
 * @param {string} message.text - The message content.
 * @param {string} message.date_created - The creation date of the message.
 * @param {string} message.agent_name - The name of the agent (optional).
 * @param {string} message.agent_profile_pic - The profile picture URL of the agent (optional).
 *
 * @returns {Object} A formatted chat bubble message object.
 */
const transformMessageToBubbleMessageHumanHandover = ({ 
  id, 
  type, 
  is_quick_response, 
  text, 
  date_created,
  agent_name,
  agent_profile_pic,
  agent_role,
  agent_title,
  images
}) => ({
  id,
  role: type,
  type: is_quick_response ? 'quick_response' : type === 'agent_connecting' ? 'agent_connecting' : 'normal',
  content: text,
  status: 'complete',
  date_created,
  agent_name,
  agent_profile_pic,
  agent_role,
  agent_title,
  images
});

export default transformMessageToBubbleMessageHumanHandover;
