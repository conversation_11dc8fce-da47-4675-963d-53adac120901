import VoiceIcon from '@/assets/voice/voice_icon.png'
import DButton from '@/components/Global/DButton';
import DButtonIcon from '@/components/Global/DButtonIcon';
import AiChatbotIcon from '@/components/Global/Icons/AiChatbotIcon';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import EditIcon from '@/components/Global/Icons/EditIcon';
import PhoneIcon from '@/components/Global/Icons/PhoneIcon';
import { useNavigate } from 'react-router-dom';
import BlobAnimation from '../BlobAnimation';
import BrainIcon from '@/components/Global/Icons/BrainIcon';
import AiVoiceIcon from '@/components/Global/Icons/AiVoiceIcon';
import TalkingIcon from '../TalkingIcon';
import MicrophoneIcon from '@/components/Global/Icons/MicrophoneIcon';
import PlayIcon from '@/components/Global/Icons/PlayIcon';
import PlayPauseIcon from '@/components/Global/Icons/PlayPauseIcon';
import { useState, useRef, useEffect } from 'react';
import PauseIcon from '@/components/Global/Icons/PauseIcon';

const VoiceBlock = ({ id, name, chatbot, phoneNumbers, voice, onEditVoice, nrOfConversations, previewUrl }) => {
  const navigate = useNavigate();
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef(new Audio());
  
  // Set up the audio source when previewUrl changes
  useEffect(() => {
    if (previewUrl) {
      audioRef.current.src = previewUrl;
      
      // Add event listener for when audio ends
      audioRef.current.addEventListener('ended', () => {
        setIsPlaying(false);
      });
    }
    
    // Cleanup
    return () => {
      audioRef.current.pause();
      audioRef.current.removeEventListener('ended', () => {
        setIsPlaying(false);
      });
    };
  }, [previewUrl]);
  
  const togglePlayPause = () => {
    if (!previewUrl) return;
    
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      // Reset audio position to start if it reached the end
      if (audioRef.current.currentTime === audioRef.current.duration) {
        audioRef.current.currentTime = 0;
      }
      audioRef.current.play().catch(error => {
        console.error('Error playing audio:', error);
      });
    }
    
    setIsPlaying(!isPlaying);
  };
  
  return <div className="flex flex-col w-full md:max-w-[300px] rounded-size1 bg-white/50 shadow-sm p-size5 gap-size4">
    <div className='flex flex-col gap-size0'>
        <div className='flex items-center justify-between'>
            <div className='flex items-center gap-size0'>
                {/* <div className='overflow-hidden flex items-center justify-center'>
                */}
                <AiVoiceIcon className="text-purple-500 w-7 h-7"/>
                {/* <img src="https://dante-public-files.lon1.cdn.digitaloceanspaces.com/AI_VOICE_DANTE.png" className='h-8 w-auto'/>     */}
                <p className='text-lgfont-regular tracking-tight'>{name}</p>
            </div>
            <DButtonIcon 
              onClick={togglePlayPause} 
              disabled={!previewUrl}
              className={!previewUrl ? 'opacity-50 cursor-not-allowed' : 'hover:bg-purple-5'}
            >
              {isPlaying ? (
                <PauseIcon className='w-5 h-5 text-purple-500' />
              ) : (
                <PlayIcon className='w-5 h-5 text-purple-500' />
              )}
            </DButtonIcon>
         </div>
        <div className='w-full h-px bg-grey-5'></div>
    </div>
    <div className='flex flex-col gap-size2'>
        <div className='flex items-center gap-size1'>
            <div className='w-[24px] h-[24px] flex items-center justify-center'>
                <BrainIcon className="w-[16px]" />
            </div>
            <p className='text-sm font-regular tracking-tight truncate'>{chatbot?.knowledge_base_name}</p>
        </div>
        <div className='flex items-center gap-size1'>
            <div className='w-[24px] h-[24px] flex items-center justify-center'>
                <AiChatbotIcon className="w-[14px] mt-1" />
            </div>
            <p className='text-sm font-regular tracking-tight'>{nrOfConversations} {nrOfConversations === 1 ? 'conversation' : 'conversations'}</p>
        </div>
        <div className='flex items-center gap-size1'>
            <div className='w-[24px] h-[24px] flex items-center justify-center'>
                <TalkingIcon className="font-light" />
            </div>
            <p className='text-sm font-regular tracking-tight'>{voice} AI Voice</p>
        </div>
    </div>
    <div className="flex flex-col items-center gap-size1 w-full">
        <DButton variant='outlined' fullWidth onClick={() => navigate(`/voice/${id}`)} className='!mt-auto'>
            <span className='text-sm'>Insights</span>
        </DButton>
        <DButton variant='outlined'onClick={onEditVoice} fullWidth>
            <span className='text-sm'>Edit AI Voice Agent</span>
            <EditIcon width={16} height={16} />
        </DButton>
    </div>
  </div>;
};

export default VoiceBlock;
