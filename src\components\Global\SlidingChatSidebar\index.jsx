import React, { Fragment, useRef } from 'react';
import { Transition } from '@headlessui/react';
import OpenNavIcon from '../Icons/OpenNavIcon';
import CloseIcon from '../Icons/CloseIcon';
import StarIcon from '../Icons/StarIcon';
import ChevronDownIcon from '../Icons/ChevronDownIcon';

const SlidingChatSidebar = ({ isOpen, onClose, onMinimize, children }) => {
  const iframeRef = useRef(null);

  const handleReset = () => {
    if (iframeRef.current?.querySelector('iframe')) {
      const iframe = iframeRef.current.querySelector('iframe');
      iframe.src = iframe.src;
    }
  };

  return (
    <Transition
      show={isOpen}
      as={Fragment}
      enter="transform transition ease-in-out duration-500"
      enterFrom="translate-x-full"
      enterTo="translate-x-0"
      leave="transform transition ease-in-out duration-500"
      leaveFrom="translate-x-0"
      leaveTo="translate-x-full"
    >
      <div className="fixed top-0 right-0 h-[100dvh] w-full sm:w-[390px] bg-white border-l border-grey-5 shadow-lg">
        <div className="flex h-full flex-col">
          <div className="flex items-center justify-between px-4 py-5 border-b border-grey-5">
            <div className="flex items-center gap-2">
              <StarIcon className="w-6 h-6 new-text-accent" />
              <span className="font-bold text-base text-grey-75">Assistant</span>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={onMinimize}
                className="rounded-md text-grey-60 hover:text-grey-80 focus:outline-none"
                aria-label="Minimize"
              >
                <ChevronDownIcon className="h-3 w-3 text-grey-75" />
              </button>
              <button
                onClick={onClose}
                className="rounded-md text-grey-60 hover:text-grey-80 focus:outline-none"
                aria-label="Close"
              >
                <CloseIcon className="h-5 w-5 text-black font-bold" />
              </button>
            </div>
          </div>
          <div className="relative flex-1 overflow-y-auto">
            {React.cloneElement(children, {
              ref: iframeRef
            })}
          </div>
        </div>
      </div>
    </Transition>
  );
};

export default SlidingChatSidebar; 