import React, { Suspense, useEffect } from 'react';
import { Outlet } from 'react-router-dom';

import useLayoutStore from '@/stores/layout/layoutStore';

import DLoading from '@/components/DLoading';
import PageViewTracker from '@/components/PageViewTracker';
import Route<PERSON>hangeHandler from '@/components/RouteChangeHandler';
import { registerServiceWorker } from '@/helpers/notificationHelper';

const Entrypoint = () => {
  const setIsDarkMode = useLayoutStore((state) => state.setIsDarkMode);

  useEffect(() => {
    const theme = localStorage.getItem('theme');

    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
      setIsDarkMode(true);
    }
  }, []);

  async function initializeServiceWorker() {
    try {
      await registerServiceWorker();
      // Note: Notification permission will be requested only when needed in human handover
    } catch (error) {
      console.error('Failed to initialize service worker:', error);
    }
  }

  useEffect(() => {
    initializeServiceWorker();
  }, []);

  return (
    <>
      <PageViewTracker />
      <RouteChangeHandler />
      <Suspense fallback={<DLoading show={true} />}>
        <Outlet />
      </Suspense>
    </>
  );
};

export default Entrypoint;
