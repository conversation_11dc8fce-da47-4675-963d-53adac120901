import convertCustomCss from '@/helpers/convertCustomCss';
import convertThemeToCSSVariablesStyle from '@/helpers/convertThemeToCSSVariables';

const StyleTag = ({ tag, tempCustomizationData, customImport }) => {
  return <style>{`
    ${customImport ? `@import url(${customImport});` : ''}
    ${tag} {
        font-family: var(--dt-font-family, "Inter"), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
        font-size: var(--dt-font-size) !important;
        line-height: var(--dt-line-height) !important;
        ${convertThemeToCSSVariablesStyle({
        color: {
            alert: tempCustomizationData && tempCustomizationData?.alert_color,
            positive: tempCustomizationData && tempCustomizationData?.positive_color,
            negative: tempCustomizationData && tempCustomizationData?.negative_color,
            surface: tempCustomizationData && tempCustomizationData?.surface_color,
            element: tempCustomizationData && tempCustomizationData?.element_color,
            brand: tempCustomizationData && tempCustomizationData?.brand_color,
        },
        font: {
            family: tempCustomizationData && tempCustomizationData?.font_name,
            size: tempCustomizationData && tempCustomizationData?.font_size,
        },
        })}
    }
    ${tempCustomizationData && tempCustomizationData?.custom_css !== '' ? convertCustomCss(tempCustomizationData) : ''}`}
    </style>;
};

export default StyleTag;
