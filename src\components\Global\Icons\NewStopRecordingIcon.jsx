import React from 'react';
import PropTypes from 'prop-types';

const NewStopRecordingIcon = ({ className = '', ...props }) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width="22" 
      height="22" 
      viewBox="0 0 22 22" 
      fill="none"
      className={`new-text-error ${className}`}
      {...props}
    >
      <path 
        fillRule="evenodd" 
        clipRule="evenodd" 
        d="M19.25 11C19.25 13.188 18.3808 15.2865 16.8336 16.8336C15.2865 18.3808 13.188 19.25 11 19.25C8.81196 19.25 6.71354 18.3808 5.16637 16.8336C3.61919 15.2865 2.75 13.188 2.75 11C2.75 8.81196 3.61919 6.71354 5.16637 5.16637C6.71354 3.61919 8.81196 2.75 11 2.75C13.188 2.75 15.2865 3.61919 16.8336 5.16637C18.3808 6.71354 19.25 8.81196 19.25 11ZM21.5 11C21.5 13.7848 20.3938 16.4555 18.4246 18.4246C16.4555 20.3938 13.7848 21.5 11 21.5C8.21523 21.5 5.54451 20.3938 3.57538 18.4246C1.60625 16.4555 0.5 13.7848 0.5 11C0.5 8.21523 1.60625 5.54451 3.57538 3.57538C5.54451 1.60625 8.21523 0.5 11 0.5C13.7848 0.5 16.4555 1.60625 18.4246 3.57538C20.3938 5.54451 21.5 8.21523 21.5 11ZM6.875 8.375C6.875 7.97718 7.03304 7.59564 7.31434 7.31434C7.59564 7.03304 7.97718 6.875 8.375 6.875H13.625C14.0228 6.875 14.4044 7.03304 14.6857 7.31434C14.967 7.59564 15.125 7.97718 15.125 8.375V13.625C15.125 14.0228 14.967 14.4044 14.6857 14.6857C14.4044 14.967 14.0228 15.125 13.625 15.125H8.375C7.97718 15.125 7.59564 14.967 7.31434 14.6857C7.03304 14.4044 6.875 14.0228 6.875 13.625V8.375Z" 
        fill="currentColor"
      />
    </svg>
  );
};

NewStopRecordingIcon.propTypes = {
  className: PropTypes.string
};

export default NewStopRecordingIcon; 