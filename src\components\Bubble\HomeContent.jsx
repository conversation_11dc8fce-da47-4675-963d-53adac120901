import AiChatbotIcon from '../Global/Icons/AiChatbotIcon';
import DButtonIcon from '../Global/DButtonIcon';
import UpRightIcon from '../Global/Icons/UpRightIcon';
import * as tasksService from '@/services/tabs.service';
import { useEffect, useState, useRef } from 'react';
import PlayIcon from '../Global/Icons/PlayIcon';
import PauseIcon from '../Global/Icons/PauseIcon';
import { usePreviousConversationsStore } from '@/stores/previousConversation/previousConversationStore';
import { DateTime } from 'luxon';
import { useConversationStore } from '@/stores/conversation/conversationStore';
import PoweredByDante from '../PoweredByDante';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import SendIcon from '../Global/Icons/SendIcon';
import clsx from 'clsx';

import DLoading from '../DLoading';
import { CHATBOTS_WITH_NEW_DESIGN } from '@/constants';


const HomeContent = ({
  kb_id,
  slots,
  isPreviewMode,
  token,
  setActiveTab,
  hidePoweredByDante,
  previous_conversation_enabled = true,
  forceRefetch,
}) => {
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(true);
  const [minLoadingTime, setMinLoadingTime] = useState(true);
  const { conversations, fetchConversations } = usePreviousConversationsStore();
  const { tabsCustomization, updateTabsCustomization } =
    useCustomizationStore();
  const [conversationsFromKbId, setConversationsFromKbId] = useState([]);
  const setCurrentConversation = useConversationStore(
    (state) => state.setCurrentConversation
  );

  // Video autoreplay state and play/pause controls
  const videoRefs = useRef({});
  const [videoPlayStates, setVideoPlayStates] = useState({});

  const getSlots = async () => {
    setLoading(true);
    try {
      const response = await tasksService.getAllSlots(kb_id, token);
      if (response.status === 200) {
        setData(response.data);
        updateTabsCustomization('home', response.data);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  const handleConversationClick = (conversation_id, conversation) => {
    setCurrentConversation({ id: conversation_id, type: 'opening' });
    if (setActiveTab) {
      setActiveTab('chat');
    }
  };

// Video play/pause control functions
  const toggleVideoPlayPause = (videoId) => {
    const iframe = videoRefs.current[videoId];
    if (!iframe) return;

    const isPlaying = videoPlayStates[videoId] || false;

    try {
      if (isPlaying) {
        // Pause video
        if (iframe.src.includes('vimeo.com')) {
          iframe.contentWindow.postMessage('{"method":"pause"}', '*');
        } else if (iframe.src.includes('youtube.com')) {
          iframe.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*');
        } else if (iframe.src.includes('loom.com')) {
          iframe.contentWindow.postMessage('{"method":"pause"}', '*');
        }
      } else {
        // Play video
        if (iframe.src.includes('vimeo.com')) {
          iframe.contentWindow.postMessage('{"method":"play"}', '*');
        } else if (iframe.src.includes('youtube.com')) {
          iframe.contentWindow.postMessage('{"event":"command","func":"playVideo","args":""}', '*');
        } else if (iframe.src.includes('loom.com')) {
          iframe.contentWindow.postMessage('{"method":"play"}', '*');
        }
      }

      setVideoPlayStates(prev => ({
        ...prev,
        [videoId]: !isPlaying
      }));
    } catch (error) {
      console.error('Error controlling video playback:', error);
    }
  };

// Helper function to convert video URLs to embeddable format
  const getEmbeddableVideoUrl = (url) => {
    if (!url) return null;

    // YouTube URL conversion
    const youtubeRegex = /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/;
    const youtubeMatch = url.match(youtubeRegex);
    if (youtubeMatch) {
      // Always ensure autoplay is disabled regardless of original URL parameters
      return `https://www.youtube.com/embed/${youtubeMatch[1]}?mute=1&enablejsapi=1&loop=1&playlist=${youtubeMatch[1]}&autoplay=0&controls=1`;
    }

    // Vimeo URL conversion - handles both vimeo.com and player.vimeo.com URLs
    const vimeoRegex = /(?:player\.vimeo\.com\/video\/|vimeo\.com\/)([0-9]+)/;
    const vimeoMatch = url.match(vimeoRegex);
    if (vimeoMatch) {
      // Extract video ID
      const videoId = vimeoMatch[1];

      // Parse existing URL to preserve some parameters but override autoplay
      const urlObj = new URL(url.includes('player.vimeo.com') ? url : `https://player.vimeo.com/video/${videoId}`);

      // Set our required parameters, overriding any existing ones
      urlObj.searchParams.set('autoplay', '0');
      urlObj.searchParams.set('controls', '1');
      urlObj.searchParams.set('title', '0');
      urlObj.searchParams.set('byline', '0');
      urlObj.searchParams.set('portrait', '0');
      urlObj.searchParams.set('muted', '0');

      // Keep existing parameters like 'h' (hash) if they exist
      return urlObj.toString();
    }

    // Loom URL conversion
    const loomRegex = /(?:loom\.com\/share\/)([^/?]+)/;
    const loomMatch = url.match(loomRegex);
    if (loomMatch) {
      return `https://www.loom.com/embed/${loomMatch[1]}?autoplay=0`;
    }

    // Jam URL conversion
    const jamRegex = /(?:jam\.dev\/c\/)([^/?]+)/;
    const jamMatch = url.match(jamRegex);
    if (jamMatch) {
      return `https://jam.dev/embed/${jamMatch[1]}?autoplay=0`;
    }

    // TikTok URL conversion
    const tiktokRegex = /(?:tiktok\.com\/@[^/]+\/video\/|vm\.tiktok\.com\/)([0-9]+)/;
    const tiktokMatch = url.match(tiktokRegex);
    if (tiktokMatch) {
      // TikTok embed doesn't support autoplay due to platform restrictions
      return `https://www.tiktok.com/embed/v2/${tiktokMatch[1]}`;
    }

    // Return original URL if not a supported platform
    return url;
  };



  // Set minimum loading time of 2 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setMinLoadingTime(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    const updateData = () => {
      if (kb_id && !isPreviewMode) {
        getSlots();
      } else {
        setData(slots);
        setLoading(false); // Set loading to false for preview mode
      }
    };

    updateData();
  }, [kb_id, slots, forceRefetch]);

  useEffect(() => {
    fetchConversations();
  }, []);

  useEffect(() => {
    if (conversations[kb_id] && !isPreviewMode) {
      setConversationsFromKbId(
        conversations[kb_id]
          .sort((a, b) => b.createdAt - a.createdAt)
          .slice(0, 3)
      );
    } else if (isPreviewMode) {
      setConversationsFromKbId([
        {
          lastMessage: 'How to create an AI agent?',
          createdAt: DateTime.now().minus({ days: 2 }).toMillis(),
        },
        {
          lastMessage: 'How do I white-label my AI Chatbot?',
          createdAt: DateTime.now().minus({ days: 15 }).toMillis(),
        },
      ]);
    }
  }, [conversations]);

  // Combine all arrays (quick_links, link_groups, sliders, meta_links, videos) into one array
  // Make sure to handle empty arrays gracefully
  const sections = [
    ...(data?.link_groups || []),
    ...(data?.quick_links || []),
    ...(data?.sliders || []),
    ...(data?.meta_links || []),
    ...(data?.videos || []),
  ];

  // Sort by order
  sections?.sort((a, b) => (a.order ?? 9999) - (b.order ?? 9999));

  // Helper functions to render each type
 const renderLinkGroup = (link_group) => {
    return (
      <div key={link_group.id} className="link-groups flex flex-col gap-size1">
        <p className="link-groups-title text-lg font-regular tracking-tight text-[var(--dt-color-element-100)] pl-size1">
          {link_group?.title ?? 'Quick links'}
        </p>
        <div className="link-groups-items flex flex-col gap-size2 mb-size1">
          {link_group?.items
            ?.sort((a, b) => a.order - b.order)
            .map((item) => (
              <a
                key={item.id}
                href={item.url}
                target="_blank"
                rel="noopener noreferrer"
                className="link-groups-item flex items-center justify-between bg-[var(--dt-color-surface-100)] shadow-sm border rounded-[10px] border-[var(--dt-color-surface-100)] p-size2 gap-size2"
              >
                <p className="link-groups-item-title text-base font-regular tracking-tight text-[var(--dt-color-element-100)]">
                  {item.name}
                </p>
                <DButtonIcon>
                  <UpRightIcon className="text-[var(--dt-color-element-100)]" />
                </DButtonIcon>
              </a>
            ))}
        </div>
      </div>
    );
  };

 const renderQuickLink = (item) => {
    return (
      <div
        key={item.id}
        className="quick-links flex items-center justify-between border rounded-[10px] border-[var(--dt-color-surface-100)] py-size2 px-size1 gap-size2 bg-[var(--dt-color-surface-100)] shadow-sm"
      >
        <a
          href={item.url}
          target={item?.open_in_new_tab ? '_blank' : '_self'}
          className="quick-links-item text-base font-regular tracking-tight text-[var(--dt-color-element-100)]"
        >
          {item.title}
        </a>
      </div>
    );
  };

  const renderSlider = (slider) => {
    return (
      <div className="slider flex flex-col gap-size1" key={slider.id}>
        <p className="slider-title text-lg font-regular tracking-tight text-[var(--dt-color-element-100)] pl-size1">
          {slider?.title ?? 'Updates'}
        </p>
        <div className="slider-items flex gap-size2 w-full overflow-x-auto no-scrollbar whitespace-nowrap">
          {slider?.items
            ?.sort((a, b) => a.order - b.order)
            .map((item) => (
              <div
                key={item.id}
                className="slider-item p-size1 flex-none flex flex-col gap-size2 w-[248px] bg-[var(--dt-color-surface-100)] border rounded-[10px] border-[var(--dt-color-surface-100)]"
              >
                <img
                  src={item.thumbnail_url}
                  className="slider-item-image h-[120px] object-cover"
                />
                <div className="slider-item-content flex flex-col gap-size0 w-full flex-wrap">
                  <a
                    className="slider-item-title text-sm font-regular text-wrap tracking-tight text-[var(--dt-color-element-75)] max-w-[240px]"
                    href={item.url}
                    target="_blank"
                  >
                    {item.name}
                  </a>
                  <p className="slider-item-description text-xs text-wrap text-[var(--dt-color-element-50)] max-w-[240px] tracking-tight leading-4">
                    {item.description.length > 100
                      ? item.description.substring(0, 150) + '...'
                      : item.description}
                  </p>
                </div>
              </div>
            ))}
        </div>
      </div>
    );
  };

 const renderMetaLink = (meta_link) => {
    return (
      <div
        className="meta-link border rounded-[10px] border-[var(--dt-color-surface-100)] p-size2 flex flex-col items-start gap-size2 bg-[var(--dt-color-surface-100)] shadow-sm"
        key={meta_link.id}
      >
        {meta_link?.image_url && <img src={meta_link?.image_url} />}
        <div className="meta-link-content flex flex-col gap-size0">
          <a
            href={meta_link?.url}
            target={meta_link?.open_in_new_tab ? '_blank' : '_self'}
            className="text-base font-regular tracking-tight text-[var(--dt-color-element-100)]"
          >
            {meta_link?.title}
          </a>
          <p className="text-xs font-light tracking-tight text-[var(--dt-color-element-50)]">
            {meta_link?.description}
          </p>
        </div>
      </div>
    );
  };


  const renderVideo = (item) => {
    const embeddableUrl = getEmbeddableVideoUrl(item.url);
    const isPlaying = videoPlayStates[item.id] || false;
    const isEmbeddableVideo = embeddableUrl && (
      embeddableUrl.includes('youtube.com') ||
      embeddableUrl.includes('vimeo.com') ||
      embeddableUrl.includes('loom.com') ||
      embeddableUrl.includes('jam.dev') ||
      embeddableUrl.includes('tiktok.com')
    );

   return (
      <div
        key={item.id}
        className="video p-size1 flex flex-col gap-size2 w-full max-w-[90%] mx-auto"
      >
        <div className="relative w-full" style={{ paddingBottom: '56.25%' }}>
          {isEmbeddableVideo ? (
            <>
              <iframe
                ref={(el) => {
                  if (el) {
                    videoRefs.current[item.id] = el;
                  }
                }}
                src={embeddableUrl}
                title={item.title}
                className="absolute top-0 left-0 w-full h-full rounded-[10px] border-0"
                allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen"
                allowFullScreen
              />
              {/* Play/Pause Control Overlay only for Vimeo (YouTube and Loom have their own controls) */}
              {embeddableUrl.includes('vimeo.com') && (

                  <div className="video-play-button absolute inset-0 size-14 bg-white/15 rounded-full backdrop-blur-sm flex items-center justify-center m-auto shadow-sm" >
             <DButtonIcon
                    onClick={() => toggleVideoPlayPause(item.id)}

                  >
                    {isPlaying ? (
                      <PauseIcon className="text-white size-8" />
                    ) : (
                      <PlayIcon className="text-white size-8" />
                    )}
                  </DButtonIcon>
          </div>
              )}
            </>
          ) : (
            <div className="absolute top-0 left-0 w-full h-full bg-[var(--dt-color-surface-100)] rounded-[10px] flex items-center justify-center">
              <div className="relative">
                {item.show_thumbnail && <img src={item.thumbnail_url} />}
                <div className="video-play-button absolute inset-0 size-14 bg-white/15 rounded-full backdrop-blur-sm flex items-center justify-center m-auto ">
                  <button onClick={() => window.open(item.url, '_blank')}>
                    <PlayIcon className="text-white size-8" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="video-content flex flex-col gap-size0 w-full">
          <p className="video-title text-sm font-regular tracking-tight text-[var(--dt-color-element-75)]">
            {item.title}
          </p>
          <p className="video-description text-xs text-[var(--dt-color-element-50)] tracking-tight text-wrap">
            {item.description}
          </p>

        </div>
      </div>
    );
  };

  // Show loading spinner when data is being fetched or minimum loading time hasn't passed
  if ((loading || minLoadingTime) && !isPreviewMode) {
    return (
      <div className="flex justify-center items-center h-full">
            <DLoading show={true} height="h-0"/>
      </div>
    );
  }

  return (
    <div className={clsx("flex-1 min-h-0 h-full flex flex-col", CHATBOTS_WITH_NEW_DESIGN.includes(kb_id) && '!px-4')}>
      {/* Main content with proper overflow handling */}
      <div className="flex-1 flex flex-col overflow-auto">
        {/* Scrollable area */}
        <div className="flex-1 overflow-y-auto no-scrollbar">
          <div className="flex flex-col gap-size3  pb-size2 pt-5  h-1">
            <span className="welcome-message text-xl  font-regular tracking-tight text-[var(--dt-color-element-100)]"></span>
            {kb_id === '86927718-7690-4c0c-a99d-8bc8afda0a4c' && sections.filter((section) => section.type === 'quick_link').map((section) => {
              return (
                <div key={section.id}>
                  {section.type === 'quick_link' && renderQuickLink(section)}
                </div>
              )
            })}
            <div
              className="send-message-container bg-[var(--dt-color-surface-100)] rounded-[10px] p-size2 flex items-center justify-between cursor-pointer"
              onClick={() => setActiveTab && setActiveTab('chat')}
            >
              <p className="send-message-text text-[var(--dt-color-element-100)]">
                Send us a message
              </p>
              <DButtonIcon onClick={() => setActiveTab && setActiveTab('chat')}>
                <SendIcon className="text-[var(--dt-color-element-100)] size-4" />
              </DButtonIcon>
            </div>
            {conversationsFromKbId.length > 0 && previous_conversation_enabled && (
              <div className="previous-conversations flex flex-col gap-size2">
                <p className="previous-conversations-text text-lg font-regular tracking-tight text-[var(--dt-color-element-100)]">
                  Previous conversations
                </p>
                {conversationsFromKbId.map((conversation, index) => (
                  <button
                    disabled={isPreviewMode}
                    key={index}
                    className="flex text-left items-start border rounded-[10px] border-[var(--dt-color-surface-100)] p-size1 gap-size2 bg-[var(--dt-color-surface-100)] shadow-sm"
                    onClick={() =>
                      handleConversationClick(conversation.id, conversation)
                    }
                  >
                    <div className="previous-conversations-icon w-4">
                      <AiChatbotIcon
                        className={'mt-3 text-[var(--dt-color-brand-100)]'}
                      />
                    </div>
                    <div className="previous-conversations-content flex flex-col  w-[calc(100%-40px)]">
                      <p className="text-base font-regular tracking-tight text-[var(--dt-color-element-100)] truncate">
                        {conversation.lastMessage
                          ? conversation.lastMessage
                          : `Conversation - ${conversation.id}`}
                      </p>
                      <p className="previous-conversations-date text-xs font-light tracking-tight text-[var(--dt-color-element-50)]">
                        {DateTime.fromMillis(
                          conversation.createdAt ?? DateTime.now().toMillis(),
                          {
                            zone: 'utc',
                          }
                        ).toRelative()}
                      </p>
                    </div>
                  </button>
                ))}
              </div>
            )}
            {sections.map((section) => {
              if (kb_id === '86927718-7690-4c0c-a99d-8bc8afda0a4c' && section.type === 'quick_link') {
                return null;
              }

              switch (section.type) {
                case 'quick_link':
                  return renderQuickLink(section);
                case 'link_group':
                  return renderLinkGroup(section);
                case 'slider':
                  return renderSlider(section);
                case 'meta_link':
                  return renderMetaLink(section);
                case 'video':
                  return renderVideo(section);
                default:
                  return null;
              }
            })}
          </div>
        </div>

        {/* Fixed footer */}
        {!hidePoweredByDante && (
          <div className="flex-none flex gap-size0 items-center justify-center py-size2">
            <PoweredByDante variant="color" isPreviewMode={isPreviewMode} />
          </div>
        )}
      </div>
    </div>
  );
};

export default HomeContent;
