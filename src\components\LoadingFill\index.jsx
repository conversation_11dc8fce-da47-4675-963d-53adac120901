// components/LoadingFill.jsx
import './index.css';

export default function LoadingFill({
  percent = 0,
  style = {},
}) {
  return (
    <div className="fill-container h-full bg-[#d1cfe233]" style={style}>
      <div
        className="fill-bar"
        style={{ height: `${percent}%` }}
      />
      <div className="content" style={{ alignItems: 'flex-end' }}>
        <h1 className='text-[64px] text-white font-sora'>
          {Math.round(percent)}%
        </h1>
      </div>
    </div>
  );
}
