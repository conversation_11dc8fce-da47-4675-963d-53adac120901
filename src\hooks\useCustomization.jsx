import { useEffect, useState } from 'react';

import * as chatbotService from '@/services/chatbot.service';
import * as customizationService from '@/services/customization.service';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import useToast from './useToast';
import { useOptimistic } from './useOptimistic';
import { useUserStore } from '@/stores/user/userStore';
import { trackKlaviyoEvent } from '@/services/chatbot.service';

const useCustomizationData = (initialFetch = false, chatbotId) => {
  const setChatbotCustomization = useCustomizationStore(
    (state) => state.setChatbotCustomization
  );
  const customizationData = useCustomizationStore(
    (state) => state.chatbotCustomization
  );
  const changedFields = useCustomizationStore((state) => state.changedFields);
  const updateChatbotName = useChatbotStore((state) => state.updateChatbotName);
  const { addSuccessToast } = useToast();

  const [loading, setLoading] = useState(initialFetch);
  const [error, setError] = useState(null);
  const [savingChanges, setSavingChanges] = useState(false);

  const [state, updateState, { rollback, pending, run }] = useOptimistic(
    customizationData,
    (currentState, newState) => ({ ...currentState, ...newState })
  );

  useEffect(() => {
    if (initialFetch && chatbotId) {
      const fetchData = async () => {
        setLoading(true);
        try {
          const response =
            await customizationService.getChatbotCustomizationById({
              kb_id: chatbotId,
            });
          const data = response?.data;
          setChatbotCustomization(data);
          updateState(data);
        } catch (error) {
          setError(error);
        } finally {
          setLoading(false);
        }
      };

      fetchData();
    }
  }, [initialFetch, chatbotId, setChatbotCustomization]);

  const patchCustomizationData = async (updatedData) => {
    let formData = new FormData();
    let chatbotLogo = updatedData.embed_logo_url;
    let chatbotIcon = updatedData.chatbot_icon;
    let favicon = updatedData.custom_url_favicon;

    const uploadFile = async (file) => {
      formData.append('file', file);
      const response = await customizationService.saveChatbotImages(
        chatbotId,
        formData
      );
      formData.delete('file');
      return response.status === 200 ? response?.data?.url : file;
    };

    if (chatbotLogo instanceof File)
      chatbotLogo = await uploadFile(chatbotLogo);
    if (chatbotIcon instanceof File)
      chatbotIcon = await uploadFile(chatbotIcon);
    if (favicon instanceof File) favicon = await uploadFile(favicon);

    const optimisticUpdate = {
      ...updatedData,
      embed_logo_url: chatbotLogo,
      chatbot_icon: chatbotIcon,
      custom_url_favicon: favicon,
    };

    updateState(optimisticUpdate); // Optimistically update the state

    run(async () => {
      setSavingChanges(true);
      try {
        const response = await customizationService.updateChatbotCustomization(
          chatbotId,
          {
            ...optimisticUpdate,
            font_name: updatedData.font_name,
            font_size: updatedData.font_size,
          }
        );
        if (response.status === 200) {
          // If chatbot_name was changed, update the chatbot store immediately for real-time updates
          if (updatedData.chatbot_name && updatedData.chatbot_name !== customizationData?.chatbot_name) {
            // Update the store immediately for real-time UI updates
            updateChatbotName(chatbotId, updatedData.chatbot_name);

            // Also update the main chatbot record in the background
            try {
              await chatbotService.patchUpdateChatbotName({
                kb_id: chatbotId,
                name: updatedData.chatbot_name,
              });
            } catch (nameUpdateError) {
              console.warn('Failed to update main chatbot name:', nameUpdateError);
              // Don't fail the whole operation if just the name update fails
            }
          }

          // Track customised event after successfully saving customization
          const user = useUserStore.getState().user;
          await trackKlaviyoEvent('customised', {
            chatbot_id: chatbotId
          });
          addSuccessToast({ message: 'Customization updated successfully' });
          setChatbotCustomization(response.data);
          changedFields.clear();
        }
      } catch (e) {
        setError(e);
        rollback(); // Rollback on failure
      } finally {
        setSavingChanges(false);
      }
    });
  };

  return {
    customizationData: state,
    patchCustomizationData,
    setChatbotCustomization,
    patching: savingChanges,
    savingChanges,
    loading: loading || pending,
    error,
  };
};

export default useCustomizationData;
