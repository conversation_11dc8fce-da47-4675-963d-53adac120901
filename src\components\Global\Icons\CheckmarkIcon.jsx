import * as React from 'react';
const CheckmarkIcon = ({ isValid, ...props }) => {
  // If isValid prop is provided, show the new circular designs
  if (isValid !== undefined) {
    return (
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d={isValid
            ? "M12 22.5C14.7848 22.5 17.4555 21.3938 19.4246 19.4246C21.3938 17.4555 22.5 14.7848 22.5 12C22.5 9.21523 21.3938 6.54451 19.4246 4.57538C17.4555 2.60625 14.7848 1.5 12 1.5C9.21523 1.5 6.54451 2.60625 4.57538 4.57538C2.60625 6.54451 1.5 9.21523 1.5 12C1.5 14.7848 2.60625 17.4555 4.57538 19.4246C6.54451 21.3938 9.21523 22.5 12 22.5ZM16.65 9.675C16.7386 9.55681 16.8031 9.42232 16.8398 9.2792C16.8765 9.13609 16.8846 8.98715 16.8637 8.8409C16.8428 8.69465 16.7933 8.55394 16.718 8.42682C16.6428 8.2997 16.5432 8.18864 16.425 8.1C16.3068 8.01136 16.1723 7.94686 16.0292 7.9102C15.8861 7.87353 15.7372 7.86541 15.5909 7.88631C15.4446 7.9072 15.3039 7.9567 15.1768 8.03197C15.0497 8.10724 14.9386 8.20681 14.85 8.325L11.1285 13.287L9.045 11.205C8.83174 11.0063 8.54967 10.8981 8.25822 10.9032C7.96676 10.9084 7.68869 11.0264 7.48257 11.2326C7.27645 11.4387 7.15838 11.7168 7.15324 12.0082C7.1481 12.2997 7.25628 12.5817 7.455 12.795L10.455 15.795C10.5689 15.9089 10.706 15.997 10.8569 16.0534C11.0079 16.1097 11.1692 16.133 11.3299 16.1216C11.4906 16.1102 11.647 16.0645 11.7885 15.9874C11.93 15.9104 12.0533 15.8038 12.15 15.675L16.65 9.675Z"
            : "M20.25 12C20.25 14.188 19.3808 16.2865 17.8336 17.8336C16.2865 19.3808 14.188 20.25 12 20.25C9.81196 20.25 7.71354 19.3808 6.16637 17.8336C4.61919 16.2865 3.75 14.188 3.75 12C3.75 9.81196 4.61919 7.71354 6.16637 6.16637C7.71354 4.61919 9.81196 3.75 12 3.75C14.188 3.75 16.2865 4.61919 17.8336 6.16637C19.3808 7.71354 20.25 9.81196 20.25 12ZM22.5 12C22.5 14.7848 21.3938 17.4555 19.4246 19.4246C17.4555 21.3938 14.7848 22.5 12 22.5C9.21523 22.5 6.54451 21.3938 4.57538 19.4246C2.60625 17.4555 1.5 14.7848 1.5 12C1.5 9.21523 2.60625 6.54451 4.57538 4.57538C6.54451 2.60625 9.21523 1.5 12 1.5C14.7848 1.5 17.4555 2.60625 19.4246 4.57538C21.3938 6.54451 22.5 9.21523 22.5 12ZM16.65 9.675C16.7386 9.55681 16.8031 9.42232 16.8398 9.2792C16.8765 9.13609 16.8846 8.98715 16.8637 8.8409C16.8428 8.69465 16.7933 8.55394 16.718 8.42682C16.6428 8.2997 16.5432 8.18864 16.425 8.1C16.3068 8.01136 16.1723 7.94686 16.0292 7.9102C15.8861 7.87353 15.7372 7.86541 15.5909 7.88631C15.4446 7.9072 15.3039 7.9567 15.1768 8.03197C15.0497 8.10724 14.9386 8.20681 14.85 8.325L11.1285 13.287L9.045 11.205C8.83174 11.0063 8.54967 10.8981 8.25822 10.9032C7.96676 10.9084 7.68869 11.0264 7.48257 11.2326C7.27645 11.4387 7.15838 11.7168 7.15324 12.0082C7.1481 12.2997 7.25628 12.5817 7.455 12.795L10.455 15.795C10.5689 15.9089 10.706 15.997 10.8569 16.0534C11.0079 16.1097 11.1692 16.133 11.3299 16.1216C11.4906 16.1102 11.647 16.0645 11.7885 15.9874C11.93 15.9104 12.0533 15.8038 12.15 15.675L16.65 9.675Z"
          }
          fill={isValid ? "#06BA63" : "#D1CFE2"}
        />
      </svg>
    );
  }

  // Default behavior for backward compatibility
  return (
    <svg
      width={20}
      height={20}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.885 4.982a.5.5 0 0 1 0 .707l-9.33 9.33a.5.5 0 0 1-.708 0l-3.732-3.732a.5.5 0 1 1 .707-.708l3.379 3.379 8.976-8.976a.5.5 0 0 1 .707 0"
        fill="currentColor"
      />
    </svg>
  );
};
export default CheckmarkIcon;
