# New Theme Design System

This document describes the new theme design system implemented for the application.

## Color Palette

The new theme color palette consists of the following colors:

- **Accent**: `#6351FF` - Primary brand color
- **Button**: `#EFEFF3` - Background color for secondary buttons
- **Hover**: `#D2D1E0` - Hover state for secondary buttons
- **Accent Hover**: `#3F29FF` - Hover state for primary buttons
- **Light**: `#FFFFFF` - Light background color
- **Dark**: `#0F0942` - Text and dark elements color
- **Stroke**: `#DFDEED` - Border color

## Implementation

The new theme design system is implemented in:

- **`src/styles/common.css`**: Contains all CSS variables and utility classes
- **Component files**: Use Tailwind classes directly for styling

## Typography

The design system uses the Inter Variable font:
- Imported globally in `common.css`
- Available weights: 100-900
- Used with Tailwind's `font-['Inter']` class

## Usage

All new theme colors use the `new-` prefix to avoid conflicts with existing colors.

### Basic Usage

```jsx
<div className="new-theme">
  <button className="new-btn-primary">Primary Button</button>
  <div className="new-bg-accent">Accent Background</div>
  <p className="new-text-dark">Dark Text</p>
</div>
```

### Available Classes

#### Background Colors
- `new-bg-accent` - Accent background color
- `new-bg-button` - Button background color
- `new-bg-hover` - Hover background color
- `new-bg-light` - Light background color
- `new-bg-dark` - Dark background color

#### Text Colors
- `new-text-accent` - Accent text color
- `new-text-dark` - Dark text color
- `new-text-light` - Light text color

#### Border Colors
- `new-border-accent` - Accent border color
- `new-border-stroke` - Stroke border color

#### Opacity Variants
- `new-bg-accent-10`, `new-bg-accent-20`, `new-bg-accent-50` - Accent background with opacity
- `new-bg-button-5`, `new-bg-button-10` - Button background with opacity
- `new-text-dark-50`, `new-text-dark-75` - Dark text with opacity
- `new-border-accent-20` - Accent border with opacity

#### Component Classes
- `new-btn-primary` - Primary button
- `new-btn-secondary` - Secondary button
- `new-btn-outline` - Outline button
- `new-card` - Card component
- `new-message-user` - User message bubble
- `new-message-assistant` - Assistant message bubble
- `new-input` - Input field

## Dark Mode Support

The new theme colors include dark mode support. When the `.dark` class or `[data-mode='dark']` attribute is applied to a parent element, the color variables will automatically adjust for dark mode.

## Custom Components

The design system includes the following custom components:

1. **UserAction** (`src/components/UserAction/index.jsx`)
   - A button component with the new design system
   - Supports default, hover, and sent states
   - Uses Inter font with semibold (600) weight
   - Styled with Tailwind classes

2. **UserMessage** (`src/components/UserMessage/index.jsx`)
   - A component for displaying user messages
   - Supports default, hover, and sent states
   - Uses Inter font with regular (400) weight
   - Styled with Tailwind classes 

3. **HumanAgent** (`src/components/HumanAgent/index.jsx`)
   - A component for displaying human agent messages
   - Shows agent name and role with a dot separator
   - Border radius: 16px
   - Uses Inter font with medium (500) weight for the role/name and regular (400) for the message
   - Styled with Tailwind classes
   - Supports dark mode

4. **MessageInput** (`src/components/MessageInput/index.jsx`)
   - A component for sending messages with voice recording capability
   - Features multiple states: default, hover, focus, and recording
   - Default state: Background #EFEFF3, rounded input with microphone icon
   - Hover state: Background #D2D1E0
   - Focus state: White background with #6351FF border
   - Recording state: Background #D2D1E0 with stop recording icon (#EA4335)
   - Uses custom icon components: NewMicrophoneIcon and NewStopRecordingIcon
   - Fully responsive and supports dark mode 