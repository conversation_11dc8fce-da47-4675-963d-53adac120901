import React, { useState } from 'react';
import UserAction from './index';

/**
 * UserActionExample - Demonstrates the UserAction component in a chat-like interface
 */
const UserActionExample = () => {
  const [messages, setMessages] = useState([]);
  const [selectedAction, setSelectedAction] = useState(null);

  // Sample quick actions
  const quickActions = [
    { id: 1, label: 'Tell me more' },
    { id: 2, label: 'How does it work?' },
    { id: 3, label: 'Book a demo' },
    { id: 4, label: 'Pricing options' }
  ];

  const handleActionClick = (action) => {
    setSelectedAction(action);
    
    // Add user message
    setMessages([...messages, { text: action.label, isUser: true }]);
    
    // Simulate response after a delay
    setTimeout(() => {
      const responses = {
        1: "I'd be happy to tell you more about our product! It's designed to help businesses streamline their operations and improve customer engagement.",
        2: "Our platform uses advanced AI to analyze your data and provide actionable insights. You simply connect your data sources, and we handle the rest.",
        3: "Great! I'd be happy to schedule a demo for you. Please provide your email and preferred date/time.",
        4: "We offer flexible pricing plans starting at $29/month for small businesses. Enterprise plans are also available with custom pricing."
      };
      
      setMessages(prev => [...prev, { text: responses[action.id], isUser: false }]);
      setSelectedAction(null);
    }, 1000);
  };

  return (
    <div className="new-theme max-w-md mx-auto p-4 border new-border-stroke rounded-lg">
      <div className="mb-6">
        <h2 className="text-xl font-medium new-text-dark mb-2">Chat Example</h2>
        <p className="text-sm new-text-dark-75">
          The UserAction buttons below demonstrate the new typography: 
          Inter Variable font, 16px size, 600 weight, 160% line height
        </p>
      </div>
      
      {/* Typography showcase */}
      <div className="mb-6 p-3 bg-gray-50 rounded-md">
        <h3 className="text-sm font-medium mb-2">Typography Specifications:</h3>
        <ul className="text-xs space-y-1 text-gray-600">
          <li><span className="font-medium">Font Family:</span> Inter Variable</li>
          <li><span className="font-medium">Font Size:</span> 16px</li>
          <li><span className="font-medium">Font Weight:</span> 600 (Semibold)</li>
          <li><span className="font-medium">Line Height:</span> 160% (25.6px)</li>
          <li><span className="font-medium">Default Color:</span> #6351FF (Accent)</li>
        </ul>
      </div>
      
      {/* Chat messages */}
      <div className="space-y-4 mb-6">
        <div className="max-w-md new-message-assistant">
          <p>Hello! How can I help you today?</p>
        </div>
        
        {messages.map((message, index) => (
          <div 
            key={index} 
            className={`max-w-md ${message.isUser ? 'ml-auto new-message-user' : 'new-message-assistant'}`}
          >
            <p>{message.text}</p>
          </div>
        ))}
      </div>
      
      {/* Quick actions */}
      <div className="flex flex-wrap gap-2">
        {quickActions.map(action => (
          <UserAction
            key={action.id}
            label={action.label}
            onClick={() => handleActionClick(action)}
            isSent={selectedAction?.id === action.id}
            disabled={selectedAction !== null}
          />
        ))}
      </div>
    </div>
  );
};

export default UserActionExample; 