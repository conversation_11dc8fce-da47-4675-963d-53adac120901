import { useEffect, useState, useRef } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import ReactRouterPrompt from 'react-router-prompt';
import { Transition } from '@headlessui/react';

import ChatbotShortcuts from '@/components/Chatbot/ChatbotShortcuts';
import Conversations from '@/components/Conversations';
import DLoading from '@/components/DLoading';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import useDanteApi from '@/hooks/useDanteApi';
import * as chatbotService from '@/services/chatbot.service';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import useLayoutStore from '@/stores/layout/layoutStore';
import { useUserStore } from '@/stores/user/userStore';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';
import clsx from 'clsx';
import * as customizationService from '@/services/customization.service';
import CompletionPopup from '@/components/Chatbot/Create/CompletionPopup';
import Bubble from '@/components/Bubble';
import VoicePreview from '@/components/Voice/VoicePreview';
import VoiceAgentIcon from '@/components/Global/Icons/VoiceAgentIcon';
import ConversationsIcon from '@/components/Global/Icons/ConversationsIcon';
import StyleTag from '@/components/StyleTag';

const ChatbotDetails = () => {
  const params = useParams();
  const location = useLocation();
  const isAboveMd = useIsAboveBreakpoint('md');
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setSelectedChatbot = useChatbotStore((state) => state.setSelectedChatbot);
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);
  const setChatbotCustomization = useCustomizationStore(
    (state) => state.setChatbotCustomization
  );
  const { auth } = useUserStore((state) => state);

  const [customizationData, setCustomizationData] = useState(null);
  const [shouldRefetchConversations, setShouldRefetchConversations] = useState(false);
  const [isVoicePreviewPlaying, setIsVoicePreviewPlaying] = useState(false);
  const [showCompletionPopup, setShowCompletionPopup] = useState(false);
  const [showVoicePreview, setShowVoicePreview] = useState(false);
  const [showConversations, setShowConversations] = useState(false);
  const isAboveSm = useIsAboveBreakpoint('sm');

  const { data, isLoading } = useDanteApi(
    chatbotService.getChatbotInfoById,
    [],
    {},
    params.id
  );

  const {
    data: dataOverview,
    loading: loadingOverview,
    refetch: refetchOverview,
  } = useDanteApi(customizationService.getChatbotOverviewById, [], {}, params.id);

  const { data: customizationRawData, isLoading: isLoadingCustomization } =
    useDanteApi(customizationService.getChatbotCustomizationById, [], {}, {
      kb_id: params.id,
    });

  const forceRefetchConversations = () => {
    setShouldRefetchConversations((prev) => !prev);
  };

  useEffect(() => {
    setSidebarOpen(false);
    setLayoutTitle(isAboveMd ? '' : 'Chatbot');
  }, [isAboveMd]);

  useEffect(() => {
    setSelectedChatbot(data?.results);
  }, [data, setSelectedChatbot]);

  useEffect(() => {
    setProgressBar([]);
  }, []);

  useEffect(() => {
    const fromChatbotCreation = location.state?.fromChatbotCreation;
    if (fromChatbotCreation && data?.results) {
      setShowCompletionPopup(true);
      window.history.replaceState({}, document.title, location.pathname);
    }
  }, [location, data]);

  useEffect(() => {
    if (customizationRawData) {
      const cd = {
        initial_messages: customizationRawData.initial_messages,
        prompt_suggestions: customizationRawData.prompt_suggestions,
        suggested_prompts_enabled: customizationRawData.suggested_prompts_enabled,
        suggested_prompts_appearance: customizationRawData.suggested_prompts_appearance,
        ...customizationRawData,
      };
      setCustomizationData(cd);
      setChatbotCustomization(customizationRawData);
    }
  }, [customizationRawData]);

  const handleVoicePreviewStateChange = (isPlaying) => {
    setIsVoicePreviewPlaying(isPlaying);
  };

  if (isLoading) {
    return <DLoading show={isLoading} />;
  }

  const bubbleConfig = {
    kb_id: params.id,
    access_token: auth?.access_token,
    chatbot_profile_pic: dataOverview?.icon,
    ...customizationData,
    name: dataOverview?.name,
    public: true,
    home_tab_enabled: true,
    initialActiveTab: 'chat',
  };

  const toggleVoicePreview = () => {
    setShowVoicePreview((prev) => !prev);
    if (!showVoicePreview) setShowConversations(false);
  };

  const toggleConversations = () => {
    setShowConversations((prev) => !prev);
    if (!showConversations) setShowVoicePreview(false);
  };

  return (
    <div className="flex flex-col w-full relative h-full">
      <div className="flex flex-col h-full md:min-h-0  w-full">
        <div className="flex flex-col h-full min-h-[65vh] md:h-[90vh] relative overflow-hidden p-3">
          {/* Grid Background Pattern */}
          <div
            className="absolute top-0 left-0 w-full h-full -z-0"
            style={{
              backgroundImage: `
                  linear-gradient(rgba(100, 100, 100, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(100, 100, 100, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px',
              backgroundColor: 'bg-white',
            }}
          />
          
          <div className="flex flex-col  md:z-20  md:absolute top-2 right-2 ">
            {/* Top Buttons Container */}
            <div className="flex justify-end items-center gap-4 mb-4">
              {/* Voice Preview Button */}
              <button
                className="group relative bg-purple-300 hover:bg-purple-400 text-white rounded-full p-3 shadow-lg transition-colors duration-300 ease-in-out flex items-center min-w-[48px]"
                onClick={toggleVoicePreview}
                title={showVoicePreview ? 'Hide Voice Agent' : 'Show Voice Agent'}
              >
                <VoiceAgentIcon className={clsx("size-5 flex-shrink-0", 'absolute left-0 right-0 top-0 bottom-0 m-auto', 'group-hover:static')} />
                <span
                  className={clsx(
                    'overflow-hidden whitespace-nowrap ml-2',
                    'max-w-0 group-hover:max-w-[300px]',
                    'transition-[max-width] duration-300 ease-in-out'
                  )}
                >
                  Turn your chatbot into an AI voice agent
                </span>
              </button>

              {/* Conversations Button */}
              <button
                className="group relative bg-purple-300 hover:bg-purple-400 text-white rounded-full p-3 shadow-lg transition-colors duration-300 ease-in-out flex items-center min-w-[48px]"
                onClick={toggleConversations}
                title={showConversations ? 'Hide Conversations' : 'Show Conversations'}
              >
                <ConversationsIcon className={clsx("size-5 flex-shrink-0", 'absolute left-0 right-0 top-0 bottom-0 m-auto', 'group-hover:static')} />
                <span
                  className={clsx(
                    'overflow-hidden whitespace-nowrap ml-2',
                    'max-w-0 group-hover:max-w-[150px]',
                    'transition-[max-width] duration-300 ease-in-out'
                  )}
                >
                  View Conversations
                </span>
              </button>
            </div>
          </div>

          <div className="flex flex-1 justify-center items-center gap-size5 max-h-[85vh] relative">
            <div className="w-full max-w-[500px] border border-grey-10 bg-white h-full rounded-size1 shadow-sm overflow-hidden">
              <StyleTag tag=".bubble" tempCustomizationData={customizationData} />
              {customizationData && (
                <Bubble
                  config={bubbleConfig}
                  type="chatbot"
                  isInApp
                  initialShouldFetchCustomization={false}
                  isPreviewMode={false}
                  hiddenPoweredByDante
                  forceRefetch={forceRefetchConversations}
                  onNewConversationCreated={() => forceRefetchConversations()}
                />
              )}
            </div>

            {/* Slide-in Voice Preview Panel */}
            <Transition
              show={showVoicePreview}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="opacity-0 translate-x-full"
              enterTo="opacity-100 translate-x-0"
              leave="transition ease-in-out duration-200 transform"
              leaveFrom="opacity-100 translate-x-0"
              leaveTo="opacity-0 translate-x-full"
            >
              <div className="w-[360px] bg-white/50 h-full backdrop-blur-sm z-10 shadow-lg absolute md:relative">
                <VoicePreview
                  chatbotId={params.id}
                  voiceId="a56a7938-bd6b-44b4-8e52-2c652946d528"
                  welcomeMessage={`I'm your AI Voice Assistant... ${customizationData?.initial_messages?.map(msg => msg.content).join(' ')}`}
                  phoneNumbers={customizationData?.phone_numbers}
                  personalityPrompt={customizationData?.personality_prompt}
                  hideCloseButton
                  place="chatbot-shortcuts"
                />
              </div>
            </Transition>

            {/* Slide-in Conversations Panel */}
            <Transition
              show={showConversations}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="opacity-0 translate-x-full"
              enterTo="opacity-100 translate-x-0"
              leave="transition ease-in-out duration-200 transform"
              leaveFrom="opacity-100 translate-x-0"
              leaveTo="opacity-0 translate-x-full"
            >
              <div className="w-[360px] bg-white/50 h-full backdrop-blur-sm z-10 shadow-lg rounded-size1">
                <Conversations
                  onClose={(type = 'close') => {
                    if (type === 'openConversation' && !isAboveSm) {
                      setShowConversations(false);
                    } else {
                      setShowConversations(false);
                    }
                  }}
                  forceRefetch={forceRefetchConversations}
                  shouldRefetch={shouldRefetchConversations}
                />
              </div>
            </Transition>
          </div>
        </div>
      </div>

      <CompletionPopup
        isOpen={showCompletionPopup}
        onClose={() => setShowCompletionPopup(false)}
        chatbotId={params.id}
      />

      <ReactRouterPrompt when={isVoicePreviewPlaying}>
        {({ isActive, onConfirm, onCancel }) => (
          <DConfirmationModal
            open={isActive}
            onClose={onCancel}
            onConfirm={onConfirm}
            title="Stop AI Voice Agent?"
            description="Leaving this page will stop the AI Voice Agent that is currently speaking. Are you sure you want to leave?"
            confirmText="Leave"
            cancelText="Cancel"
            variantConfirm="danger"
          />
        )}
      </ReactRouterPrompt>
    </div>
  );
};

export default ChatbotDetails;