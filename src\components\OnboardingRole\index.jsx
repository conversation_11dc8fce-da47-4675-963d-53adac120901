import DCheckbox from "../Global/DCheckbox"
import DInput from "../Global/DInput/DInput"
import { useState, useEffect, useRef } from "react"
import "./OnboardingRole.css";

const roles = [
    {
        id: 'founder',
        label: ' I am Founder/CEO',
        description: 'I\'m focused on strategic growth, business scaling, and ROI measurement',
        top_description: 'Perfect! As a founder, you\'ll get strategic growth tactics, scaling playbooks, and ROI measurement tools.',
    },
    {
        id: 'marketing',
        label: ' I am in Marketing',
        description: 'I\'m focused on lead generation, conversion optimization, and customer acquisition',
        top_description: 'Excellent! As a marketer, you\'ll get lead generation strategies, conversion optimization tactics, and customer acquisition guides.',
    },
    {
        id: 'engineering',
        label: ' I am an Engineer',
        description: 'I\'m focused on technical implementation, API integration, and custom development',
        top_description: 'Great choice! As an engineer, you\'ll get technical implementation guides, API documentation, and integration best practices.',
    },
    {
        id: 'sales',
        label: ' I am in Sales',
        description: 'I\'m focused on revenue acceleration, sales automation, and lead qualification',
        top_description: 'Smart pick! As a sales professional, you\'ll get revenue acceleration tactics, automation workflows, and lead qualification strategies.',
    },
    {
        id: 'customer_success',
        label: ' I am in Customer Success',
        description: 'I\'m focused on support excellence, response optimization, and customer satisfaction',
        top_description: 'Perfect fit! As a customer success leader, you\'ll get support optimization guides, satisfaction metrics, and response automation tools.'
    },
    {
        id: 'other',
        label: ' Other Roles',
        description: 'I have a different role or specific needs',
        top_description: 'Great! Tell us more about your role so we can personalize your experience.',
    }
]

const CheckIcon = () => (
   <svg width="17" height="14" viewBox="0 0 17 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.2321 1.14502C16.3444 1.24121 16.4365 1.35858 16.5034 1.49041C16.5702 1.62224 16.6105 1.76596 16.6217 1.91334C16.633 2.06072 16.6152 2.20889 16.5691 2.34936C16.5231 2.48983 16.4499 2.61986 16.3536 2.73202L7.35362 13.232C7.25272 13.3497 7.12857 13.4453 6.98896 13.5128C6.84936 13.5803 6.69732 13.6181 6.54239 13.6241C6.38745 13.63 6.23297 13.6038 6.08863 13.5472C5.94429 13.4905 5.81321 13.4047 5.70362 13.295L0.453621 8.04502C0.248791 7.83274 0.135529 7.54851 0.138231 7.25354C0.140932 6.95857 0.25938 6.67646 0.468064 6.46797C0.676747 6.25949 0.958968 6.1413 1.25394 6.13888C1.54892 6.13646 1.83304 6.24999 2.04512 6.45502L6.43712 10.8455L14.6466 1.26802C14.8408 1.0418 15.1169 0.901928 15.4142 0.879146C15.7115 0.856364 16.0057 0.951031 16.2321 1.14502Z" fill="currentColor"/>
</svg>
)

const OnboardingRole = ({ selectedRole: propSelectedRole, onSelect, onValidationChange, onAutoProgress }) => {
const noRoleSelectedText = 'While we\'re preparing your AI agent, tell us your role so we can personalize your experience!';

    const [selectedRole, setSelectedRole] = useState(propSelectedRole || null)
    const [customRole, setCustomRole] = useState('')
    const [showError, setShowError] = useState(false)
    const timeoutRef = useRef(null)

    // Check if current selection is valid
    const isValidSelection = () => {
        if (!selectedRole) return false
        if (selectedRole.id === 'other') {
            return customRole.trim().length > 0
        }
        return true
    }

    // Notify parent about validation status
    useEffect(() => {
        if (onValidationChange) {
            onValidationChange(isValidSelection())
        }
    }, [selectedRole, customRole, onValidationChange])

    const handleRoleSelect = (role) => {
        setSelectedRole(role)
        setShowError(false) // Clear error when selecting a role

        // Clear any existing timeout when switching roles
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
        }

        if (onSelect) {
            if (role.id === 'other') {
                if (customRole.trim()) {
                    // If there's already a custom role typed, don't call onSelect yet
                    // Let the user finish typing and the timer will handle it
                } else {
                    // No custom role yet, just indicate "other" is selected
                    onSelect('other')
                    setShowError(true) // Show error immediately if "Other" is selected but no custom role is provided
                }
            } else {
                // For predefined roles, call onSelect immediately
                onSelect(role.id)
            }
        }
    }

    const handleCustomRoleChange = (e) => {
        const value = e.target.value
        setCustomRole(value)
        setShowError(false) // Clear error when typing

        // Clear existing timeout to reset the 3-second countdown
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
        }

        if (selectedRole?.id === 'other') {
            if (value.trim()) {
                // Don't call onSelect immediately - wait for user to stop typing

                // Set 3-second auto-progression timer after user stops typing
                timeoutRef.current = setTimeout(() => {
                    // Now call onSelect with the final value
                    if (onSelect) {
                        onSelect(value.trim())
                    }

                    // Then trigger auto-progression
                    if (onAutoProgress) {
                        onAutoProgress()
                    }
                }, 3000)
            } else {
                // Empty value - call onSelect immediately to show error state
                if (onSelect) {
                    onSelect('other')
                }
                setShowError(true) // Show error if field is empty
            }
        }
    }

    // Cleanup timeout on unmount
    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current)
            }
        }
    }, [])
    return (
        <div className="flex flex-col justify-start lg:max-w-[498px]  w-full gap-[32px]   lg:px-0">
            <p className="text-[20px] leading-[160%] font-sora">{selectedRole ? selectedRole.top_description : noRoleSelectedText}</p>
            <div>
                {roles.map((role) => (
                    <div
                        key={role.id}
                        className={`role-option py-size3  px-[10px] lg:px-[20px] bg-transparent  hover:bg-white  rounded-size2 hover:cursor-pointer ${selectedRole?.id === role.id ? 'selected' : ''}`}
                        onClick={() => handleRoleSelect(role)}
                    >
                        <DCheckbox
                            label={role.label}
                            description={role.description}
                            checked={selectedRole?.id === role.id}
                            onChange={() => handleRoleSelect(role)}
                            fontSize="!text-base"
                            customIcon={<CheckIcon />}
                            checkboxSize="size-7"
                            checkedBgColor="data-[checked]:bg-[#6351FF]"
                            className="gap-4 font-[Inter] hover:cursor-pointer"
                        />
                        {role.description && (
                            <p className="role-description text-sm text-grey-50 font-[Inter] hover:cursor-pointer">
                                {role.description}
                            </p>
                        )}
                        {role.id === 'other' && selectedRole?.id === 'other' && (
                            <div className="mt-size2 ml-[2.8rem]">
                                <DInput
                                    placeholder="Please specify your role..."
                                    value={customRole}
                                    onChange={handleCustomRoleChange}
                                    className={`w-full ${showError && !customRole.trim() ? 'border-red-500 border-2' : ''}`}
                                    error={showError && !customRole.trim()}
                                />
                                {showError && !customRole.trim() && (
                                    <p className="text-red-500 text-sm mt-1 font-[Inter]">
                                        Please specify your role to continue
                                    </p>
                                )}
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    )
}

export default OnboardingRole;
