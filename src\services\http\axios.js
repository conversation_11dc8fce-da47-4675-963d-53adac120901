import axios from 'axios';
import { useUserStore } from '@/stores/user/userStore';
import useToastStore from '@/stores/toast/toastStore';
import { NOTIFICATION_MESSAGES, STATUS } from '@/constants';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';

const instance = axios.create({
  timeout: 1000 * 60 * 10,
});


const addToast = useToastStore.getState().addToast;

const isInApp = () =>
  !(window.location.pathname.startsWith('/embed') || window.location.pathname.startsWith('/share'));


const getErrorMessage = (response) => {
  if (response?.data?.detail && typeof response.data.detail === 'string') {
    return NOTIFICATION_MESSAGES[response.data.detail] || response.data.detail;
  }
  return isInApp()
    ? NOTIFICATION_MESSAGES.FAILED_IN_APP
    : NOTIFICATION_MESSAGES.FAILED_IN_EMBED;
};


instance.interceptors.request.use((config) => {
  const userStore = useUserStore.getState();
  const teamManagementStore = useTeamManagementStore.getState();

  // Only add Bearer token if there's no x-api-key header present
  // This prevents conflicts when demo endpoints use x-api-key authentication
  const token = userStore.auth.access_token;
  const hasApiKey = config.headers && config.headers['x-api-key'];
  if (token && isInApp() && !hasApiKey) {
    config.headers.Authorization = `Bearer ${token}`;
  }


  config.headers['x-client'] = 'dante';
  config.headers['Called-From'] = 'app.dante-ai.com';


  const selectedTeamId = teamManagementStore.selectedTeam?.id;
  if (selectedTeamId) {
    config.params = {
      ...config.params,
      team_id: selectedTeamId,
    };
  }

  return config;
});


instance.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Request failed:', error);

    if (error?.response?.status === 402) {
      window.open('/onboarding/upgrade?trial_ended=true', '_self');
      return;
    }

    // if (error?.response?.status === 426) {
    //   window.open('/onboarding/upgrade?old_to_new=true', '_self');
    //   return;
    // }

    if (error?.response?.status === 401) {
      addToast({
        message: 'Your session has expired. Please log in again.',
        type: STATUS.ERROR,
      });
      return Promise.reject(error);
    }


    const message = getErrorMessage(error.response);

    addToast({
      message,
      type: STATUS.ERROR,
    });

    return Promise.reject(error);
  }
);

export default instance;
