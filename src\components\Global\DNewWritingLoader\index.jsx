import Lottie from 'lottie-react';
import animationData from './NewSpinner.json';

const DNewWritingLoader = () => {
  return (
    <>
   <style>{`.d-new-writing-loader svg path {fill: var(--dt-color-element-100);}`}</style>

    <Lottie
      isClickToPauseDisabled
      ariaRole=""
      animationData={animationData}
      loop={true}
      autoplay={true}
      style={{ height: 150, width: 60, margin: 0 }}
      eventListeners={[]}
    ></Lottie>
  </>
  );
};

export default DNewWritingLoader;