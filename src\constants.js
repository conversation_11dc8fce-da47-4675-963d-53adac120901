import { v4 as uuidv4 } from 'uuid';

// Breakpoints object
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  preview: '1100px',
  xl: '1280px',
  '2xl': '1536px',
  '1600px': '1600px',
  '3xl': '1920px',
  tall: { raw: '(min-height: 800px)' },
  'xl-max': { raw: '(max-width: 1279px)' },
  'bubble-preview-min-height': { raw: '(min-height: 1080px)' }
};

export const DANTE_ICON = 'https://chat.dante-ai.com/btn-embed-dark.png';

export const DANTE_THEME_COLOR_CHAT = {
  brand_color: '#8275F7',
  element_color: '#000000',
  surface_color: '#FFFFFF',
  alert_color: '#FF9C28',
  positive_color: '#0ED909',
  negative_color: '#FF3528'
};

// New theme color palette
export const NEW_THEME_COLOR_CHAT = {
  brand_color: '#6351FF',     // Accent
  element_color: '#0F0942',   // Dark
  surface_color: '#FFFFFF',   // Light
  alert_color: '#FF9C28',     // Keeping alert color
  positive_color: '#0ED909',  // Keeping positive color
  negative_color: '#FF3528',  // Keeping negative color
  button_color: '#EFEFF3',    // Button
  hover_color: '#D2D1E0',     // Hover
  accent_hover: '#3F29FF',    // Accent Hover
  stroke_color: '#DFDEED'     // Stroke
};

// New theme colors with separate namespace
export const NEW_THEME = {
  accent: '#6351FF',
  button: '#EFEFF3',
  hover: '#D2D1E0',
  accent_hover: '#3F29FF',
  light: '#FFFFFF',
  dark: '#0F0942',
  stroke: '#DFDEED',
  // Keep existing alert colors
  alert: '#FF9C28',
  positive: '#0ED909',
  negative: '#FF3528'
};

export const DANTE_THEME_CHAT = {
  name: 'AI Chatbot Name',
  ...DANTE_THEME_COLOR_CHAT,
  public: true,
  font_name: 'Inter',
  font_size: 16,
  input_placeholder_text: 'Write your message',
  embed_logo_url: '',
  chatbot_icon: DANTE_ICON,
  hidden_powered_by_dante: false,
  initial_messages: [{
    role: 'assistant',
    type: 'welcome_message',
    content: '👋 Hi there how can I help you?'
  }],
  custom_css: ''
};

export const STATUS = {
  PENDING: 'PENDING',
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  WORKING: 'WORKING',
  ERROR: 'ERROR',
  AI: 'AI',
  TAKEN: 'TAKEN',
  CLOSED: 'CLOSED',
  DROPPED: 'DROPPED',
  RESOLVED: 'RESOLVED',
  UNVERIFIED: 'UNVERIFIED',
  VERIFIED: 'VERIFIED'
};

export const TASK_TYPES = {
  SUGGESTION: 'Suggestion',
  TRAINING: 'Training'
};

export const iconToCompanyName = {
  OpenAiLogoIcon: 'Open AI',
  ClaudeLogoIcon: 'Anthropic',
  CohereIcon: 'Cohere',
  GoogleLogoIcon: 'Google'
};

export const NOTIFICATION_MESSAGES = {
  FAILED_IN_APP: 'We could not process your request. Please try again and if the issue persists, contact <NAME_EMAIL> for help.',
  FAILED_IN_EMBED: 'We could not process your request. Please try again and if the issue persists, contact support for help.',
  FAILED_LARGE_FILE: 'The files you are trying to upload are larger than maximum limit of 128Mb. Please make sure to be below this limit.',
  USER_NOT_VERIFIED: 'Your account is not verified. Please verify your account and then try again',
  LOGIN_BAD_CREDENTIALS: 'Wrong email or password provided. Please double check your credentials',
  VERIFY_USER_ALREADY_VERIFIED: 'This account is already verified and you can login',
  REGISTER_USER_ALREADY_EXISTS: 'This email is already registered',
  LOGIN_BAD_OTP: 'Invalid OTP code',
  LOGIN_BAD_EMAIL: 'Invalid email',
  LOGIN_BAD_PASSWORD: 'Invalid password',
  RESET_PASSWORD_BAD_TOKEN: 'This link has expired. Please request a new password reset link.',
  GOOGLE_USER_NOT_FOUND: 'This Google account is not registered. Please sign up first to create an account.'
};

export const AGENT_MEMBER_PERMISSIONS = [
  {
    name: 'View only',
    description: 'Can view chatbots and access analytics.',
    value: 'view_only'
  },
  {
    name: 'Chat agent',
    description: 'Can chat with chatbots.',
    value: 'chat_agent'
  }
];

export const COMMON_CLASSNAMES = {
  transition: {
    duration: {
      short: 'duration-150',
      medium: 'duration-300',
      long: 'duration-500'
    },
    delay: {
      short: 'delay-75',
      medium: 'delay-150',
      long: 'delay-300'
    }
  },
  previewBubble: 'w-[368px] lg:w-[368px] 2xl:w-[440px] h-full bubble-preview-min-height:h-[790px] relative flex flex-col gap-size2 rounded-size1 flex-shrink-0 grow'

}

export const VOICE_DASHBOARD_DEFAULT_VOICES = [
  {
    id: '1',
    name: 'Voice 1',
    chatbotName: 'Chatbot 1',
    phoneNumbers: [{ number: '+***********' }, { number: '+***********' }]
  },
  {
    id: '2',
    name: 'Voice 2',
    chatbotName: 'Chatbot 2',
    phoneNumbers: [{ number: '+33600000002' }, { number: '+33600000003' }]
  }
]

export const DAYS_OF_WEEK = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

export const TIME_OPTIONS = Array.from({ length: 96 }, (_, index) => ({
  value: `${(Math.floor(index / 2) % 24).toString().padStart(2, '0')}:${(index % 2 === 0 ? '00' : '30')}`,
  label: `${(Math.floor(index / 2) % 24).toString().padStart(2, '0')}:${(index % 2 === 0 ? '00' : '30')}`
}));

export const LLM_MODEL_DEFAULT = {
  Logo: 'OpenAiLogoIcon',
  value: 'gpt-4-omnimodel-mini',
  label: 'GPT-4o Mini',
  description:
    'Advanced cost-efficient intelligence. 0.25 credits per response.',
  credits: 0.25
};

export const ACCEPTED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/svg+xml'];

export const MODULE_NAMES = {
  chatbot: {
    name: 'AI Chatbot',
    singular: 'AI Chatbot',
    plural: 'AI Chatbots'
  },
  personas: {
    name: 'AI Digital Persona',
    singular: 'AI Digital Persona',
    plural: 'AIDigital Personas'
  },
  voice: {
    name: 'AI Voice',
    singular: 'AI Voice',
    plural: 'AI Voices'
  },
  handover: {
    name: 'Human Handover',
    singular: 'Human Handover',
    plural: 'Human Handovers'
  },
  insights: {
    name: 'Insights',
    singular: 'Insight',
    plural: 'Insights'
  }
};

export const fakerSlots = {
  quick_links: [
    //   {
    //   id: uuidv4(),
    //   user_id: 'e3a81a8f-7eab-415c-b9d8-1477103e7d2b',
    //   kb_id: 'ad8d93de-50cb-442f-b1a6-abe9606511d3',
    //   title: '📗 Guides: Getting sterted with Dante AI',
    //   url: 'https://www.dante-ai.com/guides',
    //   open_in_new_tab: true,
    //   pull_meta_data: false,
    //   order: 0,
    //   disclaimer: 'Quick link',
    //   type: 'quick_link'
    // },
    {
      id: uuidv4(),
      user_id: 'e3a81a8f-7eab-415c-b9d8-1477103e7d2b',
      kb_id: 'ad8d93de-50cb-442f-b1a6-abe9606511d3',
      title: '🗓️ Book a Demo',
      url: 'https://calendly.com/jetelira/dante-ai-demo',
      open_in_new_tab: true,
      pull_meta_data: false,
      order: 0,
      disclaimer: 'Quick link',
      type: 'quick_link'
    },
  ],
  meta_links: [
    {
      id: uuidv4(),
      user_id: 'e3a81a8f-7eab-415c-b9d8-1477103e7d2b',
      kb_id: 'ad8d93de-50cb-442f-b1a6-abe9606511d3',
      title: "A marathon of sprints: one leader's philosophy for building an AI startup",
      url: 'https://www.raconteur.net/leadership/one-tech-leaders-philosophy-for-building-an-ai-startup',
      open_in_new_tab: true,
      pull_meta_data: true,
      order: 4,
      disclaimer: 'Meta links disclaimer',
      type: 'meta_link',
      description: 'This pivotal moment underscores the essence of our mission at Dante AI. We\'re not just part of this transformation; we\'re leading it by enabling businesses to navigate the shift from traditional systems to AI-driven innovation.',
      image_url: 'https://dante-public-files.lon1.cdn.digitaloceanspaces.com/Jeta.png'
    }
  ],
  link_groups: [
    {
      kb_id: 'ad8d93de-50cb-442f-b1a6-abe9606511d3',
      title: '📗 Guides',
      items: [
        {
          id: 1,
          name: "How to access Dante AI's API?",
          url: 'https://www.dante-ai.com/guides/how-to-access-dante-ais-api',
          order: 0
        },
        {
          id: 2,
          name: 'How to integrate with WhatsApp?',
          url: 'https://www.dante-ai.com/guides/whatsapp-integration',
          order: 1
        }
      ],
      open_all_in_new_tab: true,
      order: 5,
      type: 'link_group',
      disclaimer: 'Link group'
    }
  ],
  sliders: [
    {
      kb_id: '',
      title: '🗞️ AI News and Views',
      items: [
        {
          id: uuidv4(),
          url: 'https://www.dante-ai.com/article/chatbot-vs-conversational-ai-chatbots-whats-the-difference',
          label: '',
          name: 'Chatbot vs Conversational AI Chatbots: What\'s the Difference?',
          open_in_new_tab: true,
          description: 'This article dives into the differences between traditional chatbots and AI-powered chatbots, examining how they function, their unique features, and when each type is best suited for a business. ',
          thumbnail_url: 'https://dante-public-files.lon1.cdn.digitaloceanspaces.com/Image1.png',
          order: 0
        },
        {
          id: uuidv4(),
          url: 'https://www.dante-ai.com/article/what-are-the-risks-of-ai-in-customer-service',
          label: '',
          name: 'What are the Risks of AI in Customer Service?',
          open_in_new_tab: true,
          description: "In this article, we'll explore some important aspects of AI adoption, such as data privacy, technical reliability, and maintaining the human touch in service interactions.",
          thumbnail_url: 'https://dante-public-files.lon1.cdn.digitaloceanspaces.com/Image2.png',
          order: 1
        },
        {
          id: uuidv4(),
          url: 'https://www.dante-ai.com/article/what-companies-use-ai-for-customer-service',
          label: '',
          name: 'What Companies Use AI for Customer Service?',
          open_in_new_tab: true,
          description: 'This article explores how companies across industries are transforming customer service with artificial intelligence (AI). Discover how AI-driven tools like chatbots and voice recognition technology streamline routine tasks, allowing human agents to focus on complex issues. ',
          thumbnail_url: 'https://dante-public-files.lon1.cdn.digitaloceanspaces.com/Image3.png',
          order: 2
        }
      ],
      open_all_in_new_tab: true,
      order: 5,
      type: 'slider',
      disclaimer: 'Slider'
    }
  ],
  video: []
}

export const SYSTEM_FONTS = [
  'Inter',
  'Arial',
  'Helvetica',
  'Georgia',
  'Times New Roman',
  'Tahoma',
  'Trebuchet MS',
  'Verdana',
  'Courier New',
  'Impact',
  'Lucida Sans Unicode',
  'Geneva',
  'Lucida Console',
];

export const EMAILS = {
  SALES: '<EMAIL>'
}

export const DEFAULT_CHAT_MESSAGES = {
  CONSENT_MESSAGE: 'By continuing, you agree to share your information with us for the purpose of providing you with a personalized experience.'
}

export const TRIAL_DAYS = 14;

export const LIVE_AGENT_AVAILABILITY = {
  ALWAYS: 'always',
  OFFICE_HOURS: 'office_hours',
  CUSTOM: 'custom'
}

export const HUMAN_HANDOVER_CHAT_BOX_LIMIT = 3;

export const TASK_POLLING_INTERVAL = 3000;

export const MAX_URLS_PER_ENTIRE_DOMAIN = 200;

export const NOTIFICATION_SOUND_URL = 'https://dante-public-files.lon1.cdn.digitaloceanspaces.com/notification_sound.mp3';

export const CHATBOTS_WITH_NEW_DESIGN = [
  '996c0c59-c322-4194-a016-a85f45c365ed',
  '86927718-7690-4c0c-a99d-8bc8afda0a4c'
]