import React from 'react';
import HumanAgent from '../../components/HumanAgent';
import LayoutMain from '../../layouts/LayoutMain';

const HumanAgentExample = () => {
  return (
    <LayoutMain>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Human Agent Component Examples</h1>
        
        <div className="space-y-6">
          {/* Basic example */}
          <div>
            <h2 className="text-lg font-medium mb-3">Standard Human Agent</h2>
            <HumanAgent
              name="<PERSON><PERSON>"
              role="Customer Support Specialist"
              message="Hello! How can I help you today with your questions about our product?"
            />
          </div>
          
          {/* Long message example */}
          <div>
            <h2 className="text-lg font-medium mb-3">With Long Message</h2>
            <HumanAgent
              name="Alex"
              role="Technical Advisor"
              message="I understand you're having trouble with the integration. Let me walk you through the process step by step. First, make sure you have the latest version installed. Then, check your API credentials to ensure they're correctly configured in the settings panel. If you're still experiencing issues after that, we can schedule a one-on-one session to troubleshoot further."
            />
          </div>
          
          {/* Short name and role */}
          <div>
            <h2 className="text-lg font-medium mb-3">With Short Name and Role</h2>
            <HumanAgent
              name="Sam"
              role="Agent"
              message="Thanks for reaching out. I'll look into this issue right away."
            />
          </div>
          
          {/* Custom styling */}
          <div>
            <h2 className="text-lg font-medium mb-3">With Custom Styling</h2>
            <HumanAgent
              name="Maria"
              role="Senior Support Engineer"
              message="I've checked your account settings and everything looks correct. Your subscription will renew automatically on the 15th of next month."
              className="max-w-md shadow-sm"
            />
          </div>
        </div>
      </div>
    </LayoutMain>
  );
};

export default HumanAgentExample; 