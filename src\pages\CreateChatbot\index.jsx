import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import Bubble from '@/components/Bubble';
import { StepEnum } from '@/components/Chatbot/Create/StepEnum';
import { customizeSteps } from '@/components/Chatbot/Create/steps';
import DAlert from '@/components/Global/DAlert';
import DButton from '@/components/Global/DButton';
import {
  COMMON_CLASSNAMES,
  DANTE_THEME_CHAT,
  fakerSlots,
  SYSTEM_FONTS,
} from '@/constants';
import convertThemeToCSSVariablesStyle from '@/helpers/convertThemeToCSSVariables';
import generateInitialMessageDynamically from '@/helpers/generateInitialMessageDynamically';
import useDanteApi from '@/hooks/useDanteApi';
import useTaskHandler from '@/hooks/useTaskHandler';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import LayoutWithButtons from '@/layouts/LayoutWithButtons';
import * as chatbotService from '@/services/chatbot.service';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import { useCreateChatbotStore } from '@/stores/chatbot/createChatbotStore';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import useLayoutStore from '@/stores/layout/layoutStore';
import { useUserStore } from '@/stores/user/userStore';
import Creation from './Creation';
import Customization from './Customization';
import * as customizationService from '@/services/customization.service';
import generateGoogleFonts from '@/helpers/generateGoogleFonts';
import compareObjects from '@/helpers/compareObjects';
import ReactRouterPrompt from 'react-router-prompt';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import StyleTag from '@/components/StyleTag';
import useToast from '@/hooks/useToast';
import DTransition from '@/components/Global/DTransition';
import checkEnv from '@/helpers/environmentCheck';
import useTaskStore from '@/stores/task/taskStore';
import { TASK_TYPES } from '@/constants';
import { trackKlaviyoEvent } from '@/services/chatbot.service';
import {
  trackSubmitChatbotName,
  trackUploadChatbotMemory,
  trackSelectChatbotPersonality,
} from '@/helpers/analytics';
import useModalStore from '@/stores/modal/modalStore';
import InlineGlobalModals from '@/components/InlineGlobalModals';

const CreateChatbot = () => {
  //store
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const currentStep = useChatbotStore((state) => state.currentStep);
  const setCurrentStep = useChatbotStore((state) => state.setCurrentStep);
  const updateStep = useChatbotStore((state) => state.updateStep);
  const { handleTask } = useTaskHandler();
  const { openInlinePlansModal, showInlinePlans } = useModalStore((state) => state);

  const progressBar = useLayoutStore((state) => state.progressBar);
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);

  const auth = useUserStore((state) => state.auth);
  const user = useUserStore((state) => state.user);
  const tasks = useTaskStore((state) => state.tasks);
  const resetChatbotData = useCreateChatbotStore(
    (state) => state.resetChatbotData
  );

  const chatbotData = useCreateChatbotStore((state) => state.chatbotData);
  const setChatbotData = useCreateChatbotStore((state) => state.setChatbotData);
  const setIsInPreviewBubblePage = useLayoutStore(
    (state) => state.setIsInPreviewBubblePage
  );
  const chatbots = useChatbotStore((state) => state.chatbots);

  const { addWarningToast, removeToast, addErrorToast } = useToast();

  //state
  const [knowledgeBase, setKnowledgeBase] = useState(null);
  const [slots, setSlots] = useState([]);
  const [originalChatbotData, setOriginalChatbotData] = useState(chatbotData);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [taskId, setTaskId] = useState(null);

  //last step
  const [lastStep, setLastStep] = useState(false);
  const [isCreateStepsCompleted, setIsCreateStepsCompleted] = useState(false);
  const [isTraining, setIsTraining] = useState(false);
  const [trainingFinished, setTrainingFinished] = useState(false);
  const [pendingUpload, setPendingUpload] = useState(false);

  //error
  const [errors, setErrors] = useState({ nameError: '', knowledgeError: '' });

  const [saveChanges, setSaveChanges] = useState(false);
  const [chatbotCustomizating, setChatbotCustomizating] = useState(false);
  const [customizationSaved, setCustomizationSaved] = useState(false);
  const [importFontCss, setImportFontCss] = useState('');
  const [enableHomeTab, setEnableHomeTab] = useState(true);
  const [enableAvatarTab, setEnableAvatarTab] = useState(true);

  const navigate = useNavigate();
  const setSelectedChatbot = useChatbotStore(
    (state) => state.setSelectedChatbot
  );
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);

  //temp customization data
  const [tempCustomizationData, setTempCustomizationData] = useState({
    ...DANTE_THEME_CHAT,
  });

  //api calls
  const { data: personalities } = useDanteApi(chatbotService.getPersonalities);

  // Check if user has reached chatbot limit
  useEffect(() => {
    if (user) {
      // Only show the modal if both values are defined numbers, not zero, and equal to each other
      if (
        typeof user.used_ai_chatbots === 'number' &&
        typeof user.total_ai_chatbots === 'number' &&
        user.used_ai_chatbots > 0 &&
        user.used_ai_chatbots === user.total_ai_chatbots
      ) {
        openInlinePlansModal('number_of_knowledgebases');
      }
    }
  }, [user?.total_ai_chatbots]);

  //functions
  const isValidUrl = (url) => {
    try {
      // Use URL constructor to validate URL
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  const validateSteps = () => {
    let newErrors = {
      nameError: '',
      knowledgeError: '',
      personalityError: '',
      knowledgeUrlError: '',
    }; // Initialize new errors object

    // Step 0: Validate chatbot name
    if (currentStep === 0 && chatbotData.chatbotName.trim() === '') {
      newErrors.nameError = 'Please enter an AI Chatbot name';
    } else {
      newErrors.nameError = '';
    }

    // Step 1: Validate knowledge sources
    if (currentStep === 1) {
      const filteredURLs = chatbotData.chatbotUrls.filter(
        (item) => item.url && item.url !== 'https://'
      );

      const hasValidUrl = filteredURLs.some((url) => isValidUrl(url.url));

      if (
        filteredURLs.length === 0 &&
        chatbotData.chatbotFiles.length === 0 &&
        chatbotData.chatbotBulkUploadedUrls.length === 0
      ) {
        newErrors.knowledgeError = 'Please add at least one URL or file';
      } else if (
        filteredURLs.length > 0 &&
        filteredURLs[0].url &&
        !hasValidUrl
      ) {
        newErrors.knowledgeUrlError = 'Please ensure to add a valid URL';
      } else {
        newErrors.knowledgeUrlError = '';
        newErrors.knowledgeError = '';
      }
    }

    // Step 2: Validate personality template
    if (currentStep === 2 && !chatbotData.personalities_template) {
      newErrors.personalityError = 'Please select a personality template';
    }

    // Update errors state
    setErrors(newErrors);

    // Return true if no errors
    return (
      !newErrors.nameError &&
      !newErrors.knowledgeError &&
      !newErrors.knowledgeUrlError &&
      !newErrors.personalityError
    );
  };

  const handleNext = async () => {
    const isValid = validateSteps();
    if (!isValid) {
      return;
    }
    const allSteps = progressBar;
    const currentIndex = allSteps.findIndex((step) => step.id === currentStep);

    if (currentStep === StepEnum.CHATBOT_NAME) {
      if (checkEnv()) {
        trackSubmitChatbotName({
          chatbot_name: chatbotData.chatbotName,
          user_id: user?.email,
        });
        try {
          await chatbotService.klaviyoChatbotName(chatbotData.chatbotName);
        } catch (error) {
          console.log('error', error);
        }
      }
    } else if (currentStep === StepEnum.KNOWLEDGE) {
      if (checkEnv()) {
        trackUploadChatbotMemory({
          has_uploaded_url:
            (chatbotData.chatbotUrls.length > 0 &&
              chatbotData.chatbotUrls[0].url !== 'https://') ||
            chatbotData.chatbotBulkUploadedUrls.length > 0,
          has_uploaded_file: chatbotData.chatbotFiles.length > 0,
          user_id: user?.email,
        });
        // Track docs-links completed event
        await trackKlaviyoEvent('docs-links complete', {
          chatbot_name: chatbotData.chatbotName,
          files_count: chatbotData.chatbotFiles.length,
          urls_count: chatbotData.chatbotUrls.filter(
            (url) => url.url && url.url !== 'https://'
          ).length,
        });
      }
    } else if (currentStep === StepEnum.PERSONALITY_CREATE) {
      if (checkEnv()) {
        trackSelectChatbotPersonality({
          personality_type: chatbotData.personalities_template,
          user_id: user?.email,
        });
        // Track personality completed event
        await trackKlaviyoEvent('personality complete', {
          chatbot_name: chatbotData.chatbotName,
          personality_template: chatbotData.personalities_template || 'default',
        });
      }
    }

    if (currentIndex === allSteps.length - 2) {
      setLastStep(true);
    }

    if (currentIndex < allSteps.length - 1) {
      // Update current step state in global store
      updateStep(currentStep, {
        completed: true,
        active: false,
        pending: false,
      });

      const nextStepId = allSteps[currentIndex + 1].id;
      setCurrentStep(nextStepId);

      // Update progressBar steps in global store
      const updatedSteps = allSteps.map((step) => {
        if (step.id === currentStep) {
          return { ...step, completed: true, active: false, pending: false };
        } else if (step.id === nextStepId) {
          return { ...step, active: true, pending: false, completed: false };
        }
        return step;
      });
      const newMessages = generateInitialMessageDynamically();
      setTempCustomizationData({
        ...tempCustomizationData,
        initial_messages: [newMessages],
      });

      setProgressBar(updatedSteps);
    }
  };

  const handleBack = () => {
    const allSteps = progressBar;
    const currentIndex = allSteps.findIndex((step) => step.id === currentStep);
    if (currentIndex === allSteps.length - 1) {
      setLastStep(false);
    }

    if (currentIndex > 0) {
      const prevStepId = allSteps[currentIndex - 1].id;
      const currentStepId = allSteps[currentIndex].id;

      updateStep(currentStepId, {
        active: false,
        pending: true,
        completed: false,
      });

      setCurrentStep(prevStepId);

      const updatedSteps = allSteps.map((step) => {
        if (step.id === prevStepId) {
          return { ...step, active: true, pending: false, completed: false };
        } else if (step.id === currentStepId) {
          return { ...step, active: false, pending: true, completed: false };
        }
        return step;
      });
      const newMessages = generateInitialMessageDynamically();
      setTempCustomizationData({
        ...tempCustomizationData,
        initial_messages: [newMessages],
      });

      setProgressBar(updatedSteps);
    }
  };

  const handleCustomizeChatbot = async () => {
    try {
      const response = await customizationService.updateChatbotCustomization(
        knowledgeBase.id,
        {
          suggested_prompts_enabled: true,
          prompt_suggestions: tempCustomizationData.prompt_suggestions,
        }
      );

      const chatbotResponse = await chatbotService.getChatbotById(
        knowledgeBase.id
      );
      setProgressBar(customizeSteps);
      setCurrentStep(StepEnum.SETUP);
      setTrainingFinished(false);
      setTempCustomizationData(response?.data);
      setIsCreateStepsCompleted(true);
      setSaveChanges(false);
      setChatbotCustomizating(true);

      //TODO: this is a temporary solution to activate the new design
      setSelectedChatbot(chatbotResponse?.data?.results);
    } catch (error) {
      console.log('error', error);
    }
  };

  useEffect(() => {
    setLayoutTitle('Create AI Chatbot');
    setIsInPreviewBubblePage(true);
  }, []);

  // Remove chatname error if chatname field is not empty
  useEffect(() => {
    if (currentStep === StepEnum.CHATBOT_NAME && errors.nameError) {
      validateSteps();
      console.log('errors', errors);
    }
  }, [currentStep, errors.nameError, chatbotData.chatbotName]);

  useEffect(() => {
    if (tempCustomizationData?.font_name) {
      const fontCss = generateGoogleFonts(tempCustomizationData?.font_name);
      if (fontCss !== importFontCss) {
        const fontLinkId = `preload-font-${tempCustomizationData.font_name}`;

        setImportFontCss(fontCss);
        if (
          !document.getElementById(fontLinkId) &&
          !SYSTEM_FONTS.includes(tempCustomizationData.font_name)
        ) {
          const link = document.createElement('link');
          link.id = fontLinkId;
          link.rel = 'stylesheet';
          link.as = 'style';
          link.href = fontCss;
          document.head.appendChild(link);
        }
      }
    }
  }, [tempCustomizationData?.font_name]);

  useEffect(() => {
    const newMessages = generateInitialMessageDynamically();
    setTempCustomizationData({
      ...tempCustomizationData,
      initial_messages: [newMessages],
    });
  }, []);

  useEffect(() => {
    if (!chatbotData || !originalChatbotData || trainingFinished) {
      return;
    }

    const hasUnsavedChanges = !compareObjects(originalChatbotData, chatbotData);
    setUnsavedChanges(hasUnsavedChanges);
  }, [chatbotData, originalChatbotData]);

  // useEffect(() => {
  //   if (isTraining) {
  //     addWarningToast({
  //       message: 'Please wait until training process is over.',
  //       duration: 1000 * 60 * 60 * 24, // 24 hours
  //       id: 'training-toast',
  //     });
  //   } else {
  //     removeToast('training-toast');
  //   }
  // }, [isTraining]);

  return (
    <div className="flex flex-col md:flex-row gap-size3 h-full relative">
      <LayoutRightSidebar
        RightSidebar={() =>
          !isTraining && (
            <div className={COMMON_CLASSNAMES.previewBubble}>
              <StyleTag
                tag=".bubble"
                tempCustomizationData={tempCustomizationData}
              />
              <Bubble
                config={{
                  ...tempCustomizationData,
                  name: chatbotData.chatbotName || DANTE_THEME_CHAT.name,
                  access_token: auth.access_token,
                  ...knowledgeBase,
                  kb_id: knowledgeBase?.id,
                  powered_by_dante: true,
                  public: true,
                  home_tab_enabled:
                    (currentStep === StepEnum.TABS && enableHomeTab) ||
                    currentStep === StepEnum.STYLING,
                  suggested_prompts_enabled: true,
                  training_finished: trainingFinished,
                }}
                type="chatbot"
                isInApp={true}
                isPreviewMode={
                  !(!isTraining && trainingFinished && !!knowledgeBase?.id)
                }
                initialShouldFetchCustomization={false}
                slots={
                  currentStep === StepEnum.TABS
                    ? {
                        quick_links: [
                          ...slots?.filter(
                            (slot) => slot.type === 'quick_link'
                          ),
                        ],
                        meta_links: [
                          ...slots?.filter((slot) => slot.type === 'meta_link'),
                        ],
                        link_groups: [
                          ...slots?.filter(
                            (slot) => slot.type === 'link_group'
                          ),
                        ],
                        videos: [
                          ...slots?.filter((slot) => slot.type === 'video'),
                        ],
                      }
                    : fakerSlots
                }
              />
              {trainingFinished ? (
                <DAlert state="positive">
                  Your AI Chatbot is now fully interactive. Feel free to test
                  its quality before deciding to move forward or retrain it.
                </DAlert>
              ) : (
                <div className="text-grey-50 text-sm tracking-tight bg-grey-5 text-black p-size1 px-size4 rounded-size1">
                  ⚠️ This AI Chatbot preview is not interactive. It shows only
                  what the final version will look like.
                </div>
              )}
            </div>
          )
        }
        showSidebar={!isTraining}
      >
        {(handleRightSidebar) => (
          <LayoutWithButtons
            removePadding={currentStep === StepEnum.CHATBOT_NAME}
            showModalPlans={false}
          >
            <div className={`absolute top-0 left-0 w-full h-full ${!showInlinePlans ? 'hidden' : ''}`}>
              <InlineGlobalModals />
            </div>
            <Creation
              setProgressBar={setProgressBar}
              knowledgeBase={knowledgeBase}
              setKnowledgeBase={setKnowledgeBase}
              isTraining={isTraining}
              setIsTraining={setIsTraining}
              trainingFinished={trainingFinished}
              setTrainingFinished={setTrainingFinished}
              errors={errors}
              saveChanges={saveChanges}
              setLastStep={setLastStep}
              personalities={personalities}
              chatbotData={chatbotData}
              setChatbotData={setChatbotData}
              tempCustomizationData={tempCustomizationData}
              setTempCustomizationData={setTempCustomizationData}
              setPendingUpload={setPendingUpload}
              onNextClick={handleNext}
              onBackClick={handleBack}
              setUnsavedChanges={setUnsavedChanges}
              setSaveChanges={setSaveChanges}
              onTrainClick={async () => {
                await trackKlaviyoEvent('train started', {
                  chatbot_name: chatbotData.chatbotName,
                });
                setSaveChanges(true);
              }}
            />
            {/* <Customization
                tempCustomizationData={tempCustomizationData}
                setTempCustomizationData={setTempCustomizationData}
                customizationSaved={customizationSaved}
                setCustomizationSaved={setCustomizationSaved}
                chatbotId={knowledgeBase.id}
                personalities={personalities}
                slots={slots}
                setSlots={setSlots}
                enableHomeTab={enableHomeTab}
                setEnableHomeTab={setEnableHomeTab}
                enableAvatarTab={enableAvatarTab}
                setEnableAvatarTab={setEnableAvatarTab}
              /> */}
            <ReactRouterPrompt when={unsavedChanges && !trainingFinished}>
              {({ isActive, onConfirm, onCancel }) => (
                <DConfirmationModal
                  open={isActive}
                  onClose={onCancel}
                  onConfirm={onConfirm}
                  title="Are you sure you want to stop creating the chatbot?"
                  description="You have unsaved changes. If you leave, you will lose your changes."
                  confirmText="Leave"
                  cancelText="Cancel"
                  variantConfirm="danger"
                />
              )}
            </ReactRouterPrompt>
          </LayoutWithButtons>
        )}
      </LayoutRightSidebar>
    </div>
  );
};

export default CreateChatbot;
