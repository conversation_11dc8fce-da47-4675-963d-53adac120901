import { useEffect, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import Bubble from '@/components/Bubble';
import Tabs from '@/components/Chatbot/Tabs';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import LayoutWithButtons from '@/layouts/LayoutWithButtons';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import useLayoutStore from '@/stores/layout/layoutStore';
import DButton from '@/components/Global/DButton';
import useDanteApi from '@/hooks/useDanteApi';
import * as customizationService from '@/services/customization.service';
import * as tabsService from '@/services/tabs.service';
import { COMMON_CLASSNAMES, fakerSlots } from '@/constants';
import useToast from '@/hooks/useToast';
import DAlert from '@/components/Global/DAlert';
import DLoading from '@/components/DLoading';
import ReactRouterPrompt from 'react-router-prompt';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import compareObjects from '@/helpers/compareObjects';
import StyleTag from '@/components/StyleTag';
import { useUserStore } from '@/stores/user/userStore';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';

const ChatbotTabs = () => {
  const { addSuccessToast } = useToast();
  const params = useParams();
  const navigate = useNavigate();
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const user = useUserStore((state) => state.user);
  const teamSelected = useTeamManagementStore((state) => state.selectedTeam?.id);
  const setIsInPreviewBubblePage = useLayoutStore(
    (state) => state.setIsInPreviewBubblePage
  );

  // Get customization data (for the bubble preview)
  const { data, isLoading } = useDanteApi(
    customizationService.getChatbotCustomizationById,
    [],
    {},
    { kb_id: params.id }
  );

  const {
    data: tabsData,
    loading: isLoadingTabs,
    refetch: refetchTabs,
  } = useDanteApi(tabsService.getAllSlots, [], {}, params.id);

  // Track if all data is fully initialized
  const [isDataInitialized, setIsDataInitialized] = useState(false);

  // For editing we use a flat array.
  const [slots, setSlots] = useState([]);
  const [enableHomeTab, setEnableHomeTab] = useState(false);
  const [enableAvatarTab, setEnableAvatarTab] = useState(false);
  const [enablePreviousConversation, setEnablePreviousConversation] = useState(false);
  const [originalTabs, setOriginalTabs] = useState(null);
  
  // Main tabs state
  const [mainTabs, setMainTabs] = useState({
    home: {
      id: 'home',
      order: 1,
      label: 'Home'
    },
    chat: {
      id: 'chat',
      order: 2,
      label: 'Chat'
    }
  });
  
  // Original state for comparison
  const [originalState, setOriginalState] = useState({
    home_tab_enabled: false,
    avatar_enabled: false,
    previous_conversation_enabled: false,
    main_tabs: {
      home: {
        id: 'home',
        order: 1,
        label: 'Home'
      },
      chat: {
        id: 'chat',
        order: 2,
        label: 'Chat'
      }
    }
  });

  const [isSaveLoading, setIsSaveLoading] = useState(false);
  const [pageFullyLoaded, setPageFullyLoaded] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [isPreviewingHomeTab, setIsPreviewingHomeTab] = useState(false);

  // Initialize data from API response
  useEffect(() => {
    if (data) {
      // For free users without team, always disable home tab regardless of API response
      const shouldEnableHomeTab = (user.tier_type === 'free' && !teamSelected) ? false : data.home_tab_enabled;

      setEnableHomeTab(shouldEnableHomeTab);
      setEnablePreviousConversation(data.previous_conversation_enabled);
      setEnableAvatarTab(data.avatar_enabled);

      if (data.main_tabs) {
        setMainTabs(data.main_tabs);
      }

      // Set original state for change detection - use the processed values, not raw API values
      setOriginalState({
        home_tab_enabled: shouldEnableHomeTab,
        avatar_enabled: data.avatar_enabled,
        previous_conversation_enabled: data.previous_conversation_enabled,
        main_tabs: data.main_tabs || mainTabs
      });
    }
  }, [data, user.tier_type, teamSelected]);

  useEffect(() => {
    if (tabsData) {
      const originalTabsData = {
        quick_links: tabsData.quick_links || [],
        meta_links: tabsData.meta_links || [],
        link_groups: tabsData.link_groups || [],
        videos: tabsData.videos || [],
        sliders: tabsData.sliders || [],
      };
      setOriginalTabs(originalTabsData);

      setSlots([].concat(
        tabsData.quick_links || [],
        tabsData.meta_links || [],
        tabsData.link_groups || [],
        tabsData.videos || [],
        tabsData.sliders || []
      ));
    }
  }, [tabsData]);

  // Set data initialization state when all data is loaded and processed
  useEffect(() => {
    if (data && tabsData && originalTabs) {
      setIsDataInitialized(true);
    } else {
      setIsDataInitialized(false);
      setPageFullyLoaded(false);
    }
  }, [data, tabsData, originalTabs]);

  // Set page fully loaded after data is initialized
  useEffect(() => {
    if (isDataInitialized) {
      setPageFullyLoaded(true);
    }
  }, [isDataInitialized]);

  const combinedSlots = useMemo(() => ({
    quick_links: slots.filter((slot) => slot.type === 'quick_link'),
    meta_links: slots.filter((slot) => slot.type === 'meta_link'),
    link_groups: slots.filter((slot) => slot.type === 'link_group'),
    videos: slots.filter((slot) => slot.type === 'video'),
    sliders: slots.filter((slot) => slot.type === 'slider'),
  }), [slots]);

  // Check for unsaved changes
  useEffect(() => {
    if (!slots || !originalTabs || !originalState || !pageFullyLoaded) {
      return;
    }

    const slotsChanged = !compareObjects(originalTabs, combinedSlots);
    const currentState = {
      home_tab_enabled: enableHomeTab,
      avatar_enabled: enableAvatarTab,
      previous_conversation_enabled: enablePreviousConversation,
      main_tabs: mainTabs
    };

    const stateChanged = !compareObjects(originalState, currentState);
    setUnsavedChanges(slotsChanged || stateChanged);
  }, [
    slots,
    originalTabs,
    enableHomeTab,
    enableAvatarTab,
    enablePreviousConversation,
    mainTabs,
    originalState,
    pageFullyLoaded,
  ]);

  const handleSubmit = async () => {
    try {
      const quick_links = slots.filter((slot) => slot.type === 'quick_link');
      const meta_links = slots.filter((slot) => slot.type === 'meta_link');
      const link_groups = slots.filter((slot) => slot.type === 'link_group');
      const videos = slots.filter((slot) => slot.type === 'video');
      const sliders = slots.filter((slot) => slot.type === 'slider');

      const payload = {
        home_tab_enabled: enableHomeTab,
        home_tab_order: mainTabs.home.order,
        chat_order: mainTabs.chat.order,
        home_tab_label: mainTabs.home.label,
        chat_label: mainTabs.chat.label,
        previous_conversation_enabled: enablePreviousConversation,
        avatar_enabled: enableAvatarTab,
        slots: {
          quick_links,
          meta_links,
          link_groups,
          videos,
          sliders,
        },
      };

      setIsSaveLoading(true);

      const responseTabs = await tabsService.saveTabs(params.id, payload);
      if (responseTabs.status === 200) {
        addSuccessToast({
          message: 'Tabs updated successfully',
        });
        
        // Update original states to match current state
        setOriginalTabs({
          quick_links: responseTabs.data.slots.quick_links,
          meta_links: responseTabs.data.slots.meta_links,
          link_groups: responseTabs.data.slots.link_groups,
          videos: responseTabs.data.slots.videos,
          sliders: responseTabs.data.slots.sliders,
        });

        setOriginalState({
          home_tab_enabled: responseTabs.data.home_tab_enabled,
          avatar_enabled: responseTabs.data.avatar_enabled,
          previous_conversation_enabled: responseTabs.data.previous_conversation_enabled,
          main_tabs: responseTabs.data.main_tabs
        });

        // Update current states
        setSlots([].concat(
          responseTabs.data.slots.quick_links,
          responseTabs.data.slots.meta_links,
          responseTabs.data.slots.link_groups,
          responseTabs.data.slots.videos,
          responseTabs.data.slots.sliders
        ));
        
        setMainTabs(responseTabs.data.main_tabs);
        await refetchTabs();
      }
    } catch (e) {
      console.log(e);
    } finally {
      setIsSaveLoading(false);
    }
  };

  useEffect(() => {
    setSidebarOpen(false);
    if (window.innerWidth < 768) {
      setLayoutTitle('Chatbot');
    } else {
      setLayoutTitle('');
    }
  }, [window.innerWidth, setSidebarOpen, setLayoutTitle]);

  useEffect(() => {
    setIsInPreviewBubblePage(true);
  }, [setIsInPreviewBubblePage]);

  if (isLoading || isLoadingTabs || !data || !tabsData || !isDataInitialized) {
    return <DLoading show={true} />;
  }

  return (
    <LayoutRightSidebar
      RightSidebar={() => (
        <div className={COMMON_CLASSNAMES.previewBubble}>
          <StyleTag tag=".bubble" tempCustomizationData={data} />
          <Bubble
            config={{
              ...data,
              home_tab_enabled: enableHomeTab,
              main_tabs: mainTabs,
              public: true,
              previous_conversation_enabled: enablePreviousConversation,
              remove_watermark: true,
              initialActiveTab: enableHomeTab ? 'home' : undefined,
            }}
            isPreviewMode={true}
            slots={user.tier_type === 'free' ? fakerSlots : combinedSlots}
          />
        </div>
      )}
    >
      {() => (
        <LayoutWithButtons
          footer={
            <div className="flex items-center justify-between">
              <DButton
                variant="grey"
                className="!h-12 w-full md:w-auto md:!min-w-52"
                onClick={() => navigate(`/chatbot/${params.id}`)}
              >
                Cancel
              </DButton>
              <DButton
                variant="dark"
                className="!h-12 w-full md:w-auto md:!min-w-52"
                onClick={handleSubmit}
                loading={isSaveLoading}
                disabled={!unsavedChanges || !pageFullyLoaded}
              >
                Save
              </DButton>
            </div>
          }
        >
          <Tabs
            slots={slots}
            setSlots={setSlots}
            enableHomeTab={enableHomeTab}
            setEnableHomeTab={setEnableHomeTab}
            enableAvatarTab={enableAvatarTab}
            setEnableAvatarTab={setEnableAvatarTab}
            enablePreviousConversation={enablePreviousConversation}
            setEnablePreviousConversation={setEnablePreviousConversation}
            mainTabs={mainTabs}
            setMainTabs={setMainTabs}
            isPreviewingHomeTab={isPreviewingHomeTab}
            setIsPreviewingHomeTab={setIsPreviewingHomeTab}
          />
          <ReactRouterPrompt when={unsavedChanges}>
            {({ isActive, onConfirm, onCancel }) => (
              <DConfirmationModal
                open={isActive}
                onClose={onCancel}
                onConfirm={onConfirm}
                title="Are you sure you want to leave this page?"
                description="You have unsaved changes. If you leave, you will lose your changes."
                confirmText="Leave"
                cancelText="Cancel"
                variantConfirm="danger"
              />
            )}
          </ReactRouterPrompt>
        </LayoutWithButtons>
      )}
    </LayoutRightSidebar>
  );
};

export default ChatbotTabs;
