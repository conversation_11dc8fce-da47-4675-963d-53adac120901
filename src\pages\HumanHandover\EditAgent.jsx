import DButton from '@/components/Global/DButton';
import DInput from '@/components/Global/DInput/DInput';
import DModal from '@/components/Global/DModal';
import DMultiselect from '@/components/Global/DMultiselect';
import DSelect from '@/components/Global/DSelect';
import { AGENT_MEMBER_PERMISSIONS } from '@/constants';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import { ListboxOption } from '@headlessui/react';
import { useEffect, useState } from 'react';

import validateEmail from '@/helpers/validateEmail';
import DToastContainer from '@/components/DToast/DToastContainer';

const EditMember = ({ open, onClose, onSave, editAgent, setMember }) => {
  const chatbots = useChatbotStore((state) => state.chatbots);
  const [selectedChatbots, setSelectedChatbots] = useState(
    editAgent?.allowed_chatbots.map((kb) => {
      return {
        label: kb.knowledge_base_name,
        value: kb.id,
      };
    })
  );
  const [errors, setErrors] = useState([]);

  const handleBeforeSubmit = () => {
    const newErrors = [];

    if (editAgent?.name === '') {
      newErrors.push({ field: 'name', message: 'Name is required' });
    }
    if (editAgent?.user_response?.email === '') {
      newErrors.push({ field: 'email', message: 'Email is required' });
    } else if (!validateEmail(editAgent?.user_response?.email)) {
      newErrors.push({ field: 'email', message: 'Invalid email' });
    }

    setErrors(newErrors);
    if (newErrors.length === 0) {
      onSave();
    }
  };

  useEffect(() => {
    if (editAgent?.allowed_chatbots.length === chatbots?.length) {
      setSelectedChatbots(
        chatbots?.map((kb) => ({ label: kb.name, value: kb.kb_id }))
      );
    } else {
      setSelectedChatbots(
        editAgent?.allowed_chatbots.map((kb) => {
          return {
            label: kb.knowledge_base_name || kb.label,
            value: kb.id || kb.value,
          };
        })
      );
    }
  }, [editAgent]);

  const handleClose = () => {
    setErrors([]);
    onClose();
  };

  return (
    <DModal
      title="Edit Human Handover"
      isOpen={open}
      onClose={handleClose}
      footer={
        <div className="flex items-center gap-size1 w-full">
          <DButton onClick={handleClose} variant="grey" fullWidth size="md">
            Cancel
          </DButton>
          <DButton
            onClick={handleBeforeSubmit}
            variant="dark"
            fullWidth
            size="md"
          >
            Confirm
          </DButton>
        </div>
      }
    >
      <DToastContainer showFixed />

      <div className="flex flex-col gap-size5">
        <div className="flex flex-col gap-size0">
          <p className="text-base font-medium tracking-tight">Name</p>
          <DInput
            placeholder="Enter name"
            value={editAgent?.name}
            error={errors.find((error) => error.field === 'name')?.message}
            onChange={(e) => setMember({ ...editAgent, name: e.target.value })}
          />
        </div>
        <div className="flex flex-col gap-size0">
          <p className="text-base font-medium tracking-tight">Email</p>
          <DInput
            placeholder="Enter email"
            type="email"
            value={editAgent?.user_response?.email}
            error={errors.find((error) => error.field === 'email')?.message}
            onChange={(e) =>
              setMember({
                ...editAgent,
                user_response: {
                  ...editAgent.user_response,
                  email: e.target.value,
                },
              })
            }
          />
        </div>
        <div className="flex flex-col gap-size0">
          <p className="text-base font-medium tracking-tight">Agent Role</p>
          <DInput
            placeholder="Enter agent role"
            type="text"
            value={editAgent?.agent_title || ''}
            error={errors.find((error) => error.field === 'agent_title')?.message}
            onChange={(e) =>
              setMember({
                ...editAgent,
                agent_title: e.target.value,
              })
            }
          />
        </div>
        {/* <div className="flex flex-col gap-size1">
          <p className="text-base font-medium tracking-tight">Permissions</p>
          <DSelect
            value={editAgent?.permissions}
            onChange={(value) => {
              setMember({ ...editAgent, permissions: value });
            }}
            selectedChild={
              <p>
                {
                  AGENT_MEMBER_PERMISSIONS.find(
                    (permission) => permission.value === editAgent?.permissions
                  )?.name
                }
              </p>
            }
          >
            {AGENT_MEMBER_PERMISSIONS.map((permission) => (
              <ListboxOption
                key={permission.value}
                value={permission.value}
                className="cursor-pointer border-t border-grey-5 flex flex-col gap-size0 px-size0 py-size1 hover:bg-grey-5"
              >
                <p className="text-sm font-medium">{permission.name}</p>
                <p className="text-xs font-light">{permission.description}</p>
              </ListboxOption>
            ))}
          </DSelect>
        </div> */}
        <div className="flex flex-col gap-size1">
          <p className="text-base font-medium tracking-tight">Chatbot</p>
          <DMultiselect
            options={chatbots?.map((kb) => ({
              label: kb.name,
              value: kb.kb_id,
            }))}
            selected={selectedChatbots}
            skipSelectAllCheck={true}
            setSelected={(value) => {
              setSelectedChatbots(value);
              setMember({
                ...editAgent,
                all_knowledge_bases: value.length === chatbots?.length,
                chatbots: value.map((kb) => kb.value),
                allowed_chatbots: value,
              });
            }}
          />
        </div>
      </div>
    </DModal>
  );
};

export default EditMember;
