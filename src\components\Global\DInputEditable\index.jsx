import { useEffect, useRef, useState } from 'react';

import CheckIcon from '../Icons/CheckIcon';
import CheckmarkIcon from '../Icons/CheckmarkIcon';
import DeleteIcon from '../Icons/DeleteIcon';
import DragIcon from '../Icons/DragIcon';
import EditIcon from '../Icons/EditIcon';
import DSwitch from '../DSwitch';
const DInputEditable = ({
  value,
  onChange,
  placeholder,
  type = 'text',
  name,
  id = '',
  className,
  disabled = false,
  is_draggable = false,
  is_editable = false,
  is_deletable = false,
  has_switch = false,
  onEdit,
  onDelete,
  onDragListeners,
  size = 'sm',
  required = false,
  outlined = false,
  disclaimer,
  switchValue,
  onSwitchChange,
  paragraphClassName = '',
  renderExtraContent,
}) => {
  const [is_editing, setIsEditing] = useState(false);
  const inputRef = useRef(null);
  let textSize = 'text-md';

  if (size === 'xl') {
    textSize = 'text-xl';
  }

  useEffect(() => {
    if (is_editing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [is_editing]);

  return (
    <div
      className={[
        className,
        'flex gap-size1 items-center justify-between w-full h-full',
      ].join(' ')}
    >
      <div className="flex items-center gap-2 w-full h-full overflow-hidden">
        {is_draggable && (
          <div
            {...onDragListeners}
            style={{ touchAction: 'none' }}
            className="cursor-grab active:cursor-grabbing"
          >
            <DragIcon />
          </div>
        )}
        {is_editing ? (
          <input
            type={type}
            name={name}
            id={id}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            disabled={disabled}
            className={`dbutton bg-transparent ${textSize} text-min-safe-input tracking-tight min-w-[168px] w-full max-w-full text-ellipsis overflow-hidden border border-grey-20 rounded-size1 p-size1 focus:outline-none focus:border-grey-10`}
            ref={inputRef}
            required={required}
            data-testid={`d-input-editable-${name ?? id ?? ''}`}
            onBlur={() => {
              setIsEditing(false);
              onEdit && onEdit(value);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                setIsEditing(false);
                onEdit && onEdit(value);
              }
            }}
          />
        ) : (
          <p
            className={`block ${textSize} min-w-[168px] w-full max-w-[200px] sm:max-w-[250px] md:max-w-[300px] lg:max-w-[400px] xl:max-w-[500px] overflow-hidden text-ellipsis whitespace-nowrap tracking-tight h-full p-size1 ${
              outlined ? 'border border-grey-5 rounded-size1' : ''
            } ${paragraphClassName}`}
          >
            {value ? (
              <span>{value}</span>
            ) : (
              <span className="text-grey-20">{placeholder}</span>
            )}
            {required && <span className="text-red-500">*</span>}
            {disclaimer && <span className="text-grey-20"> {disclaimer}</span>}
          </p>
        )}
      </div>
      <div className="flex items-center gap-2">
        {has_switch && (
          <DSwitch checked={switchValue} onChange={onSwitchChange} />
        )}
        {is_editable && !is_editing && (
          <button
            className="dbutton border border-grey-5 rounded-size1 p-size1"
            onClick={(e) => {
              setIsEditing(true);
            }}
            data-testid={`d-input-editable-edit-button-${name ?? id ?? ''}`}
          >
            <EditIcon />
          </button>
        )}
        {is_editing && is_editable && (
          <button
            className="dbutton border border-grey-5 rounded-size1 p-size1"
            onClick={() => {
              setIsEditing(false);
              onEdit && onEdit(value);
            }}
          >
            <CheckmarkIcon />
          </button>
        )}
        {is_deletable && (
          <button
            className="dbutton border border-grey-5 rounded-size1 p-size1"
            onClick={onDelete}
          >
            <DeleteIcon />
          </button>
        )}
        {renderExtraContent && renderExtraContent()}
      </div>
    </div>
  );
};

export default DInputEditable;
