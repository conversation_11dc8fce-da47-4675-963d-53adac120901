import React from 'react';
import PropTypes from 'prop-types';

const RealTimeVoiceIcon = ({ className = '', color = 'currentColor', ...props }) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width="24" 
      height="24" 
      viewBox="0 0 24 24" 
      fill="none"
      className={className}
      {...props}
    >
      <path 
        d="M5.625 9V15C5.625 15.2984 5.50647 15.5845 5.2955 15.7955C5.08452 16.0065 4.79837 16.125 4.5 16.125C4.20163 16.125 3.91548 16.0065 3.7045 15.7955C3.49353 15.5845 3.375 15.2984 3.375 15V9C3.375 8.70163 3.49353 8.41548 3.7045 8.2045C3.91548 7.99353 4.20163 7.875 4.5 7.875C4.79837 7.875 5.08452 7.99353 5.2955 8.2045C5.50647 8.41548 5.625 8.70163 5.625 9ZM8.25 1.875C7.95163 1.875 7.66548 1.99353 7.4545 2.2045C7.24353 2.41548 7.125 2.70163 7.125 3V21C7.125 21.2984 7.24353 21.5845 7.4545 21.7955C7.66548 22.0065 7.95163 22.125 8.25 22.125C8.54837 22.125 8.83452 22.0065 9.0455 21.7955C9.25647 21.5845 9.375 21.2984 9.375 21V3C9.375 2.70163 9.25647 2.41548 9.0455 2.2045C8.83452 1.99353 8.54837 1.875 8.25 1.875ZM12 4.875C11.7016 4.875 11.4155 4.99353 11.2045 5.2045C10.9935 5.41548 10.875 5.70163 10.875 6V18C10.875 18.2984 10.9935 18.5845 11.2045 18.7955C11.4155 19.0065 11.7016 19.125 12 19.125C12.2984 19.125 12.5845 19.0065 12.7955 18.7955C13.0065 18.5845 13.125 18.2984 13.125 18V6C13.125 5.70163 13.0065 5.41548 12.7955 5.2045C12.5845 4.99353 12.2984 4.875 12 4.875ZM15.75 7.875C15.4516 7.875 15.1655 7.99353 14.9545 8.2045C14.7435 8.41548 14.625 8.70163 14.625 9V15C14.625 15.2984 14.7435 15.5845 14.9545 15.7955C15.1655 16.0065 15.4516 16.125 15.75 16.125C16.0484 16.125 16.3345 16.0065 16.5455 15.7955C16.7565 15.5845 16.875 15.2984 16.875 15V9C16.875 8.70163 16.7565 8.41548 16.5455 8.2045C16.3345 7.99353 16.0484 7.875 15.75 7.875ZM19.5 6.375C19.2016 6.375 18.9155 6.49353 18.7045 6.7045C18.4935 6.91548 18.375 7.20163 18.375 7.5V16.5C18.375 16.7984 18.4935 17.0845 18.7045 17.2955C18.9155 17.5065 19.2016 17.625 19.5 17.625C19.7984 17.625 20.0845 17.5065 20.2955 17.2955C20.5065 17.0845 20.625 16.7984 20.625 16.5V7.5C20.625 7.20163 20.5065 6.91548 20.2955 6.7045C20.0845 6.49353 19.7984 6.375 19.5 6.375Z" 
        fill={color}
      />
    </svg>
  );
};

RealTimeVoiceIcon.propTypes = {
  className: PropTypes.string,
  color: PropTypes.string,
};

export default RealTimeVoiceIcon; 