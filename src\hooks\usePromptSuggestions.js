import { useState, useCallback, useEffect } from 'react';
import * as suggestionService from '@/services/suggestion.service';

/**
 * Custom hook to manage prompt suggestions with various display conditions
 * 
 * @param {Object} config - Customization configuration object
 * @param {string} conversationId - Current conversation ID
 * @param {boolean} isAnswerLoading - Whether an answer is currently loading
 * @param {boolean} isLeadGenMode - Whether lead generation mode is active
 * @param {boolean} interactingWithLiveAgent - Whether user is interacting with a live agent
 * @param {boolean} isLiveAgentResolved - Whether the live agent conversation is resolved
 * @param {boolean} pollResponse - Whether polling for responses is active
 * @param {boolean} hasStartedConversation - Whether a conversation has started
 * @param {boolean} showConsent - Whether consent UI is shown
 * @param {Function} scrollMessage - Function to scroll to latest message
 * @returns {Object} Prompt suggestion state and handlers
 */
export default function usePromptSuggestions({
  config,
  conversationId,
  isAnswerLoading,
  isLeadGenMode,
  interactingWithLiveAgent,
  isLiveAgentResolved,
  pollResponse,
  hasStartedConversation,
  showConsent,
  scrollMessage,
  isInApp = false,
}) {
  // Simplified state management
  const [suggestions, setSuggestions] = useState(config?.prompt_suggestions || []);
  const [shouldShow, setShouldShow] = useState(false);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  
  // Extract configuration values for easier access
  const isEnabled = config?.suggested_prompts_enabled;
  const showOnce = config?.show_prompt_messages;
  const alwaysSamePrompts = config?.show_always_same_prompts;
  
  /**
   * Fetch new prompt suggestions from the API
   */
  const fetchSuggestions = useCallback(async (currentConversationId) => {
    if (!currentConversationId || !isEnabled) {
      return;
    }
    
    try {
      setIsLoadingSuggestions(true);
      setShouldShow(false);
      
      const response = await suggestionService.postSuggestions({
        kb_id: config?.kb_id,
        conversation_id: currentConversationId,
      });
      
      if (response.status === 200) {
        const newSuggestions = response.data.result;
                
        // If API returns empty suggestions, fall back to static prompts
        if (newSuggestions && newSuggestions.length > 0) {
          setSuggestions(newSuggestions);
        } else {
          // Fall back to static prompts from config
          setSuggestions(config?.prompt_suggestions || []);
        }
        
        setIsLoadingSuggestions(false);
        updateVisibility();
        
        // Single scroll attempt is sufficient
        if (scrollMessage) {
          setTimeout(() => scrollMessage(), 300);
        }
      } else {
        setSuggestions(config?.prompt_suggestions || []);
        setIsLoadingSuggestions(false);
        updateVisibility();
        
        if (scrollMessage) {
          setTimeout(() => scrollMessage(), 300);
        }
      }
    } catch (error) {
      setSuggestions(config?.prompt_suggestions || []);
      setIsLoadingSuggestions(false);
      updateVisibility();
      
      if (scrollMessage) {
        setTimeout(() => scrollMessage(), 300);
      }
    }
  }, [config?.kb_id, config?.prompt_suggestions, isEnabled, scrollMessage]);
  
  /**
   * Update prompt visibility based on all conditions
   */
  const updateVisibility = useCallback(() => {
    // Don't show if feature is disabled
    if (!isEnabled) {
      setShouldShow(false);
      return;
    }
    
    // Don't show during specific states
    // For lead gen: only hide prompts after user has started interacting, but NOT in app
    const shouldHideForLeadGen = isLeadGenMode && hasStartedConversation && !isInApp;

    if (isAnswerLoading || shouldHideForLeadGen || interactingWithLiveAgent || isLiveAgentResolved || pollResponse || showConsent || isLoadingSuggestions) {
      setShouldShow(false);
      return;
    }
    
    // Handle "show once" logic
    if (showOnce) {
      const hasShownBefore = localStorage.getItem('show_prompt_messages');
      const shouldShow = !hasShownBefore;
      setShouldShow(shouldShow);
    } else {
      setShouldShow(true);
      if (localStorage.getItem('show_prompt_messages')) {
        localStorage.removeItem('show_prompt_messages');
      }
    }
  }, [
    isEnabled,
    showOnce,
    isAnswerLoading,
    isLeadGenMode,
    hasStartedConversation,
    interactingWithLiveAgent,
    isLiveAgentResolved,
    pollResponse,
    showConsent,
    isLoadingSuggestions,
    suggestions,
    isInApp
  ]);
  
  /**
   * Mark prompts as shown (for "show once" functionality)
   */
  const markAsShown = useCallback(() => {
    if (showOnce) {
      localStorage.setItem('show_prompt_messages', 'true');
    }
    // Hide prompts immediately when marked as shown
    setShouldShow(false);
  }, [showOnce]);
  
  /**
   * Reset prompt state (for new conversations)
   */
  const resetPrompts = useCallback(() => {
    setSuggestions(config?.prompt_suggestions || []);
    setIsLoadingSuggestions(false);
    
    // If "show once" is disabled, clear localStorage
    if (!showOnce && localStorage.getItem('show_prompt_messages')) {
      localStorage.removeItem('show_prompt_messages');
    }
    
    updateVisibility();
  }, [config?.prompt_suggestions, showOnce, updateVisibility]);
  
  /**
   * Handle response completion - either fetch new prompts or use static ones
   */
  const onResponseComplete = useCallback((currentConversationId) => {
    
    if (!isEnabled) {
      return;
    }
    
    if (!alwaysSamePrompts) {
      fetchSuggestions(currentConversationId);
    } else {
      setSuggestions(config?.prompt_suggestions || []);
      updateVisibility();
      if (scrollMessage) {
        setTimeout(() => scrollMessage(), 300);
      }
    }
  }, [
    isEnabled, 
    alwaysSamePrompts, 
    fetchSuggestions, 
    config?.prompt_suggestions, 
    updateVisibility,
    scrollMessage
  ]);
  
  // Update visibility whenever dependencies change
  useEffect(() => {
    updateVisibility();
  }, [updateVisibility]);
  
  // Initial setup when config changes
  useEffect(() => {
    if (!hasStartedConversation) {
      setSuggestions(config?.prompt_suggestions || []);
      setIsLoadingSuggestions(false);
      updateVisibility();
    }
  }, [config?.prompt_suggestions, hasStartedConversation, updateVisibility]);
  
  // Scroll when prompts are shown
  useEffect(() => {
    if (shouldShow && scrollMessage) {
      setTimeout(() => scrollMessage(), 300);
    }
  }, [shouldShow, scrollMessage]);
  
  // Debug final state
  const returnObject = {
    suggestions,
    shouldShow,
    isLoadingSuggestions,
    markAsShown,
    resetPrompts,
    onResponseComplete
  };  
  return returnObject;
} 