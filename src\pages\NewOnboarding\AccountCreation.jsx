// pages/TestPage.jsx
import React, { useState, useRef, useEffect } from "react";
import OnboardingLayout from "@/layouts/OnboardingLayout";
import DFullLogo from "@/components/Global/DLogo/DFullLogo";
import "./index.css";
import GoogleIcon from "@/components/Global/Icons/GoogleIcon";
import { useLocation, useNavigate } from "react-router-dom";
import { claimOnboarding, verifyOnboardingWithGoogle } from '@/services/onboarding.service';
import { useGoogleLogin } from '@react-oauth/google';
import validateEmail from '@/helpers/validateEmail';
import RocketIcon from "@/components/Global/Icons/RocketIcon";
import TrophyIcon from "@/components/Global/Icons/TrophyIcon";
import DCheckbox from "@/components/Global/DCheckbox";
import { useUserStore } from "@/stores/user/userStore";
import { getUserInfo } from "@/services/user.service";

const CheckIcon = (props) => (
  <svg width="15" height="12" viewBox="0 0 17 14" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.2321 1.14502C16.3444 1.24121 16.4365 1.35858 16.5034 1.49041C16.5702 1.62224 16.6105 1.76596 16.6217 1.91334C16.633 2.06072 16.6152 2.20889 16.5691 2.34936C16.5231 2.48983 16.4499 2.61986 16.3536 2.73202L7.35362 13.232C7.25272 13.3497 7.12857 13.4453 6.98896 13.5128C6.84936 13.5803 6.69732 13.6181 6.54239 13.6241C6.38745 13.63 6.23297 13.6038 6.08863 13.5472C5.94429 13.4905 5.81321 13.4047 5.70362 13.295L0.453621 8.04502C0.248791 7.83274 0.135529 7.54851 0.138231 7.25354C0.140932 6.95857 0.25938 6.67646 0.468064 6.46797C0.676747 6.25949 0.958968 6.1413 1.25394 6.13888C1.54892 6.13646 1.83304 6.24999 2.04512 6.45502L6.43712 10.8455L14.6466 1.26802C14.8408 1.0418 15.1169 0.901928 15.4142 0.879146C15.7115 0.856364 16.0057 0.951031 16.2321 1.14502Z" fill="currentColor"/>
</svg>
)

export default function AccountCreation() {
  const navigate = useNavigate();

  const location = useLocation();
  // Get user_id, kb_id, chatbot_token, url from navigation state
  const { user_id, kb_id, chatbot_token, email, role, url, processingTime } = location.state || {};
  // Get user_id, kb_id, chatbot_token, processingTime from navigation state
  const [emailState, setEmailState] = useState(email);
  const [loading, setLoading] = useState(false);
  const [claimError, setClaimError] = useState("");
  const [emailError, setEmailError] = useState("");
  const [marketingConsent, setMarketingConsent] = useState(true);
  const saveAuthDetail = useUserStore((state) => state.saveAuthDetail);
  const setUser = useUserStore((state) => state.setUser);

  const validateForm = () => {
    let isValid = true;

    // Email validation
    if (!emailState || emailState.trim() === '') {
      setEmailError('Email is required');
      isValid = false;
    } else if (!validateEmail(emailState)) {
      setEmailError('Please enter a valid email address');
      isValid = false;
    } else if (emailState.includes('+')) {
      setEmailError('Emails with \'+\' are not allowed');
      isValid = false;
    } else {
      setEmailError('');
    }

    return isValid;
  };

  // Calculate display time: use processingTime if available, otherwise default to 60 seconds
  const getDisplayTime = () => {
    if (processingTime === undefined || processingTime === null) {
      return 60;
    }
    if (processingTime < 0 || processingTime > 60) {
      return 60;
    }
    return processingTime;
  };

  const displayTime = getDisplayTime();


  const handleEmailChange = (e) => {
    const newEmail = e.target.value;
    setEmailState(newEmail);

    // Clear errors when input changes
    if (claimError) {
      setClaimError('');
    }
    if (emailError) {
      setEmailError('');
    }
  };

  const handleAccountCreation = async () => {
    if (!validateForm()) return;

    setLoading(true);
    setClaimError("");
    setEmailError("");
    try {
      const response = await claimOnboarding(emailState, user_id, marketingConsent);
      if(response.status === 200){
        navigate('/create-account/verify', {
          state: {
            user_id: user_id,
            email: emailState,
            kb_id: kb_id,
            chatbot_token: chatbot_token,
            role: role,
            url: url,
            processingTime: processingTime,
            marketingConsent: marketingConsent
          }
        })
      }
    } catch (error) {
      console.error('Account creation failed:', error);
      // Extract error message from backend response
      const errorMessage = error.response?.data?.detail ||
                          error.response?.data?.message ||
                          error.message ||
                          'Failed to create account. Please try again.';


      if (errorMessage.toLowerCase().includes('email')) {
        setEmailError(errorMessage);
        setClaimError('');
      } else {
        setClaimError(errorMessage);
        setEmailError('');
      }
    } finally {
      setLoading(false);
    }
  };

  // Google sign up logic
  const handleGoogleAccountCreation = useGoogleLogin({
    onSuccess: async (tokenResponse) => {
      setLoading(true);
      setClaimError('');
      try {
        const response = await verifyOnboardingWithGoogle({
          ...tokenResponse,
          user_id,
          url,
          role,
          marketing_consent: marketingConsent
        });
        if (response.status === 200) {
          const auth = { access_token: response.data.access_token };
          saveAuthDetail(auth);
          
          const userInfo = await getUserInfo(response.data.access_token);
          setUser(userInfo);
          if (kb_id) {
            navigate(`/chatbot/${kb_id}?first_time=true`);
          } else {
            navigate('/');
          }
        }
      } catch (err) {
        console.error(err);
        const errorMessage = err.response?.data?.detail || err.message || 'Google sign up failed. Please try again.';
        setClaimError(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    onError: (error) => {
      console.error('Login Failed:', error);
      setClaimError('Google sign up failed. Please try again.');
      setLoading(false);
    },
  });

  // Construct iframe src
  const iframeSrc = kb_id && chatbot_token
    ? `${import.meta.env.VITE_APP_BASE_URL}embed/?kb_id=${kb_id}&token=${chatbot_token}&modeltype=gpt-4-omnimodel-mini&tabs=false&header=false&toolbar=false&controls=false&onboarding=true`
    : "";

  return (
    <OnboardingLayout containerClassName="min-h-screen relative overflow-x-hidden  " navClassName="px-2">
       <div className="flex flex-col  md:flex-col lg:flex-row md:justify-start  lg:justify-center items-start lg:items-center min-h-[100vh] ">
        <main className="relative z-10 flex items-center lg:justify-center px-[20px] lg:pb-0 pt-[96px]  pb-20 w-full">
        <div className="flex flex-col lg:flex-row gap-[40px] lg:gap-[8rem]  lg:items-center lg:justify-center  md:max-w-[75rem] w-full  my-auto min-h-[400px] lg:h-[592px] ">
          <div className="flex flex-col gap-[48px] w-full lg:w-[498px]">
            <div className="flex flex-col gap-[32px]">
                <div className="flex flex-col gap-[16px]">
                    <p className="text-[28px] font-sora leading-[140%] font-bold tracking-[-0.56px]">Ready in {displayTime} seconds!</p>
                    <p className="text-base leading-[160%] font-inter">Great start with the first page! Once you sign up, you can add more web pages, documents, media and other files.</p>
                </div>

            </div>
            <div className="flex flex-col gap-[16px]">
                <div className="flex flex-col ">
                    <p className="text-base leading-[160%] font-inter text-grey-75 pb-[8px]">Email:</p>
                    <input
                      type="email"
                      className={`
                        flex
                        h-14
                        px-4
                        py-2.5
                        items-center
                        gap-2.5
                        self-stretch
                        rounded-lg
                        font-[Inter]
                        border
                        ${emailError ? 'border-[#DF0F32] focus:border-[#DF0F32]' : 'border-[#D1CFE2] focus:border-[#5C5A6E]'}
                        bg-white
                        focus:outline-none
                        placeholder:text-black
                        transition-colors
                        duration-200
                      `}
                      placeholder="Enter your work email"
                      value={emailState}
                      onChange={handleEmailChange}
                      onKeyDown={e => {
                        if (e.key === 'Enter') {
                          handleAccountCreation();
                        }
                      }}
                      disabled={loading}
                      noValidate
                    />
                    <div className="h-4 font-[Inter]">
                      {emailError && <p className="text-[#DF0F32] text-sm mt-1">{emailError}</p>}
                      {claimError && <p className="text-[#DF0F32] text-sm mt-1">{claimError}</p>}
                    </div>

                </div>
                <button
                    className="
                          flex
                          h-14
                          px-6
                          py-2.5
                          flex-col
                          justify-center
                          items-center
                          self-stretch
                          rounded-lg
                          border-2
                          border-[#2B13FB]
                          text-base
                          text-white
                          leading-[150%]
                          font-[Inter]
                          font-medium
                          transition-all
                          duration-200
                          bg-[#5642FF] hover:bg-[#3F29FF] shadow-custom-glow
                    "
                    onClick={handleAccountCreation}
                    disabled={loading}
                    >
                     {loading ? 'Claiming...' : 'Get My Agent →'}
                </button>
                <div className="flex items-center gap-4 py-[16px]">
                  <div className="flex-1 h-px bg-[#D1CFE2]"></div>
                  <p className="text-base leading-[150%] text-[#464554] whitespace-nowrap font-[Inter]">OR</p>
                  <div className="flex-1 h-px bg-[#D1CFE2]"></div>
                </div>
                <button
                    className="
                        flex
                        h-14
                        px-6
                        pr-6
                        py-2.5
                        justify-center
                        items-center
                        gap-2
                        self-stretch
                        rounded-lg
                        border-2
                        border-[#6351FF]
                        bg-transparent
                        hover:bg-[#EFEEF7]
                    "
                    onClick={handleGoogleAccountCreation}
                    >
                    <GoogleIcon className="w-6 h-6"/>
                    <p className="text-base leading-[160%] font-inter text-[#6351FF] font-medium">Sign Up with Google</p>
                </button>

                <div className="flex flex-col mt-[48px] w-full">
                  <DCheckbox
                    label='I agree to <a class="underline text-black" href="https://www.dante-ai.com/terms-of-service" target="_blank" rel="noopener noreferrer">Terms</a> and <a class="underline text-black" href="https://www.dante-ai.com/privacy-policy" target="_blank" rel="noopener noreferrer">Privacy Policy</a>'
                    checked={true}
                    disabled={true}
                    onChange={() => {}}
                    checkedBgColor="data-[checked]:bg-grey-50"
                    customIcon={<CheckIcon />}
                    checkboxSize="min-w-[24px] min-h-[24px] "
                    className="font-[Inter] items-start"
                    size="sm"
                  />
                  <DCheckbox
                    label="I want to receive discounts, exclusive updates and free AI tips"
                    checked={marketingConsent}
                    onChange={(value) => {
                      setMarketingConsent(value);
                    }}
                    checkedBgColor="data-[checked]:bg-[#6351FF]"
                    customIcon={<CheckIcon  />}
                    checkboxSize="min-w-[24px] min-h-[24px] "
                    className="font-[Inter] items-start"
                    size="sm"
                  />
                </div>
                 <div className="flex flex-col  gap-x-[48px] gap-y-[8px] pt-[20px]">
                      <div className="flex gap-[8px] items-center">
                          <RocketIcon/>
                     <p className="text-base leading-[160%] font-[Inter]"> Trusted by 100,000+ businesses worldwide</p>
                      </div>
                      <div className="flex gap-[8px] items-center">
                      <TrophyIcon/>
                        <p className="text-base leading-[160%] font-[Inter]"> 4.7/5 rated on Trustpilot</p>
                    </div>
                   
                </div>
            </div>

          </div>
          <div className="relative w-full h-[592px] lg:w-[574px] bg-white opacity-[0.9] rounded-size2 mt-[20px] lg:mt-0 elevated-shadow">
            {kb_id && chatbot_token && (
              <iframe
                src={iframeSrc}
                allow="clipboard-write; clipboard-read; *;microphone *"
                width="100%"
                height="100%"
                style={{
                  border: 'none',
                  borderRadius: 12,
                  // Hide header controls if URL parameter doesn't work
                  overflow: 'hidden'
                }}
                title="Dante AI Agent"
              />
            )}
             {/* CSS to hide header controls */}
             {/* <style jsx>{`
             iframe[title="Dante AI Agent"] {
               margin-top: -60px !important;
               clip-path: inset(60px 0 0 0);
              //  height: calc(100% + 60px) !important;
              //  clip-path: inset(60px 0 100px 0);
              }
            `}</style> */}

          </div>
        </div>
      </main>


     </div>
    </OnboardingLayout>
  );
}
