import React, { useState, useRef, useEffect } from "react";
import LoadingFill from "@/components/LoadingFill";
import OnboardingRole from "@/components/OnboardingRole";
import OnboardingLayout from "@/layouts/OnboardingLayout";
import DFullLogo from "@/components/Global/DLogo/DFullLogo";
import TypingIndicator from "@/components/Global/TypingIndicator";
import TypingText from "@/components/TypingText";
import { useLocation, useNavigate } from "react-router-dom";
import { startOnboarding } from "@/services/onboarding.service";
import "./index.css";



export default function OnboardingStarted() {
  const [selectedRole, setSelectedRole] = useState(null);
  const [isValidSelection, setIsValidSelection] = useState(false);
  const [allFactsDisplayed, setAllFactsDisplayed] = useState(false);
  const autoProgressTimeoutRef = useRef(null);

  // Get url and captchaToken from navigation state
  const location = useLocation();
  const navigate = useNavigate();
  const { url, captchaToken } = location.state || {};
  const [fill, setFill] = useState(0);
  const [response, setResponse] = useState(null);
  const [factsToShow, setFactsToShow] = useState([]); // indices of facts to show
  const [facts, setFacts] = useState([]); // the interesting facts
  const [showTypingIndicator, setShowTypingIndicator] = useState(true); // show typing indicator while processing
  const [currentlyTyping, setCurrentlyTyping] = useState(false); // true while typing a fact
  const [typedFacts, setTypedFacts] = useState({}); // track which facts are fully typed
  const [startTime, setStartTime] = useState(null);
  const [endTime, setEndTime] = useState(null); // track when 100% is reached

  // Handle role selection with auto-progression logic
  const handleRoleSelect = (role) => {
    setSelectedRole(role);

    // Clear any existing auto-progress timeout
    if (autoProgressTimeoutRef.current) {
      clearTimeout(autoProgressTimeoutRef.current);
    }

    // If it's a predefined role (not "other"), navigate immediately if loading is complete
    if (role !== 'other' && typeof role === 'string' && role.trim() &&
        ['founder', 'marketing', 'engineering', 'sales', 'customer_success'].includes(role)) {
      // For predefined roles, navigate immediately if loading is complete
      if (fill >= 100) {
        navigateToAccountCreation();
      }
      // If loading is not complete, it will auto-navigate when it reaches 100%
    } else if (typeof role === 'string' && role.trim() && role !== 'other') {
      
    }
  };

  // Handle validation changes from OnboardingRole component
  const handleValidationChange = (isValid) => {
    setIsValidSelection(isValid);
  };

  // Handle auto-progression after 3 seconds of no typing for "Other" role
  const handleAutoProgress = () => {
    // Only navigate if loading is complete, otherwise let useEffect handle it
    if (fill >= 100) {
      navigateToAccountCreation();
    }
  };

  // Track when 100% is reached for processing time calculation
  useEffect(() => {
    if (fill >= 100 && !endTime) {
      setEndTime(Date.now());
    }
  }, [fill, endTime]);

  // Auto-navigate when loading reaches 100% and a valid role is already selected
  useEffect(() => {
    if (fill >= 100 && selectedRole && isValidSelection) {
      if (selectedRole !== 'other' &&
          ['founder', 'marketing', 'engineering', 'sales', 'customer_success'].includes(selectedRole)) {
        // For predefined roles, navigate immediately when loading completes
        navigateToAccountCreation();
      } else if (typeof selectedRole === 'string' && selectedRole.trim() && selectedRole !== 'other') {
        // For custom roles, also navigate when loading completes if role is already set
        navigateToAccountCreation();
      }
    }
  }, [fill, selectedRole, isValidSelection]);



  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (autoProgressTimeoutRef.current) {
        clearTimeout(autoProgressTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!url) return;
    let fillInterval = null;
    let afterResponse = false;
    const SLOW_MAX = 60;
    const SLOW_FILL_STEP = 0.5;
    const FAST_FILL_STEP = 1.1; // Moderate speed increase when showing facts
    const SLOW_INTERVAL = 250;
    const FAST_INTERVAL = 150; // Faster interval when showing facts

    // Record start time for processing calculation
    const requestStartTime = Date.now();
    setStartTime(requestStartTime);

    // Start filling
    fillInterval = setInterval(() => {
      setFill(f => {
        // Before response, cap at SLOW_MAX
        if (!afterResponse) {
          if (f < SLOW_MAX) return f + SLOW_FILL_STEP;
          return SLOW_MAX;
        } else {
          // After response, continue to 100%
          if (f < 100) return f + SLOW_FILL_STEP;
          return 100;
        }
      });
    }, SLOW_INTERVAL);

    // Start onboarding
    startOnboarding(url, captchaToken).then(res => {
      setResponse(res);
      // Extract interesting facts
      const factsArr =
        res?.data?.company_analysis?.interesting_facts || [];
      setFacts(factsArr);
      // Start showing facts one by one
      setFactsToShow([]); // reset
      if (factsArr.length > 0) {
        let i = 0;

        // Start typing the first fact immediately
        setCurrentlyTyping(true);
        setFactsToShow([0]);

        // Mark first fact as fully typed after typing duration
        setTimeout(() => {
          setTypedFacts(prev => ({ ...prev, 0: true }));
          setCurrentlyTyping(false);

          // If there are more facts, show typing indicator for next one
          if (factsArr.length > 1) {
            setShowTypingIndicator(true);
          } else {
            setShowTypingIndicator(false);
            setAllFactsDisplayed(true); // Only one fact, so all facts are displayed
          }
        }, 1200); // Longer duration to ensure typing completes

        const showNextFact = () => {
          // Speed up progress while showing fact
          clearInterval(fillInterval);
          fillInterval = setInterval(() => {
            setFill(f => f < 100 ? f + FAST_FILL_STEP : 100);
          }, FAST_INTERVAL);

          // After 1.5 seconds, slow down and prepare for next fact
          setTimeout(() => {
            // Slow down progress
            clearInterval(fillInterval);
            fillInterval = setInterval(() => {
              setFill(f => f < 100 ? f + SLOW_FILL_STEP : 100);
            }, SLOW_INTERVAL);

            // Show next fact after another 2 seconds
            setTimeout(() => {
              i++;
              if (i < factsArr.length) {
                setCurrentlyTyping(true);
                setShowTypingIndicator(false); // Hide while typing
                setFactsToShow(prev => [...prev, i]);

                // Mark this fact as fully typed after typing duration
                setTimeout(() => {
                  setTypedFacts(prev => ({ ...prev, [i]: true }));
                  setCurrentlyTyping(false);

                  // Check if there are more facts coming
                  if (i + 1 < factsArr.length) {
                    setShowTypingIndicator(true); // Show for next fact
                    showNextFact(); // Recursive call for next fact
                  } else {
                    setShowTypingIndicator(false); // No more facts
                    setAllFactsDisplayed(true); // All facts have been displayed
                  }
                }, 1200); // Longer duration to ensure typing completes
              }
            }, 2000);
          }, 1500);
        };

        // Start the cycle for the remaining facts
        if (factsArr.length > 1) {
          setTimeout(() => {
            showNextFact();
          }, 2500); // Wait for first fact to be typed + some delay
        }
      } else {
        setShowTypingIndicator(false);
        setAllFactsDisplayed(true); // No facts, so consider all facts displayed
      }
      // Allow fill to continue to 100%
      afterResponse = true;
    });

    return () => {
      clearInterval(fillInterval);
    };
  }, [url, captchaToken]);

  // Navigate to AccountCreation when auto-progress is triggered
  const navigateToAccountCreation = () => {
    if (
      fill >= 100 &&
      response &&
      response.data.temporary_user.user_id &&
      response.data.kb_id &&
      response.data.chatbot_token &&
      startTime &&
      isValidSelection
    ) {
      // For custom roles, if endTime is not set yet, set it now
      const currentEndTime = endTime || Date.now();

      // Calculate processing time using the time when 100% was reached (not including role selection delay)
      const processingTimeMs = currentEndTime - startTime;
      const processingTimeSeconds = Math.round(processingTimeMs / 1000);

      navigate('/create-account/claim', {
        state: {
          user_id: response.data.temporary_user.user_id,
          kb_id: response.data.kb_id,
          chatbot_token: response.data.chatbot_token,
          role: selectedRole,
          processingTime: processingTimeSeconds,
          url: url
        },
        replace: true,
      });
    }
  };

  return (
    <OnboardingLayout
      containerClassName="min-h-screen overflow-x-hidden onboarding-started-page"
      navClassName="max-w-[75rem] mx-auto  px-[10px] md:px-[20px] lg:px-0 "
    >

   <div className="flex flex-col lg:flex-row lg:justify-center items-center min-h-[100vh] mt-[72px] lg:mt-0">
      <main className="relative z-10 h-full flex items-center justify-center md:px-[40px] px-[20px] w-full py-[20px]  lg:py-[100px]">
      <div className="flex flex-col lg:flex-row lg:justify-center items-center w-full  mx-auto h-[600px] rounded-lg lg:max-w-[75rem] lg:gap-[128px]">
             {/* Mobile/Tablet Progress Section */}
        <aside className="lg:hidden w-full border-[#F0EFF5] py-[20px]">
         <div className="flex flex-col gap-[12px]">
            {/* Progress Bar */}
            <div className="w-full bg-[#F0EFF5] rounded-full h-2">
              <div
                className="bg-[#06BA63] h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${fill}%` }}
              ></div>
            </div>
         </div>
        </aside>
        <div className="flex flex-col gap-[40px] lg:gap-[80px] items-center  pb-[20px] flex-1 lg:max-w-[498px]">
            <OnboardingRole
              selectedRole={selectedRole}
              onSelect={handleRoleSelect}
              onValidationChange={handleValidationChange}
              onAutoProgress={handleAutoProgress}
            /> 
        </div>
          <div className="relative w-full h-[592px] lg:-0 bg-white/90 border-2 border-white backdrop-blur-[100px] rounded-size2 flex-shrink-0 md:flex-shrink-0 lg:flex-1 overflow-y-auto no-scrollbar elevated-shadow">
            <div className="pt-[20px] pl-[24px] pb-[16px] pr-[18px]">
                <DFullLogo />
            </div>
            <div className="flex flex-col gap-[16px] px-[24px] py-[20px] ">
                <p className="text-base leading-[160%] font-[Inter]">This is so exciting! I'm diving deep into your company's website right now...</p>
                <p className="text-base leading-[160%] font-[Inter]">🌐 Your website has amazing content - I'm absorbing everything!</p>
                {/* Interesting facts with typing effects */}
                {factsToShow.length > 0 && factsToShow.map(idx => (
                    <TypingText
                        key={idx}
                        text={facts[idx]}
                        isTyping={!typedFacts[idx]}
                        className="text-base leading-[160%] font-[Inter]"
                        typingSpeed={10}
                    />
                  ))}
                {/* Typing indicator - shows exactly where next fact will appear */}
                {showTypingIndicator && !currentlyTyping && (
                  <p className="text-base leading-[160%] font-[Inter] flex items-center gap-2">
                    <TypingIndicator
                      dotsOnly={true}
                      className="text-gray-500"
                    />
                  </p>
                )}

                {/* Flashing message to prompt role selection */}
                {allFactsDisplayed && fill >= 100 && (!selectedRole || !isValidSelection) && (
                  <p className="text-base leading-[160%] font-[Inter] animate-flash">
                    Your chatbot is ready! Please select your role to automatically continue and try your personalized AI assistant.
                  </p>
                )}
            </div>
        </div>
        </div>


      </main>

   

       <aside className="w-full h-[592px] mt-[200px] md:mt-[200px] webflow-bg lg:hidden" >

      </aside>

      {/* Desktop Progress Section */}
      <aside className="hidden lg:block lg:w-[35vw] lg:mt-0 lg:h-screen lg:absolute right-0 top-0 z-0 lg:bg-transparent" >
        <LoadingFill
          percent={fill}
          className="absolute inset-0 z-0"
        />
      </aside>
      </div>
    </OnboardingLayout>
  );
}
