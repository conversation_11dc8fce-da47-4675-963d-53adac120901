import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import { COMMON_CLASSNAMES, SYSTEM_FONTS } from '@/constants';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Branding from '@/components/Branding';
import convertThemeToCSSVariablesStyle from '@/helpers/convertThemeToCSSVariables';
import Bubble from '@/components/Bubble';
import useDanteApi from '@/hooks/useDanteApi';
import * as companyService from '@/services/company.service';
import {
  fakerInitialMessages,
  fakerShortConversation,
} from '@/helpers/stories/generateChatMessages';
import LayoutWithButtons from '@/layouts/LayoutWithButtons';
import DButton from '@/components/Global/DButton';
import useToast from '@/hooks/useToast';
import useLayoutStore from '@/stores/layout/layoutStore';
import generateGoogleFonts from '@/helpers/generateGoogleFonts';
import * as userService from '@/services/user.service';
import DLoading from '@/components/DLoading';
import ReactRouterPrompt from 'react-router-prompt';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import StyleTag from '@/components/StyleTag';

const BrandingPage = () => {
  const navigate = useNavigate();
  const formData = new FormData();
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);
  const { data, loading: loadingCompany } = useDanteApi(
    companyService.getCompany
  );
  const [activeTheme, setActiveTheme] = useState(0);
  const [pendingChanges, setPendingChanges] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);

  const [tempCustomization, setTempCustomization] = useState(data);
  const [validationErrors, setValidationErrors] = useState({});

  const updateCustomizationData = useCallback(
    (key, value) => {
      setTempCustomization({ ...tempCustomization, [key]: value });
      // Clear validation error when user starts typing
      if (validationErrors[key]) {
        setValidationErrors({ ...validationErrors, [key]: null });
      }
    },
    [tempCustomization, validationErrors]
  );
  const updateCustomizationDataBatch = (data) => {
    setTempCustomization({ ...tempCustomization, ...data });
  };
  const { addSuccessToast, addErrorToast } = useToast();
  const [importFontCss, setImportFontCss] = useState('');

  const validateForm = () => {
    const errors = {};

    // Validate chatbot name - required field
    if (!tempCustomization?.chatbot_name || tempCustomization.chatbot_name.trim() === '') {
      errors.chatbot_name = 'Chatbot name is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSave = async () => {
    // Validate form before saving
    if (!validateForm()) {
      addErrorToast({ message: 'Please fix the validation errors before saving' });
      return;
    }

    if (tempCustomization.embed_logo_url instanceof File) {
      formData.append('file', tempCustomization.embed_logo_url);
      const response = await userService.uploadFile(formData);
      if (response.status === 200) {
        tempCustomization.logo = response?.data?.url;
      }
      formData.delete('file');
    }
    if (tempCustomization.chatbot_icon instanceof File) {
      formData.append('file', tempCustomization.chatbot_icon);
      const response = await userService.uploadFile(formData);
      if (response.status === 200) {
        tempCustomization.icon = response?.data?.url;
      }
      formData.delete('file');
    }

    try {
      setPendingChanges(true);
      const response = await companyService.updateCompany({
        ...tempCustomization,
        website: tempCustomization?.brand_website,
      });

      if (response.status === 200) {
        setTempCustomization({...tempCustomization, ...response.data});
        addSuccessToast({ message: 'Branding updated successfully' });
      }
    } catch (error) {
      console.error(error);
      addErrorToast({ message: 'Failed to save branding. Please try again.' });
    } finally {
      setPendingChanges(false);
    }
  };

  useEffect(() => {
    if (tempCustomization?.font_name) {
      const fontCss = generateGoogleFonts(tempCustomization?.font_name);
      if (fontCss !== importFontCss) {
        const fontLinkId = `preload-font-${tempCustomization.font_name}`;

        setImportFontCss(fontCss);
        if (
          !document.getElementById(fontLinkId) &&
          !SYSTEM_FONTS.includes(tempCustomization.font_name)
        ) {
          const link = document.createElement('link');
          link.id = fontLinkId;
          link.rel = 'stylesheet';
          link.as = 'style';
          link.href = generateGoogleFonts(tempCustomization.font_name);
          document.head.appendChild(link);
        }
      }
    }
  }, [tempCustomization?.font_name]);

  useEffect(() => {
    const newData = {
      ...data,
      brand_website: data?.website,
      embed_logo_url: data?.logo,
      chatbot_icon: data?.icon,
    };
    setTempCustomization(newData);
  }, [data]);

  useEffect(() => {
    setProgressBar([]);
  }, []);

  useEffect(() => {
    if (!tempCustomization || !data) {
      return;
    }

    const compareFields = [
      'website',
      'surface_color',
      'positive_color',
      'negative_color',
      'logo',
      'icon',
      'font_size',
      'font_family',
      'element_color',
      'custom_css',
      'brand_color',
      'alert_color',
      'chatbot_name',
    ];

    const hasUnsavedChanges = compareFields.some(
      (field) => data[field] !== tempCustomization[field]
    );

    setUnsavedChanges(hasUnsavedChanges);
  }, [tempCustomization, data]);

  if (loadingCompany || !tempCustomization?.user_id) {
    return <DLoading show={true} />;
  }

  return (
    <LayoutRightSidebar
      RightSidebar={() => (
        <div className={COMMON_CLASSNAMES.previewBubble}>
         <StyleTag tag=".bubble" tempCustomizationData={tempCustomization} />
          <Bubble
            type="chatbot"
            config={{
              ...tempCustomization,
              home_tab_enabled: activeTheme === 0 ? true : false,
              notification:
                activeTheme >= 6
                  ? {
                      show: true,
                      type:
                        activeTheme === 6
                          ? 'positive'
                          : activeTheme === 7
                          ? 'negative'
                          : 'alert',
                      message: 'This is a test notification',
                    }
                  : undefined,
              initial_messages:
                activeTheme !== 0 && activeTheme !== 3
                  ? fakerInitialMessages
                  : undefined,
              messages:
                activeTheme !== 0 && activeTheme !== 3
                  ? fakerShortConversation
                  : undefined,
              prompt_suggestions:
                activeTheme === 1
                  ? [
                      {
                        content: 'Tell me something about Dante AI',
                        type: 'suggestion',
                      },
                      {
                        content: 'What is the purpose of Dante AI',
                        type: 'suggestion',
                      },
                    ]
                  : undefined,
              suggested_prompts_enabled: activeTheme === 1 ? true : false,
              calendly_integration_enabled: activeTheme === 4 ? true : false,
              public: true,
            }}
            //TODO: add this when voice mode is implemented
            // isVoiceMode={activeTheme === 3 ? true : false}
            isPreviewMode={true}
          />
          <div className="flex gap-size1 items-center justify-center">
            {Array.from({ length: 8 }).map((_, index) => (
              <button
                key={index}
                className={`dbutton rounded-full ${
                  index === activeTheme
                    ? 'bg-black w-3 h-1'
                    : 'bg-grey-50 size-1'
                }`}
                onClick={() => setActiveTheme(index)}
              ></button>
            ))}
          </div>
        </div>
      )}
    >
      {() => (
        <LayoutWithButtons
          footer={
            <div className="flex items-center justify-between">
              <DButton
                variant="grey"
                className="!h-12 w-full md:w-auto md:!min-w-52"
                onClick={() => {
                  navigate('/');
                }}
              >
                Cancel
              </DButton>
              <DButton
                variant="dark"
                className="!h-12 w-full md:w-auto md:!min-w-52"
                onClick={handleSave}
                loading={pendingChanges}
              >
                Save
              </DButton>
            </div>
          }
        >
          <div className="flex flex-col gap-size5">
            <p className="text-2xl font-medium tracking-tight">Branding</p>
            <Branding
              customizationData={tempCustomization}
              updateCustomizationData={updateCustomizationData}
              updateCustomizationDataBatch={updateCustomizationDataBatch}
              validationErrors={validationErrors}
            />
            <ReactRouterPrompt when={unsavedChanges}>
              {({ isActive, onConfirm, onCancel }) => (
                <DConfirmationModal
                  open={isActive}
                  onClose={onCancel}
                  onConfirm={onConfirm}
                  title="Are you sure you want to leave this page?"
                  description="You have unsaved changes. If you leave, you will lose your changes."
                  confirmText="Leave"
                  cancelText="Cancel"
                  variantConfirm="danger"
                />
              )}
            </ReactRouterPrompt>
          </div>
        </LayoutWithButtons>
      )}
    </LayoutRightSidebar>
  );
};

export default BrandingPage;
