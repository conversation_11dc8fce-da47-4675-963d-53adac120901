import { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import BubbleDirectLink from '@/components/BubbleDirectLink';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';

const EmbedPage = () => {
  const [searchParams] = useSearchParams();

  const setSelectedChatbot = useChatbotStore(
    (state) => state.setSelectedChatbot
  );
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const resetSelectedChatbot = useChatbotStore(
    (state) => state.resetSelectedChatbot
  );

  useEffect(() => {
    resetSelectedChatbot();
  }, []);

  useEffect(() => {
    const kb_id = searchParams.get('kb_id') || '';
    const token = searchParams.get('token') || '';
    const bubble_open = searchParams.get('bubbleopen') === 'true';
    const llmModel =
      searchParams.get('model_type') || searchParams.get('modeltype') || '';
    const parentUrl = searchParams.get('parentUrl') || '';

    setSelectedChatbot({ kb_id, token, bubble_open, llmModel, parentUrl });
  }, [searchParams, setSelectedChatbot]);

  // Validate required parameters
  const kb_idValid = selectedChatbot?.kb_id === searchParams.get('kb_id');
  const tokenValid = selectedChatbot?.token === searchParams.get('token');

  if (!kb_idValid || !tokenValid) {
    return null; // Return null if required parameters are invalid
  }

  return (
    <BubbleDirectLink
      config={{ ...selectedChatbot }}
      isPreviewMode={false}
      isInApp={false}
      initialShouldFetchCustomization={true}
    />
  );
};

export default EmbedPage;
