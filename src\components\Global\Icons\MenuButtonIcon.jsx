import React from 'react';
import PropTypes from 'prop-types';

const MenuButtonIcon = ({...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.5 4.875C22.5 4.57663 22.3815 4.29048 22.1705 4.0795C21.9595 3.86853 21.6734 3.75 21.375 3.75H2.625C2.32663 3.75 2.04048 3.86853 1.8295 4.0795C1.61853 4.29048 1.5 4.57663 1.5 4.875C1.5 5.17337 1.61853 5.45952 1.8295 5.6705C2.04048 5.88147 2.32663 6 2.625 6H21.375C21.6734 6 21.9595 5.88147 22.1705 5.6705C22.3815 5.45952 22.5 5.17337 22.5 4.875ZM22.5 12C22.5 11.7016 22.3815 11.4155 22.1705 11.2045C21.9595 10.9935 21.6734 10.875 21.375 10.875H8.625C8.32663 10.875 8.04048 10.9935 7.8295 11.2045C7.61853 11.4155 7.5 11.7016 7.5 12C7.5 12.2984 7.61853 12.5845 7.8295 12.7955C8.04048 13.0065 8.32663 13.125 8.625 13.125H21.375C21.6734 13.125 21.9595 13.0065 22.1705 12.7955C22.3815 12.5845 22.5 12.2984 22.5 12ZM21.375 18C21.6734 18 21.9595 18.1185 22.1705 18.3295C22.3815 18.5405 22.5 18.8266 22.5 19.125C22.5 19.4234 22.3815 19.7095 22.1705 19.9205C21.9595 20.1315 21.6734 20.25 21.375 20.25H17.625C17.3266 20.25 17.0405 20.1315 16.8295 19.9205C16.6185 19.7095 16.5 19.4234 16.5 19.125C16.5 18.8266 16.6185 18.5405 16.8295 18.3295C17.0405 18.1185 17.3266 18 17.625 18H21.375Z"
        fill="currentColor"
      />
    </svg>
  );
};

MenuButtonIcon.propTypes = {
  className: PropTypes.string,
  color: PropTypes.string,
};

export default MenuButtonIcon; 