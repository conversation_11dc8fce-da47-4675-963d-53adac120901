/**
 * Formats credit values to display with appropriate decimal places
 * Removes trailing zeros and formats with locale-specific number formatting
 * 
 * @param {number} credits - The credit value to format
 * @param {string} locale - The locale for number formatting (default: 'en-US')
 * @returns {string} Formatted credit string
 */
export const formatCredits = (credits, locale = 'en-US') => {
  if (credits == null || isNaN(credits)) {
    return '0';
  }
  
  // Round to 2 decimal places and remove trailing zeros
  const rounded = parseFloat(credits.toFixed(2));
  
  // Format with locale-specific number formatting
  return rounded.toLocaleString(locale);
};

/**
 * Calculates and formats remaining credits
 * 
 * @param {number} available - Available credits
 * @param {number} used - Used credits
 * @param {string} locale - The locale for number formatting (default: 'en-US')
 * @returns {string} Formatted remaining credits string
 */
export const formatRemainingCredits = (available, used, locale = 'en-US') => {
  if (available == null || used == null) {
    return '0';
  }
  
  const remaining = available - used;
  return formatCredits(remaining, locale);
};
