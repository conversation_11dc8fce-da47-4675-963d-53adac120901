import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import DProgressBar from "@/components/Global/DProgressBar";
import AddIcon from "@/components/Global/Icons/AddIcon";
import MobileNavListItem from "@/components/Global/Icons/MobileNavListItem";
import MainNav from "@/components/MainNav";
import useLayoutStore from "@/stores/layout/layoutStore";
import { Dialog, DialogPanel } from "@headlessui/react";
import DToastContainer from "@/components/DToast/DToastContainer";
import AnimatedChatButton from "@/components/Global/AnimatedChatButton";
import SlidingChatSidebar from "@/components/Global/SlidingChatSidebar";
import AiChatbotIcon from "@/components/Global/Icons/AiChatbotIcon";
import { useUserStore } from "@/stores/user/userStore";

/**
 * This layout component is used to display the content of the children
 * when the device screen size is smaller than the `sm` breakpoint.
 */
const LayoutMobile = ({ children, title, progressBar }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isChatMinimized, setIsChatMinimized] = useState(false);
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const userData = useUserStore((state) => state.user);
  const navigate = useNavigate();
  const location = useLocation();
  const isCreatePage = location.pathname === "/chatbot/create";

  const handleToggleNav = () => {
    setIsOpen(!isOpen);
  };

  useEffect(() => {
    setSidebarOpen(true);
  }, []);

  return (
    <main className="d-h-screen flex flex-row gap-size5 p-size2">
      <div className="w-full h-full flex flex-col gap-size5">
        <header>
          <div className="flex flex-col gap-size2">
            <div className="flex justify-between items-center relative min-h-[40px]">
              {title ? (
                <h1 className="text-base sm:text-lg md:text-xl font-medium tracking-tight truncate flex-1 min-w-0 pr-size2">{title}</h1>
              ) : (
                <div />
              )}

              <div className="h-[40px] flex gap-size2 flex-shrink-0">
                <div className="flex items-center">
                  <AnimatedChatButton
                    onClick={() => {
                      setIsChatOpen(!isChatOpen);
                      setIsChatMinimized(false);
                    }}
                    isOpen={isChatOpen}
                  />
                </div>
                {!isCreatePage && (
                  <button
                    className="size-10 bg-purple-200 rounded-size0 text-white flex items-center justify-center"
                    onClick={() => navigate("/chatbot/create")}
                  >
                    <AddIcon />
                  </button>
                )}
                <button
                  onClick={handleToggleNav}
                  className="size-10 bg-transparent border border-grey-5 rounded-size0 text-black flex items-center justify-center"
                >
                  <MobileNavListItem />
                </button>
              </div>
            </div>
            {progressBar && progressBar.length > 0 && (
              <div className="w-full">
                <DProgressBar steps={progressBar} />
              </div>
            )}
          </div>
          <DToastContainer />
        </header>
        <div className="flex flex-col grow">{children}</div>
      </div>

      <Dialog open={isOpen} onClose={setIsOpen} className="relative z-10">
        <div className="fixed inset-0 overflow-hidden">
          <DialogPanel
            transition
            className="pointer-events-auto relative w-screen transform transition duration-150 ease-in-out data-[closed]:translate-x-full sm:duration-700 flex items-start bg-white h-full pt-2 pl-size2"
          >
            <MainNav hideCollapsedButton closeMobileNav={handleToggleNav} />
          </DialogPanel>
        </div>
      </Dialog>

      {isChatOpen && !isChatMinimized && (
        <SlidingChatSidebar
          isOpen={isChatOpen}
          onClose={() => setIsChatOpen(false)}
          onMinimize={() => setIsChatMinimized(true)}
        >
          <div className="h-full w-full">
            <iframe src={`https://app.dante-ai.com/embed/?kb_id=ca7ac107-9716-4ef3-a6b5-55ceab49f15a&token=5e5ff150-8321-43cc-b0ed-5f89b44ecf81&modeltype=gpt-4-omnimodel-mini&tabs=false&client_full_name=${userData?.full_name}&client_email=${userData?.email}`} allow="clipboard-write; clipboard-read; ;microphone" width="100%" height="100%" style={{border: 0}}></iframe>
          </div>
        </SlidingChatSidebar>
      )}

      {isChatOpen && isChatMinimized && (
        <div className="fixed bottom-6 right-6 z-50 bg-white shadow-lg rounded-full flex items-center px-4 py-2 border border-grey-10 cursor-pointer min-w-[120px] min-h-[48px]" onClick={() => setIsChatMinimized(false)}>
          <AiChatbotIcon className="w-4 h-4 text-purple-300 mr-2 mt-0.5" />
          <span className="font-medium text-grey-75 text-sm">Chat with us</span>
        </div>
      )}
    </main>
  );
};

export default LayoutMobile;
