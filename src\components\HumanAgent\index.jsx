import React from 'react';
import PropTypes from 'prop-types';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';

/**
 * HumanAgent - A component for displaying human agent messages
 * @param {Object} props - Component props
 * @param {string} props.message - The message content
 * @param {string} props.agent_name - The agent's name (fallback)
 * @param {string} props.role - The agent's role (fallback)
 * @param {string} props.agent_title - The agent's dynamic title (fallback)
 * @param {string} props.className - Additional CSS classes
 */
const HumanAgent = ({
  message,
  agent_name,
  role,
  agent_title,
  className = '',
  images,
  setPreviewImage,
  ...props
}) => {
  // Get current agent data from store for real-time updates
  const liveAgentData = useHumanHandoverStore((state) => state.userOrganization);

  // Use current agent data if available, otherwise fall back to message data
  const displayName = liveAgentData?.name || agent_name;
  const displayRole = liveAgentData?.agent_title || agent_title || role;
  return (
    <div
      className={`
        inline-flex flex-col items-start gap-[2px] px-4 py-2
        font-['Inter',_sans-serif] rounded-2xl border new-border-light
        ${className}
      `}
      {...props}
    >
      {/* Agent info with dot separator */}
      {displayName && <div className="flex items-center gap-1.5 text-xs font-medium text-newGrey ">
        <span className='text-[12px] leading-[160%]'>{displayName}</span>
        {displayRole && <div className="w-[3px] h-[3px] rounded-full bg-newGrey leading-[160%]"></div>}
        <span className='text-[12px] leading-[160%]'>{displayRole}</span>
      </div>}
      
      {/* Message content */}
      <div className="text-base font-normal new-text-dark dark:text-white leading-[160%]">
        {message}
      </div>

      {images && <div className="flex flex-wrap gap-2 cursor-pointer" onClick={() => setPreviewImage(images)}>
        {images.map((image, index) => (
          <img key={index} src={image} alt="Agent message" className="w-16 h-16 rounded-md" />
        ))}
      </div>}
    </div>
  );
};

HumanAgent.propTypes = {
  message: PropTypes.string.isRequired,
  agent_name: PropTypes.string,
  role: PropTypes.string,
  agent_title: PropTypes.string,
  className: PropTypes.string,
  images: PropTypes.array,
  setPreviewImage: PropTypes.func
};

export default HumanAgent; 