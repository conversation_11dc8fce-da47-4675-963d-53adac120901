import React from 'react';
import PropTypes from 'prop-types';

/**
 * UserAction - A button component with the new design system
 * @param {Object} props - Component props
 * @param {string} props.label - Button text
 * @param {function} props.onClick - Click handler
 * @param {boolean} props.isSent - Whether the message has been sent (changes style)
 * @param {React.ReactNode} props.icon - Optional icon to display
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.disabled - Whether the button is disabled
 * @param {string} props.variant - Button variant ('default', 'accent')
 */
const UserAction = ({
  label,
  onClick,
  isSent = false,
  icon,
  className = '',
  disabled = false,
  variant = 'default',
  ...props
}) => {
  // Determine button style based on variant and state
  const getButtonStyle = () => {
    if (isSent) {
      return 'new-bg-accent new-text-light hover:new-bg-accent';
    }
    
    switch (variant) {
      case 'accent':
        return 'new-bg-button new-text-accent hover:bg-[#D2D1E0]';
      case 'default':
      default:
        return 'new-bg-button new-text-dark hover:bg-[#D2D1E0]';
    }
  };

  return (
    <button
      className={`
        new-theme
        inline-flex items-center justify-center gap-2 px-4 py-2
        font-['Inter'] text-base 
        leading-[160%] animate-fadeInUp
        rounded-[16px_16px_4px_16px] transition-colors duration-200
        ${getButtonStyle()}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${className}
      `}
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
      {...props}
    >
      {icon && <span className="flex items-center">{icon}</span>}
      {label}
    </button>
  );
};

UserAction.propTypes = {
  label: PropTypes.string.isRequired,
  onClick: PropTypes.func,
  isSent: PropTypes.bool,
  icon: PropTypes.node,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  variant: PropTypes.oneOf(['default', 'accent'])
};

export default UserAction; 