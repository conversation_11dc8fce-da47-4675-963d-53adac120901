import React from 'react';
import PropTypes from 'prop-types';

const NewHomeIcon = ({ className = '', ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      className={className}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.25 7.5C4.74728 7.5 5.22419 7.30246 5.57583 6.95083C5.92746 6.59919 6.125 6.12228 6.125 5.625C6.125 5.12772 5.92746 4.65081 5.57583 4.29917C5.22419 3.94754 4.74728 3.75 4.25 3.75C3.75272 3.75 3.27581 3.94754 2.92417 4.29917C2.57254 4.65081 2.375 5.12772 2.375 5.625C2.375 6.12228 2.57254 6.59919 2.92417 6.95083C3.27581 7.30246 3.75272 7.5 4.25 7.5ZM9.125 4.5C8.82663 4.5 8.54048 4.61853 8.3295 4.8295C8.11853 5.04048 8 5.32663 8 5.625C8 5.92337 8.11853 6.20952 8.3295 6.4205C8.54048 6.63147 8.82663 6.75 9.125 6.75H21.875C22.1734 6.75 22.4595 6.63147 22.6705 6.4205C22.8815 6.20952 23 5.92337 23 5.625C23 5.32663 22.8815 5.04048 22.6705 4.8295C22.4595 4.61853 22.1734 4.5 21.875 4.5H9.125ZM9.125 17.25C8.82663 17.25 8.54048 17.3685 8.3295 17.5795C8.11853 17.7905 8 18.0766 8 18.375C8 18.6734 8.11853 18.9595 8.3295 19.1705C8.54048 19.3815 8.82663 19.5 9.125 19.5H21.875C22.1734 19.5 22.4595 19.3815 22.6705 19.1705C22.8815 18.9595 23 18.6734 23 18.375C23 18.0766 22.8815 17.7905 22.6705 17.5795C22.4595 17.3685 22.1734 17.25 21.875 17.25H9.125ZM8 12C8 11.7016 8.11853 11.4155 8.3295 11.2045C8.54048 10.9935 8.82663 10.875 9.125 10.875H21.875C22.1734 10.875 22.4595 10.9935 22.6705 11.2045C22.8815 11.4155 23 11.7016 23 12C23 12.2984 22.8815 12.5845 22.6705 12.7955C22.4595 13.0065 22.1734 13.125 21.875 13.125H9.125C8.82663 13.125 8.54048 13.0065 8.3295 12.7955C8.11853 12.5845 8 12.2984 8 12ZM6.125 12C6.125 12.4973 5.92746 12.9742 5.57583 13.3258C5.22419 13.6775 4.74728 13.875 4.25 13.875C3.75272 13.875 3.27581 13.6775 2.92417 13.3258C2.57254 12.9742 2.375 12.4973 2.375 12C2.375 11.5027 2.57254 11.0258 2.92417 10.6742C3.27581 10.3225 3.75272 10.125 4.25 10.125C4.74728 10.125 5.22419 10.3225 5.57583 10.6742C5.92746 11.0258 6.125 11.5027 6.125 12ZM4.25 20.25C4.74728 20.25 5.22419 20.0525 5.57583 19.7008C5.92746 19.3492 6.125 18.8723 6.125 18.375C6.125 17.8777 5.92746 17.4008 5.57583 17.0492C5.22419 16.6975 4.74728 16.5 4.25 16.5C3.75272 16.5 3.27581 16.6975 2.92417 17.0492C2.57254 17.4008 2.375 17.8777 2.375 18.375C2.375 18.8723 2.57254 19.3492 2.92417 19.7008C3.27581 20.0525 3.75272 20.25 4.25 20.25Z"
        fill="currentColor"
      />
    </svg>
  );
};

NewHomeIcon.propTypes = {
  className: PropTypes.string,
};

export default NewHomeIcon;