import React from 'react';
import PropTypes from 'prop-types';

const NewChatIcon = ({ className = '', ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      className={className}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.7645 16.416L7.4735 19.9185C7.46817 19.9796 7.4813 20.0408 7.5112 20.0943C7.54109 20.1479 7.58637 20.1912 7.64117 20.2187C7.69597 20.2461 7.75775 20.2565 7.81853 20.2485C7.8793 20.2404 7.93625 20.2143 7.982 20.1735L11.297 17.2275L12.1805 17.247L12.5 17.25C15.0425 17.25 17.1305 16.59 18.53 15.4845C19.865 14.4315 20.75 12.8445 20.75 10.5C20.75 8.1555 19.865 6.57 18.53 5.5155C17.1305 4.41 15.0425 3.75 12.5 3.75C9.9575 3.75 7.8695 4.41 6.47 5.5155C5.135 6.57 4.25 8.1555 4.25 10.5C4.25 12.972 5.234 14.613 6.722 15.672L7.7645 16.416ZM9.476 21.8535L12.131 19.4955C12.252 19.4985 12.375 19.5 12.5 19.5C18.299 19.5 23 16.5 23 10.5C23 4.5 18.299 1.5 12.5 1.5C6.701 1.5 2 4.5 2 10.5C2 13.6755 3.317 16.011 5.4155 17.505L5.231 19.731C5.18867 20.24 5.29981 20.75 5.55008 21.1953C5.80036 21.6405 6.17829 22.0006 6.63513 22.229C7.09197 22.4574 7.60677 22.5437 8.11313 22.4768C8.61949 22.4099 9.09419 22.1928 9.476 21.8535Z"
        fill="currentColor"
      />
    </svg>
  );
};

NewChatIcon.propTypes = {
  className: PropTypes.string,
};

export default NewChatIcon; 