import React, { Suspense, useEffect, useState } from 'react';
import { Outlet, useParams } from 'react-router-dom';

import useLayoutStore from '@/stores/layout/layoutStore';
import {
  getCurrentUserInfo,
  getOrganizationById,
} from '@/services/human-handover-organization';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';

import DLoading from '@/components/DLoading';
import useDante<PERSON>pi from '@/hooks/useDanteApi';
import {
  getCategoriesByOrganizationId,
  getQuickResponses,
} from '@/services/humanHandover';
import { requestNotificationPermission } from '@/helpers/notificationHelper';

const LiveAgentEntrypoint = () => {
  const { organization_id } = useParams();

  const setIsDarkMode = useLayoutStore((state) => state.setIsDarkMode);
  const {
    setUserOrganization,
    setSelectedOrganization,
    updateCategories,
    updateQuickResponses,
  } = useHumanHandoverStore((state) => state);

  const { data: userOrganization, isLoading: isUserOrganizationLoading } =
    useDanteApi(getCurrentUserInfo, [organization_id], {}, organization_id);
  const { data: organization, isLoading: isOrganizationLoading } = useDanteApi(
    getOrganizationById,
    [organization_id],
    {},
    organization_id
  );
  const { data: categories, isLoading: isCategoriesLoading } = useDanteApi(
    getCategoriesByOrganizationId,
    [organization_id],
    {},
    organization_id
  );

  const { data: quickResponses, isLoading: isQuickResponsesLoading } =
    useDanteApi(getQuickResponses, [organization_id], {}, organization_id);

  useEffect(() => {
    if (userOrganization) {
      setUserOrganization(userOrganization);
    }
  }, [userOrganization]);

  useEffect(() => {
    if (organization) {
      setSelectedOrganization(organization);
    }
  }, [organization]);

  useEffect(() => {
    if (categories) {
      updateCategories(categories.results);
    }
  }, [categories]);

  useEffect(() => {
    if (quickResponses) {
      updateQuickResponses(quickResponses.results);
    }
  }, [quickResponses]);

  useEffect(() => {
    const theme = localStorage.getItem('theme');

    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
      setIsDarkMode(true);
    }
  }, []);

  // Request notification permission when entering human handover section
  useEffect(() => {
    const initializeNotifications = async () => {
      // Only request permission if it hasn't been granted or denied yet
      if (Notification.permission === 'default') {
        try {
          await requestNotificationPermission();
        } catch (error) {
          console.error('Failed to request notification permission:', error);
        }
      }
    };
    
    initializeNotifications();
  }, []);

  if (
    isUserOrganizationLoading ||
    isOrganizationLoading ||
    isQuickResponsesLoading ||
    isCategoriesLoading
  ) {
    return <DLoading show={true} />;
  }

  return (
    <Suspense fallback={<DLoading show={true} />}>
      <Outlet />
    </Suspense>
  );
};

export default LiveAgentEntrypoint;
