const CreditCardIcon = (props) => {
  return (
   <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
<path d="M23.7187 5.39813C23.6859 5.1659 23.5785 4.95064 23.4126 4.78479C23.2468 4.61894 23.0315 4.51151 22.7993 4.47868L22.7839 4.46326H1.05807L1.04274 4.47868C0.810502 4.5115 0.595231 4.61893 0.429383 4.78478C0.263536 4.95062 0.156103 5.1659 0.123285 5.39813L0.10791 5.41346V19.4285L0.123285 19.4439C0.156103 19.6761 0.263536 19.8914 0.429383 20.0573C0.595231 20.2231 0.810502 20.3305 1.04274 20.3634L1.05807 20.3787H22.7838L22.7993 20.3633C23.0315 20.3305 23.2468 20.2231 23.4127 20.0572C23.5785 19.8914 23.6859 19.6761 23.7187 19.4439L23.7341 19.4284V5.41346L23.7187 5.39813Z" fill="#C2CCCE"/>
<path d="M0.10791 7.51294H23.734V11.5625H0.10791V7.51294Z" fill="#597B91"/>
<path d="M20.7411 14.3139H3.25883C2.17527 14.3139 1.29688 15.1923 1.29688 16.2758C1.29688 17.3594 2.17527 18.2378 3.25883 18.2378H20.7412C21.8248 18.2378 22.7031 17.3594 22.7031 16.2758C22.7031 15.1923 21.8247 14.3139 20.7411 14.3139Z" fill="white"/>
<path d="M16.3395 17.9233C16.2866 17.9233 16.2346 17.9222 16.1836 17.92C15.5503 17.8916 15.1413 17.6951 14.877 17.4543C13.9767 17.8936 12.8995 18.0716 12.447 17.6588C12.2761 17.503 12.0498 17.1452 12.4535 16.4997C12.7807 15.9766 12.8564 15.5316 12.642 15.3924C12.3826 15.2239 11.7323 15.3853 11.3039 16.0213C10.9657 16.5235 10.3527 17.0159 9.66434 17.3385C9.3094 17.5049 8.4214 17.8562 7.84198 17.5262C7.54208 17.3554 7.12451 16.8882 7.69798 15.6776C7.76778 15.5302 7.7675 15.4531 7.76192 15.4377C7.65162 15.3091 6.72776 15.3473 5.75126 15.8961C5.12918 16.2457 4.78751 16.6541 4.88079 16.9365C4.90878 17.0247 4.90086 17.1204 4.85877 17.2028C4.81667 17.2852 4.74379 17.3477 4.65593 17.3767C4.56806 17.4057 4.4723 17.399 4.38941 17.3578C4.30651 17.3167 4.24317 17.2446 4.21311 17.1571C4.02247 16.5799 4.34562 15.9761 5.123 15.4569C5.98386 14.8819 7.58248 14.3552 8.2212 14.9067C8.38554 15.0485 8.61861 15.3764 8.33342 15.9785C8.10617 16.4582 8.04851 16.8346 8.18989 16.9151C8.5977 17.1475 10.1097 16.5357 10.7207 15.6285C11.0077 15.2024 11.4048 14.8826 11.839 14.7278C12.2782 14.5712 12.7105 14.5984 13.025 14.8026C13.4786 15.0972 13.7233 15.7956 13.0497 16.8725C12.9492 17.0332 12.934 17.1188 12.932 17.148C13.0869 17.2525 13.7812 17.1866 14.4839 16.865C14.3477 16.4898 14.3476 16.0361 14.4867 15.5989C14.6336 15.1376 14.907 14.7718 15.2182 14.6205C15.7092 14.3818 16.0915 14.7068 16.2698 15.0576C16.606 15.719 16.3546 16.4367 15.5799 17.0265C15.5648 17.0381 15.5495 17.0496 15.5339 17.061C16.4844 17.5698 18.5756 16.7493 19.4761 16.3503C19.5613 16.3125 19.6581 16.3102 19.7451 16.3437C19.8321 16.3773 19.9022 16.4441 19.94 16.5293C19.9777 16.6146 19.9801 16.7113 19.9465 16.7983C19.9129 16.8853 19.8462 16.9554 19.7609 16.9932C18.3668 17.6109 17.217 17.9233 16.3395 17.9233ZM15.5458 15.2449C15.4834 15.2565 15.2926 15.4203 15.1702 15.7716C15.0937 15.9912 15.0559 16.2584 15.1086 16.5011C15.1256 16.4886 15.1425 16.4759 15.1593 16.4631C15.4996 16.2026 15.8597 15.8027 15.643 15.3762C15.5946 15.2812 15.5567 15.2518 15.5458 15.2449Z" fill="#597B91"/>
</svg>

  );
};

export default CreditCardIcon;
