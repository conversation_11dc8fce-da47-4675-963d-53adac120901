import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

const initialState = {
  teamName: '',
  owner: {
    id: null,
  },
  selectedTeam: null,
  userMode: true,
  teams: [],
  refreshTrigger: 0, // Simple counter to trigger refetch
};

const useTeamManagementStore = create(
  persist(
    (set) => ({
      ...initialState,
      setTeamName: (teamName) => set({ teamName }),
      setOwner: (owner) => set({ owner }),
      setTeams: (teams) => set({ teams }),
      setSelectedTeam: (selectedTeam) => set({ selectedTeam }),
      setUserMode: (userMode) => set({ userMode }),
      triggerRefresh: () => set((state) => ({ refreshTrigger: state.refreshTrigger + 1 })),
      reset: () => set({ ...initialState })
    }),
    {
      name: 'team-management',
      storage: createJSONStorage(() => localStorage)
    }
  )
);

export default useTeamManagementStore;
