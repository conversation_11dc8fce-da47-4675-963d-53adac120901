import React from 'react';
import PropTypes from 'prop-types';

/**
 * UserMessage - A component for displaying user messages
 * @param {Object} props - Component props
 * @param {string} props.message - Message text
 * @param {boolean} props.isSent - Whether the message has been sent (changes style)
 * @param {React.ReactNode} props.icon - Optional icon to display
 * @param {string} props.className - Additional CSS classes
 */
const UserMessage = ({
  message,
  isSent = false,
  icon,
  className = '',
  pollResponse = false,
  wasDuringPoll = false,
  ...props
}) => {
  return (
    <div
      className={`
        new-theme
        inline-flex items-center justify-center gap-2 px-4 py-2
        font-['Inter'] text-base font-normal
        rounded-[16px_16px_4px_16px] transition-colors duration-200
        ${isSent 
          ? 'new-bg-accent' 
          : 'new-bg-button new-text-dark hover:bg-[#D2D1E0]'}
        ${className}
      `}
      style={{
        // height: isSent ? '40px' : 'auto',
        flexShrink: isSent ? 0 : 'initial',
        color: isSent ? '#F1F0FF' : undefined
      }}
      {...props}
    >
      {icon && <span className="flex items-center">{icon}</span>}
      {message}
    </div>
  );
};

UserMessage.propTypes = {
  message: PropTypes.string.isRequired,
  isSent: PropTypes.bool,
  icon: PropTypes.node,
  className: PropTypes.string
};

export default UserMessage; 