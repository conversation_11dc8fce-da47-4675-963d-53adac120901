import { useEffect, useState } from 'react';

import DButtonIcon from '@/components/Global/DButtonIcon';
import DIntegrationApp from '@/components/Global/DIntegrationApp';
import SearchIcon from '@/components/Global/Icons/SearchIcon';
import * as integrationService from '@/services/integration.service';
import useDanteApi from '@/hooks/useDanteApi';
import BlurredOverlay from '@/components/BlurredOverlay';
import { checkTeamManagementPermission } from '@/helpers/tier/featureCheck';

const IntegrationApps = ({ row = true, filteredAppSeacrh }) => {
  const { data } = useDanteApi(integrationService.getAllIntegrations, [], {
    skip: !checkTeamManagementPermission('integrations')
  });
  const [showInput, setShowInput] = useState(false);
  const [filteredApps, setFilteredApps] = useState( filteredAppSeacrh || data);

  const searchApps = (search) => {
    setFilteredApps(
      data.filter((app) =>
        app.label.toLowerCase().includes(search.toLowerCase())
      )
    );
  };

  useEffect(() => {
    setFilteredApps(filteredAppSeacrh || data);
  }, [filteredAppSeacrh, data]);

  return (
    <>
      <div
        className={`w-full h-full ${
          !row
            ? 'w-[360px] xl:w-[440px] flex gap-size3 flex-col'
            : 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-size3'
        } overflow-y-auto no-scrollbar`}
      >
        {!row && (
          <div className="flex justify-between items-center">
            <p className="text-xl font-medium tracking-tight">Apps</p>
            <div className="flex items-center">
              <DButtonIcon
                size="lg"
                variant="outlined"
                onClick={() => setShowInput(!showInput)}
                className={`${showInput ? 'rounded-r-none border-r-0' : ''}`}
              >
                <SearchIcon />
              </DButtonIcon>
              <input
                type="text"
                className={`bg-grey-1 h-11 rounded-size0 text-min-safe-input p-2 pl-0 transition-transform duration-300 ease-in-out ${
                  showInput
                    ? 'w-full opacity-100 border-l-0 border rounded-l-none border-grey-10'
                    : 'w-0 opacity-0'
                }`}
                onChange={(e) => searchApps(e.target.value)}
              />
            </div>
          </div>
        )}
        {filteredApps?.map((app) => (
          <BlurredOverlay
            show={
               !checkTeamManagementPermission('integrations')
            }
            height="h-full"
            className="h-full"
          >
            <DIntegrationApp app={app} key={app.label} />
          </BlurredOverlay>
        ))}
      </div>
    </>
  );
};

export default IntegrationApps;
