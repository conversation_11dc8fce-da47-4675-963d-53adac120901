import { useState } from 'react';
import { useConversationStore } from '@/stores/conversation/conversationStore';
import DButton from '../Global/DButton';
import DInput from '../Global/DInput/DInput';
import DModal from '../Global/DModal';
import * as chatService from '@/services/chat.service';
import { useTimezoneStore } from '@/stores/timezone/timezoneStore';

import { useUserStore } from '@/stores/user/userStore';
import validateEmail from '@/helpers/validateEmail';


const DModalEmailTransaction = ({
  open,
  onClose,
  token,
}) => {
  const currectConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const [email, setEmail] = useState("");
  const [validationError, setValidationError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const timezone = useTimezoneStore((state) => state.timezone);
  const auth = useUserStore((state) => state.auth);
  const [loading, setLoading] = useState(false);

  const handleClose = () => {
    setEmail("");
    setValidationError("");
    setSuccessMessage("");
    onClose();
  };

  const handleGetChatTranscript = async () => {
    // Clear any previous validation errors and success messages
    setValidationError("");
    setSuccessMessage("");

    if (!email || !email.trim()) {
      setValidationError("Please enter an email address");
      return;
    }

    if (!validateEmail(email.trim())) {
      setValidationError("Please enter a valid email address");
      return;
    }

    if (currectConversation.id === null) {
      setValidationError("No conversation ID found");
      return;
    }

    setLoading(true);

    try {
      const response = await chatService.sendEmailTranscript(
        { conversation_id:currectConversation.id,
        token: token ?? auth.access_token,
        email: email.trim(),
        timezone: timezone
      });

      if (response.status === 201) {
        setSuccessMessage("Email sent successfully");

        setTimeout(() => {
          handleClose();
        }, 2000);
      } else {
        setValidationError("Failed to send email. Please try again.");
      }
    } catch (error) {
      console.log(error);
      setValidationError(error.response?.data?.message || "Failed to send email. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <DModal
      isOpen={open}
      onClose={handleClose}
      title="Chat transcript"
      footer={
        <DButton
          variant="dark"
          fullWidth
          onClick={handleGetChatTranscript}
          loading={loading}
        >
          Get chat transcript
        </DButton>
      }
    >
      <div className="flex flex-col gap-size1 min-h-[120px]">
        <p className="text-sm font-regular tracking-tight text-grey-50">
          {" "}
          This email is strictly for communication purposes and will not be
          stored, shared, or used for any marketing activities.
        </p>
        <DInput
          value={email}
          onChange={(e) => {
            setEmail(e.target.value);

            if (validationError) {
              setValidationError("");
            }
          }}
          placeholder="Email address"
          error={validationError}
          className={
            successMessage
              ? "border-green-500 focus-within:border-green-600"
              : ""
          }
        />
        {/* Reserve space for messages to prevent layout shift */}
        <div className="min-h-[20px]">
          {successMessage && (
            <div className="text-sm font-regular tracking-tight text-green-600">
              {successMessage}
            </div>
          )}
        </div>
      </div>
    </DModal>
  );
};

export default DModalEmailTransaction;
