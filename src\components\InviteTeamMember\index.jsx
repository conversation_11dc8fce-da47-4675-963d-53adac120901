import React, { useState, useEffect, useCallback, memo, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import DButton from '@/components/Global/DButton';
import UserPlusIcon from '@/components/Global/Icons/UserPlusIcon';
import AddMember from '@/pages/TeamManagement/AddMember';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import { useUserStore } from '@/stores/user/userStore';
import { getTeams, createTeam, getTeamRoles, createTeamMember, getTeamMembers } from '@/services/teamManagement.service';
import useDanteApi from '@/hooks/useDanteApi';
import useToast from '@/hooks/useToast';
import featureCheck from '@/helpers/tier/featureCheck';

const InviteTeamMember = ({ onTeamMemberAdded }) => {
  const navigate = useNavigate();
  const [openAddMember, setOpenAddMember] = useState(false);
  const [loading, setLoading] = useState(false);
  const [teamId, setTeamId] = useState(null);
  const [roles, setRoles] = useState([]);
  const [error, setError] = useState({});
  const [teamOwner, setTeamOwner] = useState(null);
  const [teamsChecked, setTeamsChecked] = useState(false);

  // State for AddMember component
  const [newMember, setNewMember] = useState({
    email: '',
    team_role_id: '',
    all_knowledge_bases: false,
    allowed_kbs: [],
    credits_available: 100, // Default credits for new members
  });

  const { user } = useUserStore();
  // Get team management store state and actions
  const teams = useTeamManagementStore((state) => state.teams);
  const setTeams = useTeamManagementStore((state) => state.setTeams);
  const selectedTeam = useTeamManagementStore((state) => state.selectedTeam);
  const setSelectedTeam = useTeamManagementStore((state) => state.setSelectedTeam);

  const { addSuccessToast, addErrorToast } = useToast();

  // Get refresh functions from store
  const triggerRefresh = useTeamManagementStore((state) => state.triggerRefresh);
  const refreshTrigger = useTeamManagementStore((state) => state.refreshTrigger);

  // Use useDanteApi to get team members
  const { data: teamMembersData } = useDanteApi(
    getTeamMembers,
    [refreshTrigger],
    {},
    teamId
  );

  const teamData = teamMembersData?.results || [];

  // Fetch team data (roles, etc.)
  const fetchTeamData = useCallback(async (id) => {
    if (!id) return;

    try {
      // Fetch roles for the team
      fetchTeamRoles(id);
      // Trigger refresh to get latest members
      if (teamId === id) {
        triggerRefresh();
      }
    } catch (error) {
      console.error('Error fetching team data:', error);
    }
  }, [triggerRefresh, teamId]);

  // Fetch team roles
  const fetchTeamRoles = async (id) => {
    if (!id) return;

    try {
      const response = await getTeamRoles(id);
      if (response.status === 200) {
        setRoles(response.data.results);
      }
    } catch (error) {
      console.error('Error fetching team roles:', error);
    }
  };

  // Check if user owns a team, if not, we'll create one when needed
  useEffect(() => {
    // Prevent multiple checks
    if (teamsChecked || !user?.id) return;

    const checkUserTeams = async () => {
      try {
        const response = await getTeams();
        if (response.status === 200) {
          const teamsData = response.data.results;

          // Only update teams if it changed to avoid loops
          if (!teams || JSON.stringify(teamsData) !== JSON.stringify(teams)) {
            setTeams(teamsData);
          }

          // Find a team where the user is the owner
          const userOwnedTeam = teamsData.find(team => team.owner_id === user?.id);

          if (userOwnedTeam) {
            setTeamId(userOwnedTeam.id);
            setTeamOwner(userOwnedTeam.owner_id);

            // Only set selectedTeam if it's not already set to avoid loops
            // if (!selectedTeam || selectedTeam.id !== userOwnedTeam.id) {
            //   setSelectedTeam(userOwnedTeam);
            // }

            // Fetch team members and roles for the owned team
            fetchTeamData(userOwnedTeam.id);
          }

          setTeamsChecked(true);
        }
      } catch (error) {
        console.error('Error fetching teams:', error);
        setTeamsChecked(true);
      }
    };

    checkUserTeams();
  }, [user, fetchTeamData, selectedTeam, setSelectedTeam, setTeams, teamsChecked, teams]);

  


  // Update teamId when selectedTeam changes (e.g., when team is created from team management page)
  useEffect(() => {
    if (selectedTeam?.id && selectedTeam.owner_id === user?.id && teamId !== selectedTeam.id) {
      setTeamId(selectedTeam.id);
      setTeamOwner(selectedTeam.owner_id);
      fetchTeamData(selectedTeam.id);
    }
  }, [selectedTeam, user?.id, teamId, fetchTeamData]);

  // Create a new team if user doesn't have one
  const createNewTeam = useCallback(async () => {
    if (loading) return null;
    setLoading(true);

    try {
      const teamName = user?.first_name ? `${user.first_name}'s Team` : 'My Team';
      const response = await createTeam(teamName);

      if (response.status === 200) {
        const newTeam = response.data;
        setTeamId(newTeam.id);
        setTeamOwner(newTeam.owner_id);

        // Update teams in store without causing a re-render loop
        const updatedTeams = teams ? [...teams, newTeam] : [newTeam];
        setTeams(updatedTeams);

        //get teams
        const teamsResponse = await getTeams();
        if (teamsResponse.status === 200) {
          setTeams(teamsResponse.data.results);
        }

        setTimeout(() => {
          setSelectedTeam(newTeam);
        }, 0);

        // Fetch roles for the new team
        fetchTeamRoles(newTeam.id);

        addSuccessToast({ message: 'Team created successfully', state: 'positive' });
        fetchTeamData(newTeam.id);

        //open modal
        setOpenAddMember(true);
        return newTeam.id;
      }
    } catch (error) {
      console.error('Error creating team:', error);
      addErrorToast({ message: 'Failed to create team', state: 'negative' });
      return null;
    } finally {
      setLoading(false);
    }
  }, [loading, user, teams, setTeams, setSelectedTeam, addSuccessToast, addErrorToast]);

  const  handleOpenAddMember = useCallback(async () => {
    if (!featureCheck('team_management')) return;

    // Check if we have a selectedTeam but no teamId (sync issue)
    if (!teamId && selectedTeam?.id && selectedTeam.owner_id === user?.id) {
      setTeamId(selectedTeam.id);
      setTeamOwner(selectedTeam.owner_id);
      fetchTeamData(selectedTeam.id);
      setOpenAddMember(true);
      return;
    }

    // If user doesn't own a team, create one
    if (!teamId) {
      const newTeamId = await createNewTeam();
      if (!newTeamId) return; // Exit if team creation failed
    }

    // Trigger refresh when opening modal to get latest data
    triggerRefresh();

    // Open the AddMember modal
    setOpenAddMember(true);
  }, [teamId, selectedTeam, user?.id, createNewTeam, fetchTeamData, triggerRefresh]);

  const handleSubmitInvite = async () => {
    setLoading(true);
    setError({});

    try {
      if (!newMember.email) {
        setError({ email: 'Email is required' });
        setLoading(false);
        return;
      }

      // Use the actual API service to invite a team member
      // Ensure we have a valid teamId (fallback to selectedTeam if needed)
      const currentTeamId = teamId || (selectedTeam?.id && selectedTeam.owner_id === user?.id ? selectedTeam.id : null);

      if (!currentTeamId) {
        setError({ email: 'No team found. Please try again.' });
        setLoading(false);
        return;
      }

      const response = await createTeamMember(currentTeamId, newMember);

      if (response.status === 200) {
        addSuccessToast({ message: 'Invitation sent successfully', state: 'positive' });
        setOpenAddMember(false);

        // Reset form
        setNewMember({
          email: '',
          team_role_id: '',
          all_knowledge_bases: false,
          allowed_kbs: [],
          credits_available: 100,
        });

        // Trigger refresh to update team data
        triggerRefresh();

        // Trigger refresh for team management page if callback is provided
        if (onTeamMemberAdded) {
          onTeamMemberAdded();
        }

        // Redirect to team management page
        navigate(`/team-management/${currentTeamId}`);
      }
    } catch (error) {
      console.error('Error inviting member:', error);

      // // Handle validation errors from backend
      // if (error.response?.data?.errors) {
      //   setError(error.response.data.errors);
      // } else {
      //   addErrorToast({ message: 'Failed to send invitation', state: 'negative' });
      // }
    } finally {
      setLoading(false);
    }
  };

  // Memoize the button component to prevent unnecessary re-renders
  const inviteButton = useMemo(
    () => (
      <DButton
        size="sm"
        onClick={handleOpenAddMember}
        className="!gap-[8px] !p-0"
        disabled={loading}
      >
        <UserPlusIcon className="w-3 h-3 mb-[1px]" />
        <span className="mr-size0">Invite</span>
      </DButton>
    ),
    [loading, handleOpenAddMember]
  );

  return (
    <>
      {inviteButton}

      {/* AddMember Modal */}
      <AddMember
        open={openAddMember}
        onClose={() => {
          setOpenAddMember(false);
          setNewMember({
            email: '',
            team_role_id: '',
            all_knowledge_bases: false,
            allowed_kbs: [],
            credits_available: 100,
          });
          setError({});
          // Trigger refresh when modal closes to get latest data
          triggerRefresh();
        }}
        onSubmit={handleSubmitInvite}
        newMember={newMember}
        setNewMember={setNewMember}
        roles={roles}
        loading={loading}
        error={error}
        teamData={teamData}
        teamOwner={teamOwner}
        teams={teams}
        setOpenEditRole={() => {}}
        setOpenDeleteRole={() => {}}
        setSelectedRole={() => {}}
        setOpenAddRole={() => {}}
      />
    </>
  );
};

// Export as memoized component to prevent unnecessary re-renders
export default memo(InviteTeamMember);
