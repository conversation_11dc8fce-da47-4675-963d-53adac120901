import * as React from 'react';

const PackageIcon = (props) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_448_1401)">
      <path
        d="M22.1472 18.7417L12.8082 23.9251C12.3601 24.1737 11.627 24.1737 11.179 23.9251L1.83997 18.7417C1.61596 18.6174 1.50397 18.4534 1.50397 18.2896H1.40479V6.71042H22.4832V18.2896C22.4832 18.4534 22.3712 18.6174 22.1472 18.7417Z"
        fill="#FF473E"
      />
      <path
        d="M22.4828 6.71043V18.2896C22.4828 18.4535 22.3708 18.6174 22.1468 18.7417L12.8078 23.9251C12.5748 24.0544 12.2648 24.116 11.9578 24.1107C11.9651 18.3125 12.1299 12.507 11.9497 6.71039L22.4828 6.71043Z"
        fill="#FF6E83"
      />
      <path
        d="M11.1789 12.3459L1.83993 7.16249C1.3919 6.91382 1.3919 6.50695 1.83993 6.25828L11.1789 1.07493C11.627 0.82626 12.3601 0.82626 12.8081 1.07493L22.1471 6.25832C22.5951 6.50699 22.5951 6.91387 22.1471 7.16254L12.8081 12.3459C12.3601 12.5946 11.627 12.5946 11.1789 12.3459Z"
        fill="#FFA9BA"
      />
      <path
        d="M18.3325 3.97457C18.332 4.02936 18.3168 4.08302 18.2884 4.12988C18.26 4.17675 18.2195 4.21509 18.1712 4.24087L6.98143 10.2055C6.96602 10.2137 6.94993 10.2206 6.93334 10.226L6.89551 10.2471V21.3786C6.89551 21.4316 6.88167 21.4837 6.85534 21.5298C6.82901 21.5758 6.79112 21.6142 6.74541 21.6411C6.6997 21.6681 6.64775 21.6826 6.5947 21.6832C6.54166 21.6839 6.48936 21.6707 6.44298 21.645L4.56798 20.6043C4.52044 20.5779 4.48083 20.5393 4.45325 20.4925C4.42567 20.4456 4.41113 20.3922 4.41113 20.3379V9.50577C4.41118 9.3199 4.45296 9.13641 4.53338 8.96884C4.61381 8.80127 4.73082 8.65389 4.8758 8.53757L4.88687 8.52894C4.91143 8.50973 4.93473 8.4928 4.95868 8.47663C4.99427 8.45246 5.03107 8.43012 5.06893 8.40969L15.9317 2.61932C15.9766 2.5954 16.0268 2.58309 16.0777 2.58352C16.1285 2.58396 16.1785 2.59712 16.2229 2.6218L18.1757 3.7056C18.2236 3.73219 18.2634 3.77121 18.291 3.81855C18.3186 3.86588 18.333 3.91978 18.3325 3.97457ZM19.3644 8.68696C19.2935 8.6061 19.2123 8.53483 19.123 8.47495C19.1172 8.47096 19.1113 8.46698 19.1044 8.46248C19.0754 8.44357 19.0456 8.42592 19.0151 8.40955L8.15237 2.61927C8.10748 2.59535 8.05732 2.58304 8.00646 2.58348C7.9556 2.58391 7.90565 2.59707 7.86118 2.62176L5.90841 3.70555C5.86048 3.73213 5.82061 3.77115 5.793 3.8185C5.76538 3.86584 5.75106 3.91976 5.75152 3.97456C5.75199 4.02937 5.76723 4.08303 5.79564 4.1299C5.82405 4.17677 5.86458 4.21511 5.91296 4.24087L17.1027 10.2055C17.1183 10.2138 17.1344 10.2206 17.1508 10.2261L17.1886 10.2472V21.3786C17.1886 21.4316 17.2024 21.4838 17.2288 21.5298C17.2551 21.5759 17.293 21.6143 17.3387 21.6412C17.3844 21.6681 17.4364 21.6826 17.4894 21.6833C17.5424 21.684 17.5947 21.6708 17.6411 21.645L19.5161 20.6044C19.5637 20.578 19.6033 20.5394 19.6309 20.4925C19.6584 20.4457 19.673 20.3923 19.673 20.3379V9.50577C19.6728 9.20444 19.5631 8.91343 19.3644 8.68696Z"
        fill="#FFD4DE"
      />
      <path
        d="M15.7314 6.92576C15.7314 7.69034 14.7936 8.24524 13.5016 8.24524C12.9747 8.24524 12.5068 8.15285 12.1374 7.9917C12.0902 7.97121 12.0391 7.96138 11.9877 7.96291C11.9363 7.96444 11.8858 7.9773 11.84 8.00056C11.4549 8.1949 10.9443 8.30815 10.3611 8.30815C9.06913 8.30815 8.13135 7.75324 8.13135 6.98866C8.13135 6.60373 8.3691 6.27199 8.77007 6.03738C8.92804 5.94499 8.99436 5.74779 8.91613 5.58237C8.63033 4.97796 8.63952 4.41091 8.99005 4.06038C9.1716 3.87884 9.42022 3.78288 9.70911 3.78288C9.91208 3.78288 10.1325 3.83084 10.3589 3.92088C10.4459 3.95558 10.5431 3.95467 10.6294 3.91834C10.7158 3.88202 10.7844 3.81319 10.8204 3.72673C11.0531 3.17121 11.4382 2.82907 11.8999 2.82907C12.3538 2.82907 12.7337 3.15959 12.9675 3.69846C13.0452 3.87734 13.2501 3.9627 13.4303 3.88831C13.6669 3.79066 13.8974 3.73845 14.1091 3.73845C14.4741 3.73845 14.7016 3.88934 14.8282 4.01595C15.1728 4.36052 15.1876 4.91426 14.9166 5.50709C14.8405 5.6734 14.9079 5.87018 15.0671 5.96013C15.4835 6.19535 15.7314 6.5328 15.7314 6.92576Z"
        fill="#FF6E83"
      />
    </g>
    <defs>
      <clipPath id="clip0_448_1401">
        <rect width="24" height="24" fill="white" transform="translate(0 0.5)" />
      </clipPath>
    </defs>
  </svg>
);

export default PackageIcon;
