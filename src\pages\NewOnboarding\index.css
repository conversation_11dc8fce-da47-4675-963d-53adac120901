.webflow-bg {
    background: linear-gradient(128deg, #0D0514 -1.61%, #1E0427 32.94%, #290A18 88.82%);
}
.circle-blur {
  z-index: -9998;
  background-color: #6351ff;
  opacity: .4;
  filter: blur(200px);
  border-radius: 1000px;
  width: 100%;
  height: 100%;
  margin-left: auto;
  margin-right: auto;
  position: absolute;
  inset: 0% 0% auto;
  /* Performance optimizations */
  will-change: opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.circle-blur.small {
  width: 500px;
  height: 500px;
  transform: none;
}

.circle-blur.small.align-left {
  width: 800px;
  height: 800px;
  inset: 0% auto 0% 0%;
}

.circle-blur.xsmall {
  width: 400px;
  height: 400px;
  transform: none;
}

.circle-blur.xsmall.align-right {
  filter: blur(100px);
  width: 400px;
  height: 400px;
  inset: auto 0% 0% auto;
}

.circle-blur.xsmall.align-right.color-secondary {
  background-color: #ec4e20;
  inset: auto 0% 0%;
}

.circle-blur.xsmall.align-top-left {
  inset: 0% auto auto 0%;
}

.circle-blur.left {
  background-color: #ffc19c;
  position: fixed;
  top: -30vh;
  right: 30vw;
  transform: translateZ(0);
  /* Performance optimizations */
  will-change: opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

.circle-blur.right {
  left: 50rem;
}

.circle-blur.right.color-secondary {
  background-color: #f59292;
  position: fixed;
  inset: auto 0% 0% auto;
  transform: translateZ(0);
  /* Performance optimizations */
  will-change: opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

.circle-blur.bottom {
  opacity: .2;
  background-color: #279c79;
  border-radius: 500px;
  position: fixed;
  inset: 0% 0% auto;
  transform: translate(0, 500px);
}

.circle-blur.medium {
  background-color: #ec4e20;
  width: 700px;
  height: 700px;
}

/* Fade-in animation for interesting facts */
.animate-fadein {
  animation: fadein 0.7s ease forwards;
}
@keyframes fadein {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: none; }
}

/* Flashing animation for role selection prompt */
.animate-flash {
  animation: flash 1.5s ease-in-out infinite;
}
@keyframes flash {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

/* Purple checkbox styling for onboarding started page only */
.onboarding-started-page [data-headlessui-state="checked"] {
  background-color: #6351FF !important;
}

/* Custom pulse animations for circle backgrounds */
@keyframes pulse-custom {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.2; }
}

@keyframes pulse-custom-delayed {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.2; }
}

.animate-pulse-custom {
  animation: pulse-custom 8s ease-in-out infinite;
}

.animate-pulse-custom-delayed {
  animation: pulse-custom-delayed 8s ease-in-out infinite;
  animation-delay: 1s;
}

/* Reusable box shadow for elevated containers */
.elevated-shadow {
  box-shadow: 0 10px 40px 0 rgba(15, 9, 66, 0.07);
}
