import DToastContainer from '@/components/DToast/DToastContainer';
import DTitle from '@/components/Global/DTitle';
import GlobalModals from '@/components/GlobalModals';
import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';
import useLayoutStore from '@/stores/layout/layoutStore';
import clsx from 'clsx';
import { useEffect } from 'react';

const LayoutMain = ({ children, title, variant = 'default', boxMode = true }) => {
  const isAboveSm = useIsAboveBreakpoint('sm');
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);

  const className = clsx(
    'h-[1px] grow flex flex-col gap-size5 overflow-y-auto scrollbar',
    {
      'bg-white p-size5 rounded-size1': variant === 'default',
      'bg-transparent': variant === 'transparent-minimal',
    }
  );

  useEffect(() => {
    if (!isAboveSm) {
      setLayoutTitle(title);
    } else {
      setLayoutTitle(null);
    }
    setProgressBar([]);
  }, [title, isAboveSm]);

  return (
    <div className={className}>
      {isAboveSm && title && (
        <header className={`flex justify-between items-start ${boxMode ? '3xl:max-w-[1200px] 3xl:mx-auto' : ''} w-full`}>
          <DTitle title={title} className="text-lg md:text-xl w-full" />
        </header>
      )}
      <div className="flex-1 min-h-0 h-full relative">{children}</div>
      <GlobalModals />
    </div>
  );
};

export default LayoutMain;
