import React from 'react';
import HumanAgent from './index';

export default {
  title: 'Components/HumanAgent',
  component: HumanAgent,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
};

export function Default() {
  return (
    <HumanAgent
      agent_name="<PERSON><PERSON>"
      role="Customer Support Specialist"
      message="Hello! How can I help you today with your questions about our product?"
    />
  );
}

export function LongMessage() {
  return (
    <HumanAgent
      agent_name="Alex"
      role="Technical Advisor"
      message="I understand you're having trouble with the integration. Let me walk you through the process step by step. First, make sure you have the latest version installed. Then, check your API credentials to ensure they're correctly configured in the settings panel."
    />
  );
}

export function ShortNameAndRole() {
  return (
    <HumanAgent
      name="Sam"
      role="Agent"
      message="Thanks for reaching out. I'll look into this issue right away."
    />
  );
} 