import { useEffect } from 'react';
import DModalPlans from '../DModalPlans';
import useModalStore from '@/stores/modal/modalStore';

/**
 * GlobalModals component
 * 
 * This component manages application-wide modals that can be triggered from anywhere in the app.
 * It should be included in the main layout component(s) of the application.
 */
const GlobalModals = ({ removeBackdrop = false }) => {
  const { showPlans, closePlansModal, currentFeature, currentFeatureTitle } = useModalStore();

  return (
    <>
      {/* Plans upgrade modal */}
      <DModalPlans
        isOpen={showPlans}
        onClose={closePlansModal}
        highlightedFeature={currentFeature}
        title={currentFeatureTitle}
        subtitle="Your premium feature is one click away. No minimum contract, cancel at anytime."
        removeBackdrop={removeBackdrop}
      />
      
      {/* Additional global modals can be added here */}
    </>
  );
};

export default GlobalModals; 