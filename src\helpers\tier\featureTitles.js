
// Simple key-value mapping of feature names to upgrade modal titles
export const featureTitles = {
  // Voice & Audio Features
  'voice_to_text': 'Upgrade to unlock Voice-to-Text',
  'realtime_voice_access': 'Upgrade to unlock AI Voice Agents',
  'ai_voice_insights': 'Upgrade to unlock Voice Analytics',
  'nr_free_phone_numbers': 'Upgrade to get your free phone number',
  'buy_phone_number': 'Upgrade to purchase phone numbers',
  'nr_voices': 'Upgrade to unlock more voices',
  'nr_ai_voice_agents': 'Upgrade to create more AI Voice Agents',

  // Integrations
  'api_access': 'Upgrade to unlock API Access',
  'whatsapp_integration': 'Upgrade to unlock WhatsApp Integration',
  'messenger_integration': 'Upgrade to unlock Messenger Integration',
  'slack_integration': 'Upgrade to unlock Slack Integration',
  'teams_integration': 'Upgrade to unlock Microsoft Teams Integration',
  'discord_integration': 'Upgrade to unlock Discord Integration',
  'zapier_integration': 'Upgrade to unlock Zapier Integration',
  'google_drive_integration': 'Upgrade to unlock Google Drive Integration',
  'calendly_integration': 'Upgrade to unlock Calendly Integration',
  'intercom_integration': 'Upgrade to unlock Intercom Integration',
  'dropbox_integration': 'Upgrade to unlock Dropbox Integration',

  // Branding & Customization
  'remove_watermark': 'Upgrade to remove Dante AI branding',
  'remove_powered_by_dante': 'Upgrade to remove "Powered by Dante"',
  'custom_css': 'Upgrade to unlock Custom CSS',
  'advanced_chatbot_branding': 'Upgrade to unlock Advanced Branding',

  // Analytics & Data
  'analytics_dashboard': 'Upgrade to unlock Analytics Dashboard',
  'data_collection': 'Upgrade to unlock Data Collection',
  'sources': 'Upgrade to unlock Source Citations',
  'share_conversations': 'Upgrade to unlock Conversation Sharing',

  // Security & Management
  'enhanced_chatbot_security': 'Upgrade to unlock Enhanced Security',
  'rate_limiting': 'Upgrade to unlock Rate Limiting',
  'chatbot_password': 'Upgrade to unlock Password Protection',
  'team_management': 'Upgrade to unlock Team Management',
  'add_custom_roles': 'Upgrade to unlock Custom Roles',

  // AI Models & Features
  'claude_model': 'Upgrade to unlock Claude AI Model',
  'cohere_model': 'Upgrade to unlock Cohere AI Model',
  'user_openai_api_key': 'Upgrade to use your own OpenAI API key',
  'advanced_gpt_models': 'Upgrade to unlock Advanced GPT Models',

  // Live Support
  'live_agent': 'Upgrade to unlock Live Agent Handover',
  'unanswered_question_recognition': 'Upgrade to unlock Smart Question Recognition',

  // Content & Training
  'number_of_knowledgebases': 'Upgrade to create more chatbots',
  'custom_url': 'Upgrade to unlock Custom URLs',
  'auto_refresh_training': 'Upgrade to unlock Auto-Refresh Training',

  // Additional Features
  'disclaimer': 'Upgrade to unlock Disclaimer Settings',
  'wordpress_integration': 'Upgrade to unlock WordPress Integration',
  'add_initial_message': 'Upgrade to unlock Custom Welcome Messages',
  'input_placeholder': 'Upgrade to unlock Custom Input Placeholders',
  'suggested_prompts': 'Upgrade to unlock Suggested Prompts',
  'home_tab': 'Upgrade to unlock Home Tab Customization',
  'show_welcome_message_as_tooltip': 'Upgrade to unlock Welcome Message Tooltip',
  'show_email_details': 'Upgrade to unlock Email Chat Transcript Button',
  'show_microphone': 'Upgrade to unlock Microphone Feature',
  'show_play_button': 'Upgrade to unlock Play Button Feature'
};

/**
 * Get feature-specific title for upgrade modal
 * @param {string} feature - The feature key
 * @returns {string} Feature-specific title or default
 */
export function getFeatureTitle(feature) {
  return featureTitles[feature] ;
}
