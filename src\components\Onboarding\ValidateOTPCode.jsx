import { useEffect, useState } from 'react';
import DButton from '../Global/DButton';
import DInputBlock from '../Global/DInput/DInputBlock';
import OTPInputs from '../OTPInputs';
import LayoutOnBoarding from './LayoutOnBoarding';
import { SignUpStepsEnum } from './signUpSteps';
import * as userService from '@/services/user.service';
import useToast from '@/hooks/useToast';

const ValidateEmail = ({
  setCurrentStep,
  handleChange,
  email,
  code,
  login,
  trial,
}) => {
  const [codeError, setCodeError] = useState('');
  const [pending, setPending] = useState(false);
  const { addSuccessToast } = useToast();

  // Clear error when component mounts or when code is empty (user went back)
  useEffect(() => {
    if (!code || code.length === 0) {
      setCodeError('');
    }
  }, [code]);

  const handleVerifyEmail = async (e) => {
    setCodeError('');
    try {
      setPending(true);
      const response = await userService.verifyEmail(email, code);
      if (response.status === 200) {
        handleChange('', 'code');
        addSuccessToast({ message: response.data.message });
        setCurrentStep &&
          setCurrentStep(
            trial ? SignUpStepsEnum.PASSWORD : SignUpStepsEnum.CHOOSE_TRIAL
          );
        login && login(e);
      }
    } catch (e) {
      if (e.response.status === 404) {
        setCodeError('Invalid code');
      } else if (e.response.status === 400) {
        setCodeError(e.response.data.detail);
      }
    } finally {
      setPending(false);
    }
  };

  const handleResendCode = async () => {
    try {
      const response = await userService.resendCode(email);
      if (response.status === 200) {
        addSuccessToast({ message: response.data.message });
      }
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    return () => {
      setCodeError('');
    };
  }, [code]);

  return (
    <LayoutOnBoarding
      title="Check your email"
      subtitle="Enter verification code"
    >
      <div className="flex flex-col gap-size5 items-center w-full">
        <div className="flex flex-col gap-size3 w-full">
          <DInputBlock label="Verification code" name="email" hiddenLabel>
            <OTPInputs
              onChange={(code) => {
                handleChange(code, 'code');
                // Clear error when user starts typing
                if (codeError) {
                  setCodeError('');
                }
              }}
              error={codeError}
              handleSubmit={handleVerifyEmail}
            />
          </DInputBlock>
          <p
            className="text-grey-50 text-sm tracking-tight cursor-pointer"
            onClick={handleResendCode}
          >
            Resend the code
          </p>
          <div className="flex gap-size3 w-full">
            <DButton
              variant="grey"
              size="lg"
              className="w-max whitespace-nowrap"
              onClick={() => setCurrentStep(SignUpStepsEnum.EMAIL)}
              name="go-back-button"
            >
              Go Back
            </DButton>
            <DButton
              type="submit"
              variant="dark"
              size="lg"
              fullWidth
              onClick={handleVerifyEmail}
              loading={pending}
              name="verify-otp-button"
            >
              Continue
            </DButton>
          </div>
        </div>
      </div>
    </LayoutOnBoarding>
  );
};

export default ValidateEmail;
