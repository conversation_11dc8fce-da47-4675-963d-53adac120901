import * as authService from '@/services/auth.service';

const resetPasswordUseCase = async ({ password, token }) => {
  try {
    const response = await authService.resetPassword({
      password,
      token
    });

    return response;
  } catch (error) {
    console.log(error);
    // Re-throw the error so it can be properly handled by the component
    throw error;
  }
};

export default resetPasswordUseCase;
