import { useEffect, useRef, useState } from 'react';
import DInput from '@/components/Global/DInput/DInput';
import clsx from 'clsx';

const OTPInputs = ({ length = 6, onChange, error, handleSubmit, newDesign = false }) => {
  const [otpCode, setOtpCode] = useState(Array(length).fill(''));
  const inputRef = useRef(Array(length).fill(null));

  // Handle input changes
  const handleOtpChange = (e) => {
    const { name, value } = e.target;
    const index = parseInt(name.split('_')[2], 10);

    if (/^\d$/.test(value)) {
      const newOtpCode = [...otpCode];
      newOtpCode[index] = value;
      setOtpCode(newOtpCode);

      // Move focus to next input if not the last one
      if (value && index < length - 1) {
        inputRef.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (e, index) => {
    const { key } = e;

    if ((e.ctrlKey || e.metaKey) && key === 'v') {
      return;
    }

    // Handle Enter key to submit the form
    if (key === 'Enter' && handleSubmit) {
      e.preventDefault();
      handleSubmit(e);
      return;
    }

    if (
      !/^\d$/.test(key) &&
      !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab', 'Enter'].includes(key)
    ) {
      e.preventDefault();
    }

    if (key === 'Backspace' || key === 'Delete') {
      e.preventDefault();
      const newOtpCode = [...otpCode];
      if (newOtpCode[index]) {
        newOtpCode[index] = '';
        setOtpCode(newOtpCode);
      } else if (index > 0) {
        inputRef.current[index - 1]?.focus();
        const prevCode = [...newOtpCode];
        prevCode[index - 1] = '';
        setOtpCode(prevCode);
      }
    }

    if (key === 'ArrowLeft' && index > 0) {
      inputRef.current[index - 1]?.focus();
    }

    if (key === 'ArrowRight' && index < length - 1) {
      inputRef.current[index + 1]?.focus();
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData('Text').replace(/\D/g, '');

    const newOtpCode = [...otpCode];
    for (let i = 0; i < length && i < pasteData.length; i++) {
      newOtpCode[i] = pasteData[i];
    }
    setOtpCode(newOtpCode);

    const firstEmptyIndex = newOtpCode.findIndex((code) => code === '');
    if (firstEmptyIndex !== -1) {
      inputRef.current[firstEmptyIndex]?.focus();
    } else {
      inputRef.current[length - 1]?.focus();
    }
  };

  // Update parent with the current OTP code
  useEffect(() => {
    onChange(otpCode.join(''));
  }, [otpCode]);

  return (
    <div>
      <div className={clsx('flex', newDesign ? 'gap-2.5' : 'gap-size3')}>
        {Array.from({ length }).map((_, index) => (
          <DInput
            key={index}
            ref={(ref) => (inputRef.current[index] = ref)}
            maxLength="1"
            name={`otp_code_${index}`}
            id={`otp_code_${index}`}
            value={otpCode[index]}
            onChange={handleOtpChange}
            onPaste={index === 0 ? handlePaste : undefined}
            autoComplete="one-time-code"
            onKeyDown={(e) => handleKeyDown(e, index)}
            className={clsx(
              newDesign
                ? `flex !h-[56px] items-center gap-2.5 self-stretch rounded-lg border ${error ? 'border-[#DF0F32]' : 'border-[#D1CFE2]'} bg-white w-full text-center text-lg font-semibold px-[10px] py-[16px]`
                : 'w-12 text-center h-12 px-4 py-2.5',
              error && !newDesign && 'border-[#DF0F32]'
            )}
            inputClassName={clsx(error && 'text-negative-200')}
            hiddenError
          />
        ))}
      </div>

      <p className="text-[#DF0F32] text-sm font-[Inter] h-4 mt-2">{error}</p>
    </div>
  );
};

export default OTPInputs;
