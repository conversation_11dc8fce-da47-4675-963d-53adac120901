import NewDBubbleMessage from '@/components/Global/DBubbleMessage/NewDBubbleMessage';
import React, { useState, useEffect } from 'react';
import UserAction from '@/components/UserAction';
import SourcesPopup from '@/components/SourcesPopup';
import generateMarkdownBubble from '@/helpers/generateBubbleMessages';
import { DateTime } from 'luxon';
import UserIcon from '@/components/Global/Icons/NewUserIcon';
import CalendarIcon from '@/components/Global/Icons/NewCalendarIcon';

/**
 * NewChatListMessages component - A new implementation of the ChatListMessages component
 * @param {Object} props
 * @param {Array} props.messages - Array of chat messages
 * @param {Function} props.onSuggestionClick - Callback for suggestion clicks
 * @param {Function} props.transformLinkUri - Function to transform links in messages
 * @param {boolean} props.readonly - Whether the chat is readonly
 * @param {boolean} props.hideFooter - Whether to hide the footer
 * @param {string} props.chatbot_profile_pic - URL of the chatbot profile picture
 * @param {boolean} props.isInApp - Whether the chat is in the app
 * @param {Function} props.handleFooterButton - Function to handle footer button click
 * @param {boolean} props.isDanteFaq - Whether this is the Dante FAQ
 * @param {boolean} props.showDate - Whether to show the date
 * @param {Array} props.sources - Sources to show
 * @param {boolean} props.openSources - Whether sources are open
 * @param {Function} props.setOpenSources - Function to set openSources
 * @param {boolean} props.showSources - Whether to show sources
 * @param {boolean} props.sourcesLoading - Whether sources are loading
 * @param {Object} props.chatImageLoader - Chat image loader
 * @param {boolean} props.isPreviewMode - Whether the chat is in preview mode
 * @param {boolean} props.showSuggestionPrompts - Whether to show suggestion prompts
 * @param {Object} props.config - Chat configuration
 * @param {Object} props.dynamicButtons - Dynamic button configurations
 * @param {Function} props.handleOpenLiveAgent - Function to handle opening live agent
 * @param {boolean} props.isPreviousConversationHasLiveAgent - Whether previous conversation has live agent
 * @param {boolean} props.isAnswerLoading - Whether an answer is loading
 * @param {boolean} props.isLoadingSuggestions - Whether suggestions are currently loading
 * @returns {JSX.Element}
 */
const NewChatListMessages = ({ 
  messages = [], 
  onSuggestionClick,
  transformLinkUri,
  chatbot_profile_pic,
  isInApp = false,
  handleFooterButton = () => {},
  isDanteFaq = false,
  showDate = false,
  sources = [],
  openSources = false,
  setOpenSources = () => {},
  chatImageLoader,
  isPreviewMode = false,
  showSuggestionPrompts = false,
  config = {},
  dynamicButtons = {},
  handleOpenLiveAgent = () => {},
  isPreviousConversationHasLiveAgent = false,
  isAnswerLoading = false,
  isLoadingSuggestions = false,
  interactingWithLiveAgent = false,
  isInHumanHandoverApp,
  pollResponse,
  showConsent = false,
  setShowConsent = () => {},
  handleSend = () => {},
  showButtons,
  messagesEndRef,
  isTyping = false,
  typingUser = null,
}) => {
  const [specialButtonsVisible, setSpecialButtonsVisible] = useState(true);
  const [clickedActionId, setClickedActionId] = useState(null);
  const [clickedLiveAgent, setClickedLiveAgent] = useState(false);
  const [clickedCalendly, setClickedCalendly] = useState(false);

  // Effect to handle animation when new messages arrive
  useEffect(() => {
    // Check if we have a new message by comparing the last message ID
    if (messages.length > 0 && messages[messages.length - 1]?.role === 'assistant') {
      // Only show suggestions if the last message is from assistant
      setTimeout(() => {
        setSpecialButtonsVisible(true);
        setClickedActionId(null);
        setClickedLiveAgent(false);
        setClickedCalendly(false);
      }, 500);
    }
  }, [messages]);

  const handleActionClick = (action) => {
    console.log('action', action);
    // Set which action was clicked for animation
    setClickedActionId(action.id);
    
    setSpecialButtonsVisible(false);
    
    // Call the callback after a short delay
    setTimeout(() => {
      if (onSuggestionClick) {
        onSuggestionClick(action);
      } else {
        console.log('Action clicked:', action);
      }
    }, 100);
  };

  const handleLiveAgentClick = () => {
    setClickedLiveAgent(true);
    setSpecialButtonsVisible(false);
    setTimeout(() => {
      handleOpenLiveAgent();
    }, 0);
  };

  const handleCalendlyClick = () => {
    setClickedCalendly(true);
    
    // Open Calendly URL
    window.open(config?.calendly_url, '_blank');
  };

  // Check if live agent is available based on schedule
  const isLiveAgentAvailable = () => {
    if (!config?.talk_to_live_agent) {
      return false;
    }

    if (config?.live_agent_always_active) {
      return true;
    }

    const schedule = config?.live_agent_list_of_schedules || [];
    
    try {
      // Get the configured timezone or default to UTC
      const timezone = config?.human_handover_timezone || Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone || 'Europe/Belgrade';
      
      // Get current date/time in the configured timezone using Luxon
      const now = DateTime.now().setZone(timezone);
      const currentDay = now.weekdayLong; // Returns full weekday name
      const currentTime = now.toFormat('HH:mm'); // 24-hour format

      const todaySchedule = schedule.find((s) => s.day === currentDay);

      if (todaySchedule && dynamicButtons?.show_live_agent) {
        // Convert schedule times to DateTime objects in the configured timezone for comparison
        const scheduleStart = DateTime.fromFormat(todaySchedule.from, 'HH:mm', { zone: timezone });
        const scheduleEnd = DateTime.fromFormat(todaySchedule.to, 'HH:mm', { zone: timezone });
        const currentDateTime = DateTime.fromFormat(currentTime, 'HH:mm', { zone: timezone });

        return currentDateTime >= scheduleStart && currentDateTime <= scheduleEnd;
      }
    } catch (error) {
      console.error('Error checking live agent availability:', error);
    }

    return false;
  };

  // Check if we should show special buttons
  const showLiveAgentButton = dynamicButtons?.show_live_agent && 
                              isLiveAgentAvailable() && 
                              !isPreviousConversationHasLiveAgent && 
                              !isAnswerLoading;
                              
  const showCalendlyButton = dynamicButtons?.show_calendly_url && !isAnswerLoading && !showConsent;

  // Determine if we should show the suggestion prompts section
  const shouldShowSuggestions = config?.suggested_prompts_enabled && 
                               config?.prompt_suggestions?.length > 0 && 
                               showSuggestionPrompts && 
                               !isAnswerLoading && 
                               !isLoadingSuggestions &&
                               !interactingWithLiveAgent && 
                               !clickedLiveAgent &&  
                               !pollResponse && 
                               !isInApp &&
                               !isInHumanHandoverApp ; 

  return (
    <div className="flex flex-col w-full grow transition duration-300 no-scrollbar overflow-y-auto">
      {/* Message list */}
      <div className="flex flex-col no-scrollbar">
        {messages.length > 0 ? (
          <>
            {messages.map((message, index) => {
              const isHandoverMessage = message.type === 'human_handover_message';
              const prevMessage = index > 0 ? messages[index - 1] : null;
              const isPrevHandoverMessage = prevMessage?.type === 'human_handover_message';
              
              // Check if this is a user message sent during live agent
              const isUserMessageDuringLiveAgent = message.role === 'user' && message.sentDuringLiveAgent;
              const isPrevMessageFromAssistant = prevMessage?.role === 'assistant';
              
              let marginClass = "mt-4";
              
              if (isHandoverMessage) {
                if (index === 0 || !isPrevHandoverMessage) {
                  marginClass = "mt-4";
                } else {
                  marginClass = "mt-1";
                }
              } else if (isUserMessageDuringLiveAgent) {
                // First user message after assistant gets mt-4, subsequent ones get mt-1
                marginClass = isPrevHandoverMessage ? "mt-4" : "mt-1";
              }
              
              // No top margin for the first message
              if (index === 0) marginClass = "";
              
              return (
                <div key={message.id || index} className={marginClass}>
                  <NewDBubbleMessage
                    message={message}
                    chatbot_profile_pic={chatbot_profile_pic}
                    agent_profile_pic={message.agent_profile_pic}
                    agent_name={message.agent_name}
                    isInApp={isInApp}
                    isDanteFaq={isDanteFaq}
                    was_answered={message.was_answered}
                    completed_date={
                      showDate && message.date_created &&
                      DateTime.fromISO(message.date_created).setLocale('en-US')
                    }
                    type={message.type}
                    interactingWithLiveAgent={interactingWithLiveAgent}
                    pollResponse={pollResponse}
                    status={message.status}
                    isInHumanHandoverApp={isInHumanHandoverApp}
                  >
                    {message.status !== 'loading' &&
                      message.content !== '' &&
                      generateMarkdownBubble(transformLinkUri, message, config?.kb_id)}
                  </NewDBubbleMessage>
                </div>
              );
            })}
          </>
        ) : (
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500">No messages yet</p>
          </div>
        )}

        {/* Typing indicator for human handover */}
        {isTyping && (
          <div className="flex justify-start mb-2">
            <div className="max-w-[80%]">
              <div className="flex items-center gap-2 ">
                <div className="flex items-center gap-1">
                  <div
                    className="w-[5px] h-[5px] bg-gray-500 rounded-full animate-bounce"
                    style={{ animationDelay: '0ms' }}
                  ></div>
                  <div
                    className="w-[5px] h-[5px] bg-gray-500 rounded-full animate-bounce"
                    style={{ animationDelay: '200ms' }}
                  ></div>
                  <div
                    className="w-[5px] h-[5px] bg-gray-500 rounded-full animate-bounce"
                    style={{ animationDelay: '400ms' }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Suggestion prompts section - now outside the message loop */}
      {shouldShowSuggestions && (
        <div className="flex flex-col gap-2 items-end my-4">
          {/* First row: Suggestion prompts */}
          {!showConsent && <div className="flex flex-wrap gap-2 justify-end">
            {config?.prompt_suggestions?.length > 0 && showButtons ? ( config.prompt_suggestions)?.map((action, idx) => {
              return (
                <UserAction
                  key={action.id || idx}
                  label={action.content || action.label}
                  onClick={() => handleActionClick(action)}
                  isSent={clickedActionId === (action.id || idx)}
                  disabled={!!clickedActionId || isPreviewMode || clickedLiveAgent}
                  variant="default"
                />
              );
            }) : ''}
          </div>}
          
          {/* Second row: Special buttons - now using specialButtonsVisible */}
          {(showLiveAgentButton || showCalendlyButton) && !isInHumanHandoverApp && !showConsent && showButtons && (
            <div className="flex flex-wrap gap-2 justify-end">
              {showLiveAgentButton && (
                <UserAction
                  label="Chat with agent"
                  icon={<UserIcon className="w-4 h-4 new-text-accent" />}
                  onClick={handleLiveAgentClick}
                  isSent={clickedLiveAgent}
                  disabled={isPreviewMode || clickedLiveAgent}
                  variant="accent"
                />
              )}
              
              {showCalendlyButton && (
                <UserAction
                  label={config?.calendly_btn_text || "Book a demo"}
                  icon={<CalendarIcon className="w-4 h-4 new-text-accent" />}
                  onClick={handleCalendlyClick}
                  disabled={isPreviewMode || clickedLiveAgent}
                  variant="accent"
                />
              )}
            </div>
          )}
          {showConsent && !isInHumanHandoverApp && (
            <div className="flex flex-wrap gap-2 justify-end">
              <UserAction 
                label="Yes" 
                onClick={() => {
                  setShowConsent(false);
                  handleSend('yes');
                }} 
                variant="accent" 
              />
              <UserAction 
                label="No" 
                onClick={() => {
                  setShowConsent(false);
                  handleSend('no');
                }} 
                variant="accent" 
              />
            </div>
          )}
        </div>
      )}
      
      <SourcesPopup
        open={openSources}
        onClose={() => setOpenSources(false)}
        data={sources}
      />
    </div>
  );
};

export default NewChatListMessages; 